package com.datalink.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量预测请求DTO
 */
@Data
public class TblForecastBatchRequestDTO {
    
    @NotEmpty(message = "预测数据不能为空")
    @Valid
    @JsonProperty("DATA")
    private List<TblForecastItemDTO> data;
}
