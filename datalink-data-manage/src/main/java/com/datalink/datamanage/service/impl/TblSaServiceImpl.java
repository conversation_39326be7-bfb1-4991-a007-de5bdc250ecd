package com.datalink.datamanage.service.impl;

import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblSa;
import com.datalink.datamanage.domain.TblSaItem;
import com.datalink.datamanage.domain.TblSaScheduleLine;
import com.datalink.datamanage.mapper.TblSaMapper;
import com.datalink.datamanage.service.ITblSaService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 协议Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@Service
public class TblSaServiceImpl implements ITblSaService
{
    @Autowired
    private TblSaMapper tblSaMapper;

    /**
     * 查询协议
     *
     * @param saId 协议ID
     * @return 协议
     */
    @Override
    public TblSa selectTblSaById(Long saId)
    {
        return tblSaMapper.selectTblSaById(saId);
    }

    /**
     * 查询协议列表
     *
     * @param tblSa 协议
     * @return 协议
     */
    @Override
    public List<TblSa> selectTblSaList(TblSa tblSa)
    {
        return tblSaMapper.selectTblSaList(tblSa);
    }

    /**
     * 新增协议
     *
     * @param tblSa 协议
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblSa(TblSa tblSa)
    {
        tblSa.setCreateTime(DateUtils.getNowDate());
        int rows = tblSaMapper.insertTblSa(tblSa);
        insertTblSaItem(tblSa);
        return rows;
    }

    /**
     * 修改协议
     *
     * @param tblSa 协议
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblSa(TblSa tblSa)
    {
        tblSa.setUpdateTime(DateUtils.getNowDate());
        tblSaMapper.deleteTblSaItemBySaId(tblSa.getSaId());
        insertTblSaItem(tblSa);
        return tblSaMapper.updateTblSa(tblSa);
    }

    /**
     * 批量删除协议
     *
     * @param saIds 需要删除的协议ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblSaByIds(Long[] saIds)
    {
        tblSaMapper.deleteTblSaItemBySaIds(saIds);
        return tblSaMapper.deleteTblSaByIds(saIds);
    }

    /**
     * 删除协议信息
     *
     * @param saId 协议ID
     * @return 结果
     */
    @Override
    public int deleteTblSaById(Long saId)
    {
        tblSaMapper.deleteTblSaItemBySaId(saId);
        return tblSaMapper.deleteTblSaById(saId);
    }

    @Override
    public List<TblSa> selectTblSaFullList(TblSa tblSa) {
        return tblSaMapper.selectTblSaFullList(tblSa);
    }

    @Override
    public Long selectLastId() {
        return tblSaMapper.selectLastId();
    }

    @Override
    public int updateTblSaOnly(TblSa sa) {
        sa.setUpdateTime(new Date());
        return tblSaMapper.updateTblSa(sa);
    }

    @Override
    public List<TblSa> selectTblSaWithItemList(TblSa tblSa) {
        return tblSaMapper.selectTblSaWithItemList(tblSa);
    }

    /**
     * 新增协议行项目信息
     *
     * @param tblSa 协议对象
     */
    public void insertTblSaItem(TblSa tblSa)
    {
        List<TblSaItem> tblSaItemList = tblSa.getDetail();
        Long saId = tblSa.getSaId();
        if (StringUtils.isNotNull(tblSaItemList))
        {
            List<TblSaItem> list = new ArrayList<TblSaItem>();
            for (TblSaItem tblSaItem : tblSaItemList)
            {
                tblSaItem.setSaId(saId);
                list.add(tblSaItem);
            }
            if (!list.isEmpty())
            {
                tblSaMapper.batchTblSaItem(list);
                List<TblSaScheduleLine> tblSaScheduleLineList = Lists.newArrayList();
                for (TblSaItem item: list){
                    for (TblSaScheduleLine tblSaScheduleLine : item.getScheduleLines()){
                        tblSaScheduleLine.setItemId(item.getItemId());
                        tblSaScheduleLineList.add(tblSaScheduleLine);
                    }
                }
                if (!tblSaScheduleLineList.isEmpty()){
                    tblSaMapper.batchTblSaScheduleLine(tblSaScheduleLineList);
                }
            }
        }
    }
}
