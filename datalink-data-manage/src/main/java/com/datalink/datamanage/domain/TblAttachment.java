package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 附件对象 tbl_attachment
 *
 * <AUTHOR>
 * @date 2023-01-24
 */
public class TblAttachment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 附件ID */
    private Long attachmentId;

    /** 附件名 */
    @Excel(name = "附件名")
    private String name;

    /** 地址 */
    @Excel(name = "地址")
    private String url;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 从属ID */
    @Excel(name = "从属ID")
    private Long parent;

    public void setAttachmentId(Long attachmentId)
    {
        this.attachmentId = attachmentId;
    }

    public Long getAttachmentId()
    {
        return attachmentId;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setUrl(String url)
    {
        this.url = url;
    }

    public String getUrl()
    {
        return url;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setParent(Long parent)
    {
        this.parent = parent;
    }

    public Long getParent()
    {
        return parent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attachmentId", getAttachmentId())
            .append("name", getName())
            .append("url", getUrl())
            .append("type", getType())
            .append("parent", getParent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
