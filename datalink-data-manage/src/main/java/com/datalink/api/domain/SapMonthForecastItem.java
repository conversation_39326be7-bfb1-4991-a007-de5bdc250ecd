package com.datalink.api.domain;

import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class SapMonthForecastItem extends BaseEntity {
    private Long id;
    private Long forecastId;
    private String matnr;
    private String plwrk;
    private String zmethd;
    private String wanst;
    private String dispo;
    private String beskz;
    private BigDecimal bstrf;
    private String meins;
    private String zmonth;
    private BigDecimal gsmng1;
    private BigDecimal gsmng2;
    private BigDecimal gsmng3;
    private BigDecimal gsmngT;
    private String seiku;
    private String jyoken;
    private String kansan;
    private String ryosanDankiri;
    private String ryosanDankiriM;
    private String ryosanDankiriYm;
} 