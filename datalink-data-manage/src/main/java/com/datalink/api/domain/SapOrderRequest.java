package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * SAP发送订单请求DTO
 */
@Setter
@Getter
public class SapOrderRequest {

    /** 采购订单号 */
    @JsonProperty("EBELN")
    @NotEmpty(message = "采购订单号不能为空")
    private String EBELN;

    /** 公司代码 */
    @JsonProperty("BUKRS")
    @NotEmpty(message = "公司代码不能为空")
    private String BUKRS;

    /** 采购凭证类型 */
    @JsonProperty("BSART")
    private String BSART;

    /** 删除标识 */
    @JsonProperty("LOEKZ")
    private String LOEKZ;

    /** 创建人 */
    @JsonProperty("ERNAM")
    private String ERNAM;

    /** 供应商编号 */
    @JsonProperty("LIFNR")
    @NotEmpty(message = "供应商编号不能为空")
    private String LIFNR;

    /** 货币 */
    @JsonProperty("WAERS")
    private String WAERS;

    /** 凭证日期 */
    @JsonProperty("BEDAT")
    private String BEDAT;

    /** 订单行项目 */
    @JsonProperty("EKPO")
    @Valid
    @NotNull(message = "订单行项目不能为空")
    private List<SapEkpoItem> EKPO;

    @Override
    public String toString() {
        return "SapOrderRequest{" +
                "EBELN='" + EBELN + '\'' +
                ", BUKRS='" + BUKRS + '\'' +
                ", BSART='" + BSART + '\'' +
                ", LOEKZ='" + LOEKZ + '\'' +
                ", ERNAM='" + ERNAM + '\'' +
                ", LIFNR='" + LIFNR + '\'' +
                ", WAERS='" + WAERS + '\'' +
                ", BEDAT='" + BEDAT + '\'' +
                ", EKPO=" + EKPO +
                '}';
    }
}
