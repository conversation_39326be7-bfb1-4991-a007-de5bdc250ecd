package com.datalink.datamanage.service;

import java.util.List;
import com.datalink.datamanage.domain.TransportPlan;

/**
 * 货量提示Service接口
 * 
 * <AUTHOR>
 */
public interface ITransportPlanService 
{
    /**
     * 查询货量提示
     *
     * @param transportPlanId 货量提示ID
     * @param tz
     * @return 货量提示
     */
    public TransportPlan selectTransportPlanById(Long transportPlanId, String tz);

    /**
     * 查询货量提示列表
     *
     * @param transportPlan 货量提示
     * @param tz
     * @return 货量提示集合
     */
    public List<TransportPlan> selectTransportPlanList(TransportPlan transportPlan, String tz);

    /**
     * 新增货量提示
     *
     * @param transportPlan 货量提示
     * @param tz
     * @return 结果
     */
    public int insertTransportPlan(TransportPlan transportPlan, String tz);

    /**
     * 修改货量提示
     *
     * @param transportPlan 货量提示
     * @param tz
     * @return 结果
     */
    public int updateTransportPlan(TransportPlan transportPlan, String tz);

    /**
     * 批量删除货量提示
     * 
     * @param transportPlanIds 需要删除的货量提示ID
     * @return 结果
     */
    public int deleteTransportPlanByIds(Long[] transportPlanIds);

    /**
     * 删除货量提示信息
     * 
     * @param transportPlanId 货量提示ID
     * @return 结果
     */
    public int deleteTransportPlanById(Long transportPlanId);
}
