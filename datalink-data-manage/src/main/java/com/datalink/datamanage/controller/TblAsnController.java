package com.datalink.datamanage.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.core.redis.RedisCache;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.itextpdf.PdfUtil;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblAsn;
import com.datalink.datamanage.domain.TblAsnArticle;
import com.datalink.datamanage.domain.TblAsnItem;
import com.datalink.datamanage.domain.TblOrderAsnQuantity;
import com.datalink.datamanage.service.ITblAsnService;
import com.datalink.datamanage.service.ITblOrderService;
import com.datalink.excel.AsnDataListener;
import com.datalink.excel.OpenASNDataListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;

/**
 * ASNController
 *
 * <AUTHOR>
 * @date 2021-08-04
 */
@RestController
@RequestMapping("/datamanage/asn")
public class TblAsnController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(TblAsnController.class);

    @Autowired
    private ITblAsnService tblAsnService;

    @Autowired
    private ITblOrderService tblOrderService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询ASN列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:list')")
    @DataScope(supplierAlias = "a", isNoControl = true)
    @GetMapping("/list")
    public TableDataInfo list(TblAsn tblAsn)
    {
        startPage();
        List<TblAsn> list = tblAsnService.selectTblAsnList(tblAsn);
        return getDataTable(list);
    }

    /**
     * 导出ASN列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:export')")
    @Log(title = "ASN", businessType = BusinessType.EXPORT)
    @DataScope(supplierAlias = "a", isNoControl = true)
    @GetMapping("/export")
    public AjaxResult export(TblAsn tblAsn)
    {
        List<TblAsn> list = tblAsnService.selectTblAsnList(tblAsn);
        ExcelUtil<TblAsn> util = new ExcelUtil<TblAsn>(TblAsn.class);
        return util.exportExcel(list, "ASN数据");
    }

    /**
     * 获取ASN详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:query')")
    @GetMapping(value = "/{asnId}")
    public AjaxResult getInfo(@PathVariable("asnId") Long asnId)
    {
        return AjaxResult.success(tblAsnService.selectTblAsnById(asnId));
    }

    /**
     * 新增ASN
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:add')")
    @Log(title = "ASN", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblAsn tblAsn)
    {
        for (TblAsnItem item : tblAsn.getDetail()){
            for (TblAsnArticle article : item.getArticles()){
                if (null == article.getBatchNo()){
                    article.setBatchNo(DateUtils.parseDateToStr("yyyyMMdd", tblAsn.getDeliveryDate()));
                }
            }
        }
        tblAsn.setCreateBy(SecurityUtils.getUsername());
        return toAjax(tblAsnService.insertTblAsn(tblAsn));
    }

    /**
     * 修改ASN
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:edit')")
    @Log(title = "ASN", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblAsn tblAsn)
    {
        tblAsn.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(tblAsnService.updateTblAsn(tblAsn));
    }

    /**
     * 删除ASN
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:remove')")
    @Log(title = "ASN", businessType = BusinessType.DELETE)
	@DeleteMapping("/{asnIds}")
    public AjaxResult remove(@PathVariable Long[] asnIds)
    {
        return toAjax(tblAsnService.deleteTblAsnByIds(asnIds));
    }

    /**
     * 获取订单行项目
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:query')")
    @GetMapping(value = "/listItems")
    @Log(title = "查看ASN Items", businessType = BusinessType.EXPORT)
    public TableDataInfo listItems(TblAsnItem tblAsnItem)
    {
        startPage();
        return getDataTable(tblAsnService.selectTblAsnItemList(tblAsnItem));
    }

    /**
     * 获取订单详细信息(不包含行项目)
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:query')")
    @GetMapping(value = "/head/{asnId}")
    public AjaxResult getAsnOnly(@PathVariable("asnId") Long asnId)
    {
        return AjaxResult.success(tblAsnService.selectTblAsnOnlyById(asnId));
    }

    /**
     * 获取ASN物料信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:query')")
    @GetMapping(value = "/listArticles")
    public TableDataInfo listArticles(TblAsnArticle tblAsnArticle)
    {
        startPage();
        return getDataTable(tblAsnService.selectTblAsnArticleList(tblAsnArticle));
    }

    @Log(title = "ASN", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('datamanage:asn:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        ExcelReader reader = EasyExcel.read(file.getInputStream()).build();
        ReadSheet sheet = EasyExcel.readSheet(0).registerReadListener(new AsnDataListener()).build();
        reader.read(sheet);
        TblAsn asn = (TblAsn) reader.analysisContext().getCustom();
        reader.finish();
        asn.setAsnCode("temp");
        List<String> errorList = new ArrayList<>();
        Set<ConstraintViolation<TblAsn>> constraintViolations = Validation.buildDefaultValidatorFactory().getValidator().validate(asn);
        for (ConstraintViolation<TblAsn> constraintViolation : constraintViolations) {
            errorList.add(constraintViolation.getMessage());
        }
        for (TblAsnItem item : asn.getDetail()){
            item.setDnNo("temp");
            Set<ConstraintViolation<TblAsnItem>> itemConstraintViolations = Validation.buildDefaultValidatorFactory().getValidator().validate(item);
            for (ConstraintViolation<TblAsnItem> constraintViolation : itemConstraintViolations) {
                errorList.add(constraintViolation.getMessage());
            }
            for (TblAsnArticle article : item.getArticles()){
                Set<ConstraintViolation<TblAsnArticle>> articleConstraintViolations = Validation.buildDefaultValidatorFactory().getValidator().validate(article);
                for (ConstraintViolation<TblAsnArticle> constraintViolation : articleConstraintViolations) {
                    errorList.add(constraintViolation.getMessage());
                }
            }
        }
        if(errorList.size() > 0){
            return AjaxResult.error(errorList.toString());
        }else{
//            String prefix = asn.getCompCode()+asn.getSuppCode()+ DateUtils.parseDateToStr("yyyyMMddHH", new Date());
            String prefix = asn.getSuppCode()+ DateUtils.parseDateToStr("yyyyMMddHH", new Date());
            String count = String.valueOf(redisCache.getIncrementNum(prefix));
            while(count.length() < 3){
                count = "0"+count;
            }
            String asnCode = prefix+count;
            asn.setAsnCode(asnCode);
            for (TblAsnItem item : asn.getDetail()){
                item.setDnNo(asnCode);
            }
            asn.setDirection(DataConstants.DIRECTION_OUT);
            asn.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            asn.setCreateBy(SecurityUtils.getUsername());
            tblAsnService.insertTblAsn(asn);
        }
        return AjaxResult.success("导入成功");
    }

    /**
     * 导入Open ASN数据
     */
    @Log(title = "Open ASN", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('datamanage:asn:import')")
    @PostMapping("/importOpenASN")
    public AjaxResult importOpenASN(MultipartFile file) throws Exception
    {
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }
        
        try {
            // 打印文件信息，辅助调试
            logger.info("开始导入Open ASN文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
            
            ExcelReader reader = EasyExcel.read(file.getInputStream())
                    .headRowNumber(0) // 设置头行为第0行
                    .build();
            ReadSheet sheet = EasyExcel.readSheet(0)
                    .registerReadListener(new OpenASNDataListener(tblOrderService, tblAsnService))
                    .build();
            reader.read(sheet);
            List<TblAsn> asnList = (List<TblAsn>) reader.analysisContext().getCustom();
            reader.finish();

            if (asnList == null || asnList.isEmpty()) {
                logger.warn("导入的Excel文件未包含有效的ASN数据");
                return AjaxResult.error("导入数据为空");
            }

            logger.info("成功解析Excel文件，获取到 {} 条ASN记录", asnList.size());
            
            List<String> errorList = new ArrayList<>();
            List<TblAsn> validAsnList = new ArrayList<>();

            // 验证解析出的ASN记录
            for (TblAsn asn : asnList) {

                // 验证ASN头部
                Set<ConstraintViolation<TblAsn>> constraintViolations = Validation.buildDefaultValidatorFactory().getValidator().validate(asn);
                boolean isValid = true;

//                for (ConstraintViolation<TblAsn> constraintViolation : constraintViolations) {
//                    errorList.add("Open ASN [" + asn.getAsnCode() + "] 校验错误: " + constraintViolation.getMessage());
//                    isValid = false;
//                }

                // 验证ASN行项目和物料
                if (asn.getDetail() == null || asn.getDetail().isEmpty()) {
                    errorList.add("Open ASN [" + asn.getAsnCode() + "] 校验错误: 行项目信息不能为空");
                    isValid = false;
                } else {
                    for (TblAsnItem item : asn.getDetail()) {
//                        Set<ConstraintViolation<TblAsnItem>> itemConstraintViolations = Validation.buildDefaultValidatorFactory().getValidator().validate(item);
//                        for (ConstraintViolation<TblAsnItem> constraintViolation : itemConstraintViolations) {
//                            errorList.add("Open ASN [" + asn.getAsnCode() + "] 项目校验错误: " + constraintViolation.getMessage());
//                            isValid = false;
//                        }

                        if (item.getArticles() == null || item.getArticles().isEmpty()) {
                            errorList.add("Open ASN [" + asn.getAsnCode() + "] 项目 [" + item.getOrderCode() + "] 校验错误: 物料信息不能为空");
                            isValid = false;
                        } else {
//                            for (TblAsnArticle article : item.getArticles()) {
//                                Set<ConstraintViolation<TblAsnArticle>> articleConstraintViolations = Validation.buildDefaultValidatorFactory().getValidator().validate(article);
//                                for (ConstraintViolation<TblAsnArticle> constraintViolation : articleConstraintViolations) {
//                                    errorList.add("Open ASN [" + asn.getAsnCode() + "] 物料 [" + article.getArticleNo() + "] 校验错误: " + constraintViolation.getMessage());
//                                    isValid = false;
//                                }
//                            }
                        }
                    }
                }

                if (isValid) {
                    validAsnList.add(asn);
                }
            }

            if (!errorList.isEmpty()) {
                logger.warn("ASN数据校验失败，错误信息: {}", errorList);
                return AjaxResult.error(String.join("\n", errorList));
            }

            // 处理有效的ASN记录
            String currentUsername = SecurityUtils.getUsername();
            int importCount = 0;
            StringBuilder msg = new StringBuilder();
            for (TblAsn asn : validAsnList) {
                // 设置其他必要字段
                asn.setDirection(DataConstants.DIRECTION_OUT);
                asn.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
                asn.setCreateBy(currentUsername);

                try {
                    // 保存ASN
                    tblAsnService.insertTblAsn(asn);
                } catch (Exception e) {
                    logger.error("导入Open ASN文件时发生错误", e);
                    msg.append(e.getMessage()).append(";");
                    importCount--;
                }
                importCount++;
                
                logger.debug("成功导入ASN: {}", asn.getAsnCode());
            }

            logger.info("成功导入 {} 条Open ASN记录{}", importCount, msg.length() > 0 ? "，部分记录导入失败：" + msg : "");
            return AjaxResult.success("成功导入 " + importCount + " 条Open ASN记录" + (msg.length() > 0 ? "，部分记录导入失败：" + msg : ""));
        } catch (Exception e) {
            logger.error("导入Open ASN文件时发生错误", e);
            return AjaxResult.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取订单剩余物料列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:query')")
    @GetMapping(value = "/listOrderAsnQuantities")
    public AjaxResult listOrderAsnQuantities(TblOrderAsnQuantity tblOrderAsnQuantity)
    {
        return AjaxResult.success(tblAsnService.selectTblOrderAsnQuantityList(tblOrderAsnQuantity));
    }

    /**
     * 打印条码
     */
    @PreAuthorize("@ss.hasPermi('datamanage:asn:export')")
    @Log(title = "打印ASN条码", businessType = BusinessType.EXPORT)
    @GetMapping("/printCode/{asnId}")
    public AjaxResult printCode(@PathVariable("asnId")Long asnId)
    {
        TblAsn asn = tblAsnService.selectTblAsnById(asnId);
        List<Map<String, String>> dataMapList = new ArrayList<>();
        List<Map<String, String>> barcodeMapList = new ArrayList<>();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
//        sdf.setTimeZone(TimeZone.getTimeZone("GMT+0800"));
        for (TblAsnItem item : asn.getDetail()){
            for (TblAsnArticle article : item.getArticles()){
                for (int i = 0; i < article.getPackQty().intValue(); i++){
                    Map<String, String> dataMap = new HashMap<>();
                    Map<String, String> barcodeMap = new HashMap<>();
                    dataMap.put("suppName", asn.getSuppName());
                    dataMap.put("articleNo", article.getArticleNo());
                    dataMap.put("articleName", article.getArticleName());
                    dataMap.put("batchNo", article.getBatchNo());
                    String qty = "0";
                    NumberFormat numberFormat = NumberFormat.getInstance();
                    numberFormat.setGroupingUsed(false);
                    if (i != article.getPackQty().intValue() - 1){
                        qty = numberFormat.format(article.getQtyPerPack());
                    }else{
                        qty = numberFormat.format(article.getQuantity().subtract(article.getQtyPerPack().multiply(article.getPackQty().subtract(new BigDecimal(1)))));
                    }
                    dataMap.put("quantity", qty + article.getUnit());
                    dataMap.put("deliveryDate", DateUtils.parseDateToStr("yyyy-MM-dd",asn.getDeliveryDate()));
                    dataMap.put("printDate", DateUtils.dateTimeNow("yyyy-MM-dd HH:mm:ss"));
                    dataMap.put("printer", SecurityUtils.getLoginUser().getNickName());
                    StringBuilder barcodeSeq = new StringBuilder(String.valueOf(Integer.parseInt(article.getStartWith().substring(article.getStartWith().length() - 6)) + i));
                    while(barcodeSeq.length() < 6){
                        barcodeSeq.insert(0, "0");
                    }
                    String seqNo = barcodeSeq.insert(0, article.getStartWith().substring(0, article.getStartWith().length() - 6)).toString();
                    dataMap.put("barcode", seqNo);
                    String barcode = seqNo + '#' + article.getArticleNo() + '#' + qty + '#' + article.getBatchNo() + '#' + item.getOrderCode() + '#' + asn.getSuppCode();
                    barcodeMap.put("barcodeImage", barcode);
                    dataMapList.add(dataMap);
                    barcodeMapList.add(barcodeMap);
                }

            }
        }
        InputStream template = this.getClass().getClassLoader().getResourceAsStream("templates/asnStampTemplate.pdf");
        return PdfUtil.genPdf(dataMapList, barcodeMapList, template, "ASN-Barcode.pdf");
    }


    /**
     * 打印pdf
     */
    @Log(title = "打印ASN条码", businessType = BusinessType.EXPORT)
    @GetMapping("/printPdf")
    public AjaxResult printPdf(@RequestParam String asnId, @RequestParam String tz)
    {
        return tblAsnService.printPdf(Long.parseLong(asnId), tz);
    }
    /**
     * 打印提货单
     */
    @Log(title = "打印提货单", businessType = BusinessType.EXPORT)
    @GetMapping("/printPickList")
    public AjaxResult printPickList(@RequestParam Long asnId, @RequestParam String tz)
    {
        return tblAsnService.printPickingList(asnId, tz);
    }
}
