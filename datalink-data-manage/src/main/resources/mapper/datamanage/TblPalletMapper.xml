<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblPalletMapper">
    
    <resultMap type="TblPallet" id="TblPalletResult">
        <id property="palletId" column="Pallet_ID"/>
        <result property="materialPlantId" column="Material_Plant_ID"/>
        <result property="snpQuantity" column="SNP_Quantity"/>
        <result property="palletLength" column="Pallet_Length"/>
        <result property="palletWidth" column="Pallet_Width"/>
        <result property="palletHeight" column="Pallet_Height"/>
        <result property="containerType" column="Container_Type"/>
        <result property="createBy" column="Create_By"/>
        <result property="createTime" column="Create_Time"/>
        <result property="updateBy" column="Update_By"/>
        <result property="updateTime" column="Update_Time"/>
        <result property="delFlag" column="Del_Flag"/>
        <association property="materialPlant" column="Material_Plant_ID" javaType="TblMaterialPlant" resultMap="TblMaterialPlantResult" />
    </resultMap>
    
    <resultMap type="TblMaterialPlant" id="TblMaterialPlantResult">
        <id property="materialPlantId" column="Material_Plant_ID"/>
        <result property="materialId" column="Material_ID"/>
        <result property="plantCode" column="Plant_Code"/>
        <result property="packQuantity" column="Pack_Quantity"/>
        <association property="material" column="Material_ID" javaType="TblMaterial" resultMap="TblMaterialResult" />
    </resultMap>
    
    <resultMap type="TblMaterial" id="TblMaterialResult">
        <id property="materialId" column="Material_ID"/>
        <result property="materialCode" column="Material_Code"/>
        <result property="materialName" column="Material_Name"/>
        <result property="baseUnit" column="Base_Unit"/>
    </resultMap>
    
    <resultMap type="PalletInfoVO" id="PalletInfoVOResult">
        <id property="palletId" column="Pallet_ID"/>
        <result property="materialPlantId" column="Material_Plant_ID"/>
        <result property="materialId" column="Material_ID"/>
        <result property="materialCode" column="Material_Code"/>
        <result property="materialName" column="Material_Name"/>
        <result property="baseUnit" column="Base_Unit"/>
        <result property="plantCode" column="Plant_Code"/>
        <result property="packQuantity" column="Pack_Quantity"/>
        <result property="snpQuantity" column="SNP_Quantity"/>
        <result property="palletLength" column="Pallet_Length"/>
        <result property="palletWidth" column="Pallet_Width"/>
        <result property="palletHeight" column="Pallet_Height"/>
        <result property="containerType" column="Container_Type"/>
        <result property="createBy" column="Create_By"/>
        <result property="createTime" column="Create_Time"/>
        <result property="updateBy" column="Update_By"/>
        <result property="updateTime" column="Update_Time"/>
    </resultMap>
    
    <sql id="selectTblPalletVo">
        select p.Pallet_ID, p.Material_Plant_ID, p.SNP_Quantity, p.Pallet_Length, p.Pallet_Width, 
               p.Pallet_Height, p.Container_Type, p.Create_By, p.Create_Time, p.Update_By, p.Update_Time, p.Del_Flag,
               mp.Material_ID, mp.Plant_Code, mp.Pack_Quantity,
               m.Material_Code, m.Material_Name, m.Base_Unit
        from tbl_pallet p
        left join tbl_material_plant mp on p.Material_Plant_ID = mp.Material_Plant_ID
        left join tbl_material m on mp.Material_ID = m.Material_ID
    </sql>
    
    <sql id="selectPalletInfoVo">
        select p.Pallet_ID, p.Material_Plant_ID, p.SNP_Quantity, p.Pallet_Length, p.Pallet_Width, 
               p.Pallet_Height, p.Container_Type, p.Create_By, p.Create_Time, p.Update_By, p.Update_Time,
               mp.Material_ID, mp.Plant_Code, mp.Pack_Quantity,
               m.Material_Code, m.Material_Name, m.Base_Unit
        from tbl_pallet p
        left join tbl_material_plant mp on p.Material_Plant_ID = mp.Material_Plant_ID
        left join tbl_material m on mp.Material_ID = m.Material_ID
    </sql>
    
    <select id="selectTblPalletById" parameterType="Long" resultMap="TblPalletResult">
        <include refid="selectTblPalletVo"/>
        where p.Pallet_ID = #{palletId}
        and p.Del_Flag = '0'
    </select>
    
    <select id="selectTblPalletList" parameterType="TblPallet" resultMap="TblPalletResult">
        <include refid="selectTblPalletVo"/>
        <where>
            p.Del_Flag = '0'
            <if test="materialPlantId != null">
                AND p.Material_Plant_ID = #{materialPlantId}
            </if>
        </where>
    </select>
    
    <select id="selectPalletInfoList" parameterType="PalletInfoVO" resultMap="PalletInfoVOResult">
        <include refid="selectPalletInfoVo"/>
        <where>
            p.Del_Flag = '0'
            <if test="materialPlantId != null">
                AND p.Material_Plant_ID = #{materialPlantId}
            </if>
            <if test="materialId != null">
                AND mp.Material_ID = #{materialId}
            </if>
            <if test="materialCode != null and materialCode != ''">
                AND m.Material_Code like concat('%', #{materialCode}, '%')
            </if>
            <if test="plantCode != null and plantCode != ''">
                AND mp.Plant_Code = #{plantCode}
            </if>
        </where>
    </select>
    
    <select id="selectTblPalletByMaterialPlantId" parameterType="Long" resultMap="TblPalletResult">
        <include refid="selectTblPalletVo"/>
        where p.Material_Plant_ID = #{materialPlantId}
        and p.Del_Flag = '0'
    </select>
    
    <insert id="insertTblPallet" parameterType="TblPallet" useGeneratedKeys="true" keyProperty="palletId">
        insert into tbl_pallet
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialPlantId != null">Material_Plant_ID,</if>
            <if test="snpQuantity != null">SNP_Quantity,</if>
            <if test="palletLength != null">Pallet_Length,</if>
            <if test="palletWidth != null">Pallet_Width,</if>
            <if test="palletHeight != null">Pallet_Height,</if>
            <if test="containerType != null and containerType != ''">Container_Type,</if>
            <if test="createBy != null and createBy != ''">Create_By,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="updateBy != null and updateBy != ''">Update_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="delFlag != null">Del_Flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialPlantId != null">#{materialPlantId},</if>
            <if test="snpQuantity != null">#{snpQuantity},</if>
            <if test="palletLength != null">#{palletLength},</if>
            <if test="palletWidth != null">#{palletWidth},</if>
            <if test="palletHeight != null">#{palletHeight},</if>
            <if test="containerType != null and containerType != ''">#{containerType},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>
    
    <update id="updateTblPallet" parameterType="TblPallet">
        update tbl_pallet
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialPlantId != null">Material_Plant_ID = #{materialPlantId},</if>
            <if test="snpQuantity != null">SNP_Quantity = #{snpQuantity},</if>
            <if test="palletLength != null">Pallet_Length = #{palletLength},</if>
            <if test="palletWidth != null">Pallet_Width = #{palletWidth},</if>
            <if test="palletHeight != null">Pallet_Height = #{palletHeight},</if>
            <if test="containerType != null and containerType != ''">Container_Type = #{containerType},</if>
            <if test="updateBy != null and updateBy != ''">Update_By = #{updateBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
        </trim>
        where Pallet_ID = #{palletId}
    </update>
    
    <delete id="deleteTblPalletById" parameterType="Long">
        update tbl_pallet set Del_Flag = '2' where Pallet_ID = #{palletId}
    </delete>
    
    <delete id="deleteTblPalletByIds" parameterType="String">
        update tbl_pallet set Del_Flag = '2' where Pallet_ID in 
        <foreach item="palletId" collection="array" open="(" separator="," close=")">
            #{palletId}
        </foreach>
    </delete>
</mapper> 