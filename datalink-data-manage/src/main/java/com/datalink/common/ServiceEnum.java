package com.datalink.common;

import com.datalink.datamanage.service.ITblNoticeService;
import com.datalink.datamanage.service.ITblOrderService;

public enum ServiceEnum {
    OrderService {
        @Override
        public Class<?> getService() {
            return ITblOrderService.class;
        }
    },
    NoticeService {
        @Override
        public Class<?> getService() {
            return ITblNoticeService.class;
        }
    };

    public abstract Class<?> getService();
}
