package com.datalink.api.common;


import com.datalink.common.DataConstants;
import com.datalink.kafka.KafkaData;
import com.datalink.system.service.ISysConfigService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class KafkaMessage<T extends KafkaData> {

    private String from;
    private String to;
    private List<T> data;
    private String type;

    public KafkaMessage() {
    }

    public KafkaMessage(String from, String to, List<T> data, String type){
        this.from = from;
        this.to = to;
        this.data = data;
        this.type = type;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String toMessageString() throws JsonProcessingException{
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(this);
    }

    public void getTopic(){

    }
}
