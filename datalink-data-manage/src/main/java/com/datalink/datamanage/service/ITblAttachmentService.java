package com.datalink.datamanage.service;

import com.datalink.datamanage.domain.TblAttachment;

import java.util.List;

/**
 * 附件Service接口
 *
 * <AUTHOR>
 * @date 2023-01-24
 */
public interface ITblAttachmentService
{
    /**
     * 查询附件
     *
     * @param attachmentId 附件ID
     * @return 附件
     */
    public TblAttachment selectTblAttachmentById(Long attachmentId);

    /**
     * 查询附件列表
     *
     * @param tblAttachment 附件
     * @return 附件集合
     */
    public List<TblAttachment> selectTblAttachmentList(TblAttachment tblAttachment);

    /**
     * 新增附件
     *
     * @param tblAttachment 附件
     * @return 结果
     */
    public int insertTblAttachment(TblAttachment tblAttachment);

    /**
     * 修改附件
     *
     * @param tblAttachment 附件
     * @return 结果
     */
    public int updateTblAttachment(TblAttachment tblAttachment);

    /**
     * 批量删除附件
     *
     * @param attachmentIds 需要删除的附件ID
     * @return 结果
     */
    public int deleteTblAttachmentByIds(Long[] attachmentIds);

    /**
     * 删除附件信息
     *
     * @param attachmentId 附件ID
     * @return 结果
     */
    public int deleteTblAttachmentById(Long attachmentId);
}
