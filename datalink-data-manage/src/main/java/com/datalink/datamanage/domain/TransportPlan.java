package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 货量提示对象 tbl_transport_plan
 * 
 * <AUTHOR>
 * @date 2024-11-03
 */
public class TransportPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 运输计划ID */
    private Long transportPlanId;

    /** 序号 */
    @Excel(name = "序号")
    private Long no;

    /** 状态(荷確等) */
    @Excel(name = "状态(荷確等)")
    private String status;

    /** 仓库编号 */
    @Excel(name = "仓库编号")
    private String depot;

    /** 物料因子 */
    @Excel(name = "物料因子")
    private String materialFactor;

    /** 运输方式 */
    @Excel(name = "运输方式")
    private String way;

    /** 客户编号 */
    @Excel(name = "客户编号")
    private String clientCode;

    /** 交货地点编号 */
    @Excel(name = "交货地点编号")
    private String deliveryLocation;

    /** 工厂编号 */
    @Excel(name = "工厂编号")
    private String factory;

    /** 提货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pickupDate;

    /** 提货时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "提货时间", width = 30, dateFormat = "HH:mm:ss")
    private Date pickupTime;

    /** 交货日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deliveryDate;

    /** 交货时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "交货时间", width = 30, dateFormat = "HH:mm:ss")
    private Date deliveryTime;

    /** 港口编号 */
    @Excel(name = "港口编号")
    private String port;

    /** 总重量 */
    @Excel(name = "总重量")
    private BigDecimal weightTotal;

    /** 总数量 */
    @Excel(name = "总数量")
    private Long totalQuantity;

    /** 平均值 */
    @Excel(name = "平均值")
    private BigDecimal average;

    /** 票据编号 */
    @Excel(name = "票据编号")
    private String ticketNo;

    /** 公司编号 */
    @Excel(name = "公司编号")
    private String company;

    /** 提货数量 */
    @Excel(name = "提货数量")
    private Long pickupQuantity;

    /** 车型 */
    @Excel(name = "车型")
    private String carType;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNo;

    /** 司机姓名 */
    @Excel(name = "司机姓名")
    private String driver;

    /** 交货地点名称 */
    @Excel(name = "交货地点名称")
    private String deliveryLocationName;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 承运商提货时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "承运商提货时间", width = 30, dateFormat = "HH:mm:ss")
    private Date carrierPickupTime;

    /** 创建用户 */
    @Excel(name = "创建用户")
    private String createBy;

    /** 更新用户 */
    @Excel(name = "更新用户")
    private String updateBy;

    private Long userId;

    public void setTransportPlanId(Long transportPlanId) 
    {
        this.transportPlanId = transportPlanId;
    }

    public Long getTransportPlanId() 
    {
        return transportPlanId;
    }
    public void setNo(Long no) 
    {
        this.no = no;
    }

    public Long getNo() 
    {
        return no;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDepot(String depot) 
    {
        this.depot = depot;
    }

    public String getDepot() 
    {
        return depot;
    }
    public void setMaterialFactor(String materialFactor) 
    {
        this.materialFactor = materialFactor;
    }

    public String getMaterialFactor() 
    {
        return materialFactor;
    }
    public void setWay(String way) 
    {
        this.way = way;
    }

    public String getWay() 
    {
        return way;
    }
    public void setClientCode(String clientCode) 
    {
        this.clientCode = clientCode;
    }

    public String getClientCode() 
    {
        return clientCode;
    }
    public void setDeliveryLocation(String deliveryLocation) 
    {
        this.deliveryLocation = deliveryLocation;
    }

    public String getDeliveryLocation()
    {
        return deliveryLocation;
    }
    public void setFactory(String factory) 
    {
        this.factory = factory;
    }

    public String getFactory() 
    {
        return factory;
    }
    public void setPickupDate(Date pickupDate) 
    {
        this.pickupDate = pickupDate;
    }

    public Date getPickupDate() 
    {
        return pickupDate;
    }
    public void setPickupTime(Date pickupTime) 
    {
        this.pickupTime = pickupTime;
    }

    public Date getPickupTime() 
    {
        return pickupTime;
    }

    public void setCarrierPickupTime(Date carrierPickupTime)
    {
        this.carrierPickupTime = carrierPickupTime;
    }

    public Date getCarrierPickupTime()
    {
        return carrierPickupTime;
    }
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }
    public void setDeliveryTime(Date deliveryTime) 
    {
        this.deliveryTime = deliveryTime;
    }

    public Date getDeliveryTime() 
    {
        return deliveryTime;
    }
    public void setPort(String port) 
    {
        this.port = port;
    }

    public String getPort() 
    {
        return port;
    }
    public void setWeightTotal(BigDecimal weightTotal) 
    {
        this.weightTotal = weightTotal;
    }

    public BigDecimal getWeightTotal() 
    {
        return weightTotal;
    }
    public void setTotalQuantity(Long totalQuantity) 
    {
        this.totalQuantity = totalQuantity;
    }

    public Long getTotalQuantity() 
    {
        return totalQuantity;
    }
    public void setAverage(BigDecimal average) 
    {
        this.average = average;
    }

    public BigDecimal getAverage() 
    {
        return average;
    }
    public void setTicketNo(String ticketNo) 
    {
        this.ticketNo = ticketNo;
    }

    public String getTicketNo() 
    {
        return ticketNo;
    }
    public void setCompany(String company) 
    {
        this.company = company;
    }

    public String getCompany() 
    {
        return company;
    }
    public void setPickupQuantity(Long pickupQuantity) 
    {
        this.pickupQuantity = pickupQuantity;
    }

    public Long getPickupQuantity() 
    {
        return pickupQuantity;
    }
    public void setCarType(String carType) 
    {
        this.carType = carType;
    }

    public String getCarType() 
    {
        return carType;
    }
    public void setCarNo(String carNo) 
    {
        this.carNo = carNo;
    }

    public String getCarNo() 
    {
        return carNo;
    }
    public void setDriver(String driver) 
    {
        this.driver = driver;
    }

    public String getDriver() 
    {
        return driver;
    }
    public void setDeliveryLocationName(String deliveryLocationName) 
    {
        this.deliveryLocationName = deliveryLocationName;
    }

    public String getDeliveryLocationName() 
    {
        return deliveryLocationName;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }

    public void setCreateBy(String createBy)
    {
        this.createBy = createBy;
    }

    public String getCreateBy()
    {
        return createBy;
    }

    public void setUpdateBy(String updateBy)
    {
        this.updateBy = updateBy;
    }

    public String getUpdateBy()
    {
        return updateBy;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("transportPlanId", getTransportPlanId())
            .append("no", getNo())
            .append("status", getStatus())
            .append("depot", getDepot())
            .append("materialFactor", getMaterialFactor())
            .append("way", getWay())
            .append("clientCode", getClientCode())
            .append("deliveryLocation", getDeliveryLocation())
            .append("factory", getFactory())
            .append("pickupDate", getPickupDate())
            .append("pickupTime", getPickupTime())
            .append("carrierPickupTime", getCarrierPickupTime())
            .append("deliveryDate", getDeliveryDate())
            .append("deliveryTime", getDeliveryTime())
            .append("port", getPort())
            .append("weightTotal", getWeightTotal())
            .append("totalQuantity", getTotalQuantity())
            .append("average", getAverage())
            .append("ticketNo", getTicketNo())
            .append("company", getCompany())
            .append("pickupQuantity", getPickupQuantity())
            .append("carType", getCarType())
            .append("carNo", getCarNo())
            .append("driver", getDriver())
            .append("deliveryLocationName", getDeliveryLocationName())
            .append("companyName", getCompanyName())
            .append("createTime", getCreateTime())
            .append("createUser", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateUser", getUpdateBy())
            .toString();
    }
}
