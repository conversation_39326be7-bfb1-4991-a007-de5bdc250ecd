package com.datalink.api.domain;

import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SapWeekForecastItem extends BaseEntity {
    private Long id;
    private Long forecastId;
    private String matnr;
    private String werks;
    private String zmethd;
    private String beskz;
    private String ordCls;
    private String wanst;
    private String dispo;
    private BigDecimal bstrf;
    private String meins;
    private String maabc;
    private Integer wmNo;
    private String datsLm;
    private String datsM;
    private String datsNm;
    private String datsNnm;
    private String poutMk;
    private String poutYm;
    private List<SapWeekForecastItemDetail> details;
}