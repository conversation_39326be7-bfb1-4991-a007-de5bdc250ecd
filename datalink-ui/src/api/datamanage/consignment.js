import request from '@/utils/request'

// 查询寄售库存列表
export function listConsignment(query) {
  return request({
    url: '/datamanage/consignment/list',
    method: 'get',
    params: query
  })
}

// 查询寄售库存详细
export function getConsignment(consignmentid) {
  return request({
    url: '/datamanage/consignment/' + consignmentid,
    method: 'get'
  })
}

// 查询寄售库存详细(不包含行项目)
export function getConsignmentOnly(consignmentid) {
  return request({
    url: '/datamanage/consignment/head/' + consignmentid,
    method: 'get'
  })
}

// 新增寄售库存
export function addConsignment(data) {
  return request({
    url: '/datamanage/consignment',
    method: 'post',
    data: data
  })
}

// 修改寄售库存
export function updateConsignment(data) {
  return request({
    url: '/datamanage/consignment',
    method: 'put',
    data: data
  })
}

// 删除寄售库存
export function delConsignment(consignmentid) {
  return request({
    url: '/datamanage/consignment/' + consignmentid,
    method: 'delete'
  })
}

// 导出寄售库存
export function exportConsignment(query) {
  return request({
    url: '/datamanage/consignment/export',
    method: 'get',
    params: query
  })
}

// 查询寄售库存行项目列表
export function listConsignmentItems(query) {
  return request({
    url: '/datamanage/consignment/listItems',
    method: 'get',
    params: query
  })
}
