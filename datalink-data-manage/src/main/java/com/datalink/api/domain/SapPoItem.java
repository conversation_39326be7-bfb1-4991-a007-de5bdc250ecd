package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

public class SapPoItem {

    /** 工厂代码 */
    @JsonProperty("WERKS")
    private String WERKS;

    /** 采购组织 */
    @JsonProperty("EKORG")
    private String EKORG;

    /** 订单类型描述 */
    @JsonProperty("BATXT")
    private String BATXT;

    /** 供应商编码 */
    @JsonProperty("LIFNR")
    private String LIFNR;

    /** Depot */
    @JsonProperty("ZDEPOT")
    private String ZDEPOT;

    /** 供应商描述 */
    @JsonProperty("ZNAME1")
    private String ZNAME1;

    /** 公司代码 */
    @JsonProperty("BUKRS")
    private String BUKRS;

    /** 物料编码 */
    @JsonProperty("MATNR")
    private String MATNR;

    /** 物料描述 */
    @JsonProperty("TXZ01")
    private String TXZ01;

    /** PO采购订单 */
    @JsonProperty("EBELN")
    private String EBELN;

    /** PO行项目 */
    @JsonProperty("EBELP")
    private String EBELP;

    /** SO销售订单 */
    @JsonProperty("VBELN")
    private String VBELN;

    /** SO行项目 */
    @JsonProperty("POSNR")
    private String POSNR;

    /** 采购交期 */
    @JsonProperty("EINDT")
    private String EINDT;

    /** 客户交货日期 */
    @JsonProperty("EDATU")
    private String EDATU;

    /** 订单数量 */
    @JsonProperty("MENGE")
    private String MENGE;

    /** 客户编码 */
    @JsonProperty("KUNNR")
    private String KUNNR;

    /** 送达方 */
    @JsonProperty("VBPA")
    private String VBPA;

    /** 采购计量单位 */
    @JsonProperty("MEINS")
    private String MEINS;

    /** MRP策略组 */
    @JsonProperty("STRGR")
    private String STRGR;

    /** 纳入方式 */
    @JsonProperty("ZMETHD")
    private String ZMETHD;

    /** 凭证类型 */
    @JsonProperty("BSART")
    private String BSART;

    /** 自己調達区分 */
    @JsonProperty("ZMM001")
    private String ZMM001;

    /** 采购单价（不含税） */
    @JsonProperty("NETPR")
    private String NETPR;

    /** 订单价格单位（采购） */
    @JsonProperty("BPRME")
    private String BPRME;

    /** 价格单位 */
    @JsonProperty("PEINH")
    private String PEINH;

    /** 订单货币 */
    @JsonProperty("WAERS")
    private String WAERS;

    /** 收货场所 */
    @JsonProperty("WANST")
    private String WANST;

    /** 库存地点 */
    @JsonProperty("LGORT")
    private String LGORT;

    /** 记录创建日期+时间 */
    @JsonProperty("AEDAT")
    private String AEDAT;

    /** 行项目订单注释 */
    @JsonProperty("ZTEXT1")
    private String ZTEXT1;

    /** 行项目原交期 */
    @JsonProperty("ZEINDT")
    private String ZEINDT;

    /** 公司名称 */
    @JsonProperty("ZNAME")
    private String ZNAME;

    /** 部品番号識別 */
    @JsonProperty("CODE")
    private String CODE;

    /** 舍入值 */
    @JsonProperty("BSTRF")
    private String BSTRF;

    /** 基本计量单位 */
    @JsonProperty("MEINS_02")
    private String MEINS_02;

    /** 卸货点 */
    @JsonProperty("SORT1")
    private String SORT1;

    /** 客户物料名称 */
    @JsonProperty("KDMAT")
    private String KDMAT;

    /** 采购类型 */
    @JsonProperty("BESKZ")
    private String BESKZ;

    /** 作业区 */
    @JsonProperty("LIFNR_02")
    private String LIFNR_02;

    /** ABC区分 */
    @JsonProperty("MAABC")
    private String MAABC;

    /** 在担 */
    @JsonProperty("ZMM002")
    private String ZMM002;

    /** 存储仓位 */
    @JsonProperty("LGPBE")
    private String LGPBE;

    /** 備考 */
    @JsonProperty("EDATU_02")
    private String EDATU_02;

    /** 删除标记 */
    @JsonProperty("LOKEZ")
    private String LOKEZ;

    /** 行项目类别 */
    @JsonProperty("PSTYP")
    private String PSTYP;

    /** 分割回数 */
    @JsonProperty("ZFGHS")
    private String ZFGHS;

    /** SO订单类型 */
    @JsonProperty("AUART")
    private String AUART;

    /** SO订单数量 */
    @JsonProperty("KWMEMG")
    private String KWMEMG;

    /** 客户PO号 */
    @JsonProperty("BSTNK")
    private String BSTNK;

    /** Rank NO */
    @JsonProperty("IHREZ")
    private String IHREZ;

    /** 箱包数量 */
    @JsonProperty("ZXBSL")
    private String ZXBSL;

    /** 供应商编码 */
    @JsonProperty("LIFNR_03")
    private String LIFNR_03;

    /** SupplierCode */
    @JsonProperty("LIFNR_04")
    private String LIFNR_04;

    /** PO№&RAN */
    @JsonProperty("ZPORAN")
    private String ZPORAN;

    /** SO创建日期 */
    @JsonProperty("ERDAT")
    private String ERDAT;

    /** 发货工厂 */
    @JsonProperty("WERKS_02")
    private String WERKS_02;

    /** 修改日期+时间 */
    @JsonProperty("ZMDDT")
    private String ZMDDT;

    public String getWERKS() {
        return WERKS;
    }

    public void setWERKS(String WERKS) {
        this.WERKS = WERKS;
    }

    public String getEKORG() {
        return EKORG;
    }

    public void setEKORG(String EKORG) {
        this.EKORG = EKORG;
    }

    public String getBATXT() {
        return BATXT;
    }

    public void setBATXT(String BATXT) {
        this.BATXT = BATXT;
    }

    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }

    public String getMAABC() {
        return MAABC;
    }

    public void setMAABC(String MAABC) {
        this.MAABC = MAABC;
    }

    public String getZMM002() {
        return ZMM002;
    }

    public void setZMM002(String ZMM002) {
        this.ZMM002 = ZMM002;
    }

    public String getLGPBE() {
        return LGPBE;
    }

    public void setLGPBE(String LGPBE) {
        this.LGPBE = LGPBE;
    }

    public String getZDEPOT() {
        return ZDEPOT;
    }

    public void setZDEPOT(String ZDEPOT) {
        this.ZDEPOT = ZDEPOT;
    }

    public String getZNAME1() {
        return ZNAME1;
    }

    public void setZNAME1(String ZNAME1) {
        this.ZNAME1 = ZNAME1;
    }

    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }

    public String getZNAME() {
        return ZNAME;
    }

    public void setZNAME(String ZNAME) {
        this.ZNAME = ZNAME;
    }

    public String getCODE() {
        return CODE;
    }

    public void setCODE(String CODE) {
        this.CODE = CODE;
    }

    public String getBSTRF() {
        return BSTRF;
    }

    public void setBSTRF(String BSTRF) {
        this.BSTRF = BSTRF;
    }

    public String getMATNR() {
        return MATNR;
    }

    public void setMATNR(String MATNR) {
        this.MATNR = MATNR;
    }

    public String getTXZ01() {
        return TXZ01;
    }

    public void setTXZ01(String TXZ01) {
        this.TXZ01 = TXZ01;
    }

    public String getSORT1() {
        return SORT1;
    }

    public void setSORT1(String SORT1) {
        this.SORT1 = SORT1;
    }

    public String getKDMAT() {
        return KDMAT;
    }

    public void setKDMAT(String KDMAT) {
        this.KDMAT = KDMAT;
    }

    public String getBESKZ() {
        return BESKZ;
    }

    public void setBESKZ(String BESKZ) {
        this.BESKZ = BESKZ;
    }

    public String getEBELN() {
        return EBELN;
    }

    public void setEBELN(String EBELN) {
        this.EBELN = EBELN;
    }

    public String getEBELP() {
        return EBELP;
    }

    public void setEBELP(String EBELP) {
        this.EBELP = EBELP;
    }

    public String getVBELN() {
        return VBELN;
    }

    public void setVBELN(String VBELN) {
        this.VBELN = VBELN;
    }

    public String getPOSNR() {
        return POSNR;
    }

    public void setPOSNR(String POSNR) {
        this.POSNR = POSNR;
    }

    public String getEINDT() {
        return EINDT;
    }

    public void setEINDT(String EINDT) {
        this.EINDT = EINDT;
    }

    public String getEDATU() {
        return EDATU;
    }

    public void setEDATU(String EDATU) {
        this.EDATU = EDATU;
    }

    public String getLOKEZ() {
        return LOKEZ;
    }

    public void setLOKEZ(String LOKEZ) {
        this.LOKEZ = LOKEZ;
    }

    public String getPSTYP() {
        return PSTYP;
    }

    public void setPSTYP(String PSTYP) {
        this.PSTYP = PSTYP;
    }

    public String getMENGE() {
        return MENGE;
    }

    public void setMENGE(String MENGE) {
        this.MENGE = MENGE;
    }

    public String getKUNNR() {
        return KUNNR;
    }

    public void setKUNNR(String KUNNR) {
        this.KUNNR = KUNNR;
    }

    public String getVBPA() {
        return VBPA;
    }

    public void setVBPA(String VBPA) {
        this.VBPA = VBPA;
    }

    public String getMEINS() {
        return MEINS;
    }

    public void setMEINS(String MEINS) {
        this.MEINS = MEINS;
    }

    public String getSTRGR() {
        return STRGR;
    }

    public void setSTRGR(String STRGR) {
        this.STRGR = STRGR;
    }

    public String getZMETHD() {
        return ZMETHD;
    }

    public void setZMETHD(String ZMETHD) {
        this.ZMETHD = ZMETHD;
    }

    public String getBSART() {
        return BSART;
    }

    public void setBSART(String BSART) {
        this.BSART = BSART;
    }

    public String getZMM001() {
        return ZMM001;
    }

    public void setZMM001(String ZMM001) {
        this.ZMM001 = ZMM001;
    }

    public String getNETPR() {
        return NETPR;
    }

    public void setNETPR(String NETPR) {
        this.NETPR = NETPR;
    }

    public String getBPRME() {
        return BPRME;
    }

    public void setBPRME(String BPRME) {
        this.BPRME = BPRME;
    }

    public String getPEINH() {
        return PEINH;
    }

    public void setPEINH(String PEINH) {
        this.PEINH = PEINH;
    }

    public String getWAERS() {
        return WAERS;
    }

    public void setWAERS(String WAERS) {
        this.WAERS = WAERS;
    }

    public String getWANST() {
        return WANST;
    }

    public void setWANST(String WANST) {
        this.WANST = WANST;
    }

    public String getLGORT() {
        return LGORT;
    }

    public void setLGORT(String LGORT) {
        this.LGORT = LGORT;
    }

    public String getAEDAT() {
        return AEDAT;
    }

    public void setAEDAT(String AEDAT) {
        this.AEDAT = AEDAT;
    }

    public String getZTEXT1() {
        return ZTEXT1;
    }

    public void setZTEXT1(String ZTEXT1) {
        this.ZTEXT1 = ZTEXT1;
    }

    public String getZEINDT() {
        return ZEINDT;
    }

    public void setZEINDT(String ZEINDT) {
        this.ZEINDT = ZEINDT;
    }

    public String getMEINS_02() {
        return MEINS_02;
    }

    public void setMEINS_02(String MEINS_02) {
        this.MEINS_02 = MEINS_02;
    }

    public String getLIFNR_02() {
        return LIFNR_02;
    }

    public void setLIFNR_02(String LIFNR_02) {
        this.LIFNR_02 = LIFNR_02;
    }

    public String getEDATU_02() {
        return EDATU_02;
    }

    public void setEDATU_02(String EDATU_02) {
        this.EDATU_02 = EDATU_02;
    }

    public String getZFGHS() {
        return ZFGHS;
    }

    public void setZFGHS(String ZFGHS) {
        this.ZFGHS = ZFGHS;
    }

    public String getAUART() {
        return AUART;
    }

    public void setAUART(String AUART) {
        this.AUART = AUART;
    }

    public String getKWMEMG() {
        return KWMEMG;
    }

    public void setKWMEMG(String KWMEMG) {
        this.KWMEMG = KWMEMG;
    }

    public String getBSTNK() {
        return BSTNK;
    }

    public void setBSTNK(String BSTNK) {
        this.BSTNK = BSTNK;
    }

    public String getIHREZ() {
        return IHREZ;
    }

    public void setIHREZ(String IHREZ) {
        this.IHREZ = IHREZ;
    }

    public String getZXBSL() {
        return ZXBSL;
    }

    public void setZXBSL(String ZXBSL) {
        this.ZXBSL = ZXBSL;
    }

    public String getLIFNR_03() {
        return LIFNR_03;
    }

    public void setLIFNR_03(String LIFNR_03) {
        this.LIFNR_03 = LIFNR_03;
    }

    public String getLIFNR_04() {
        return LIFNR_04;
    }

    public void setLIFNR_04(String LIFNR_04) {
        this.LIFNR_04 = LIFNR_04;
    }

    public String getZPORAN() {
        return ZPORAN;
    }

    public void setZPORAN(String ZPORAN) {
        this.ZPORAN = ZPORAN;
    }

    public String getERDAT() {
        return ERDAT;
    }

    public void setERDAT(String ERDAT) {
        this.ERDAT = ERDAT;
    }

    public String getWERKS_02() {
        return WERKS_02;
    }

    public void setWERKS_02(String WERKS_02) {
        this.WERKS_02 = WERKS_02;
    }

    public String getZMDDT() {
        return ZMDDT;
    }

    public void setZMDDT(String ZMDDT) {
        this.ZMDDT = ZMDDT;
    }
}
