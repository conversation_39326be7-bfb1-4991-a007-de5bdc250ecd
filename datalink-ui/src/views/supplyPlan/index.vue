<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <!-- <div class="card-header">
      <h2>{{ $t('supplyPlan.plan.title') }}</h2>
    </div> -->

    <!-- 搜索表单 -->
    <el-form :inline="true" class="query-form">
      <el-form-item :label="$t('supplyPlan.form.yearMonth')">
        <el-date-picker v-model="selectedYearMonth" format="yyyyMM" value-format="yyyy-MM"
          type="month"></el-date-picker>
      </el-form-item>

      <el-form-item :label="$t('system.user.departmentLabel')">
        <el-select-tree node-key="code" :data="supplierTreeData" v-model="client" clearable
          check-strictly></el-select-tree>
      </el-form-item>
      <!-- <el-form-item :label="$t('supplyPlan.form.client')">
        <el-select v-model="client" :placeholder="$t('supplyPlan.form.clientPlaceholder')">
          <el-option v-for="option in clientOptions" :key="option.value" :label="option.label"
            :value="option.value"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item v-if="!isCarrierWithDepot" :label="$t('supplyPlan.form.depot')">
        <!-- <el-select v-model="depot" clearable :placeholder="$t('supplyPlan.form.depotPlaceholder')">
          <el-option v-for="option in depotOptions" :label="option.dictLabel" :value="option.dictValue"></el-option>
        </el-select> -->
        <el-input v-model="depot" :placeholder="$t('supplyPlan.form.depotPlaceholder')" clearable
        />
      </el-form-item>
      <el-form-item :label="$t('supplyPlan.form.factory')">
        <!-- <el-select v-model="factory" clearable :placeholder="$t('supplyPlan.form.factoryPlaceholder')">
          <el-option v-for="option in factoryOptions" :label="option.dictLabel" :value="option.dictValue"></el-option>
        </el-select> -->
        <el-input v-model="factory" :placeholder="$t('supplyPlan.form.factoryPlaceholder')" clearable
        />
      </el-form-item>
      <el-form-item :label="$t('supplyPlan.form.partNumber')">
        <el-input v-model="partNumber" clearable :placeholder="$t('supplyPlan.form.partNumberPlaceholder')"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="query">{{ $t("supplyPlan.buttons.search")
          }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("supplyPlan.buttons.reset")
          }}</el-button>
      </el-form-item>
    </el-form>


    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" :loading="printTxtLoading"
          @click="handlePrintTxt">{{
            $t('order.button.printTxt') }}</el-button>
      </el-col>
    </el-row>
    <!-- 基本信息表 -->
    <h2>{{ $t("supplyPlan.info.basicInfo") }}</h2>
    <el-pagination v-if="total >= pageSize" background class="custom-pagination" :total="total" :page-size="pageSize"
      :current-page="pageNum" layout="prev, pager, next" @current-change="handlePageChange">
    </el-pagination>
    <el-table :data="[additionalInfo]" border stripe>
      <el-table-column prop="client" :label="$t('supplyPlan.info.client')"></el-table-column>
      <el-table-column prop="depot" :label="$t('supplyPlan.info.depot')"></el-table-column>
      <el-table-column prop="factory" :label="$t('supplyPlan.info.factory')"></el-table-column>
      <el-table-column prop="partNumber" :label="$t('supplyPlan.info.partNumber')"></el-table-column>
      <el-table-column :label="$t('supplyPlan.info.requirement')">
        <el-table-column prop="monthlyRequirement.NMonth" :label="$t('supplyPlan.info.nMonth')"></el-table-column>
        <el-table-column prop="monthlyRequirement.N1Month" :label="$t('supplyPlan.info.n1Month')"></el-table-column>
        <el-table-column prop="monthlyRequirement.N2Month" :label="$t('supplyPlan.info.n2Month')"></el-table-column>
      </el-table-column>
      <el-table-column prop="monthlyPlan" :label="$t('supplyPlan.info.monthly')"></el-table-column>
      <el-table-column prop="dailyPlan" :label="$t('supplyPlan.info.daily')"></el-table-column>
    </el-table>

    <el-table :data="[additionalInfo]" border stripe>
      <el-table-column prop="deliveryMethod" :label="$t('supplyPlan.info.deliveryMethod')"></el-table-column>
      <el-table-column prop="supplyType" :label="$t('supplyPlan.info.supplyType')"></el-table-column>
      <el-table-column prop="supplyMethod" :label="$t('supplyPlan.info.supplyMethod')"></el-table-column>
      <el-table-column prop="deliveryLocation" :label="$t('supplyPlan.info.deliveryLocation')"></el-table-column>
      <el-table-column prop="basicUnit" :label="$t('supplyPlan.info.basicUnit')"></el-table-column>
      <el-table-column prop="orderUnit" :label="$t('supplyPlan.info.orderUnit')"></el-table-column>
      <el-table-column prop="allocationLot" :label="$t('supplyPlan.info.allocationLot')"></el-table-column>
      <el-table-column prop="snep" :label="$t('supplyPlan.info.snep')">
      </el-table-column>
      <el-table-column prop="currentYearMonth" :label="$t('supplyPlan.info.currentYearMonth')"></el-table-column>
      <el-table-column prop="clientInventory" :label="$t('supplyPlan.info.clientInventory')"></el-table-column>
      <el-table-column prop="confirmedDate" :label="$t('supplyPlan.info.confirmedDate')"></el-table-column>
      <el-table-column prop="updatedDateTime" :label="$t('supplyPlan.info.updatedDateTime')"></el-table-column>
    </el-table>

    <!-- 支給計画表 -->
    <h2>{{ $t("supplyPlan.plan.tableTitle") }}</h2>
    <table class="plan-table">
      <!-- Header row -->
      <tr class="week-header">
        <td></td>
        <td>{{ $t("supplyPlan.plan.lastMonthRemaining") }}</td>
        <td>{{ $t("supplyPlan.plan.monthlyTotal") }}</td>
        <td v-for="(weekVal, index) in planData.weeks">{{ $t(("supplyPlan.plan.week" + (index + 1))) }}</td>
      </tr>
      <tr class="plan">
        <td>{{ $t("supplyPlan.plan.planAmount") }}</td>
        <td>{{ planData.lastMonthRemaining || '' }}</td>
        <td>{{ planData.monthlyTotal || '' }}</td>
        <td v-for="(weekVal) in planData.weeks">{{ weekVal || '' }}</td>
      </tr>
      <tr class="actual">
        <td>{{ $t("supplyPlan.plan.actualAmount") }}</td>
        <td>{{ actualData.lastMonthRemaining || '' }}</td>
        <td>{{ actualData.monthlyTotal || '' }}</td>
        <td v-for="(weekVal) in actualData.weeks">{{ weekVal || '' }}</td>
      </tr>

      <template v-for="(week, index) in weeksData">
        <tr class="week-header">
          <td></td>
          <td>{{ $t("supplyPlan.plan.days.mon") }}</td>
          <td>{{ $t("supplyPlan.plan.days.tue") }}</td>
          <td>{{ $t("supplyPlan.plan.days.wed") }}</td>
          <td>{{ $t("supplyPlan.plan.days.thu") }}</td>
          <td>{{ $t("supplyPlan.plan.days.fri") }}</td>
          <td>{{ $t("supplyPlan.plan.days.sat") }}</td>
          <td>{{ $t("supplyPlan.plan.days.sun") }}</td>
          <td v-if="planData.weeks.length > 5"></td>
        </tr>
        <tr>
          <td :key="index">{{ $t("supplyPlan.plan.week" + (index + 1)) }}</td>
          <td v-for="i in 7" :key="`day-${i}`">
            <span v-if="week.days.find(day => day.dayOfWeek === (i === 7 ? 0 : i))">
              {{ week.days.find(day => day.dayOfWeek === (i === 7 ? 0 : i)).date }}
            </span>
          </td>
          <td v-if="planData.weeks.length > 5"></td>
        </tr>
        <tr class="plan">
          <td>{{ $t("supplyPlan.plan.planAmount") }}</td>
          <td v-for="i in 7" :key="i">
            <span v-if="week.days.find((day) => day.dayOfWeek === i % 7)">
              {{ week.days.find((day) => day.dayOfWeek === i % 7).plan || '' }}
            </span>
          </td>
          <td v-if="planData.weeks.length > 5"></td>
        </tr>
        <tr class="actual">
          <td>{{ $t("supplyPlan.plan.actualAmount") }}</td>
          <td v-for="i in 7" :key="i">
            <span v-if="week.days.find((day) => day.dayOfWeek === i % 7)">
              {{ week.days.find((day) => day.dayOfWeek === i % 7).actual || '' }}
            </span>
          </td>
          <td v-if="planData.weeks.length > 5"></td>
        </tr>
      </template>
    </table>
  </div>
</template>

<script>
import { fetchFormOptions, querySupplyPlan, printTxt } from "@/api/datamanage/supplyPlan";
import dayjs from "dayjs";
import { treeselect } from "@/api/system/dept";
import ElSelectTree from "el-select-tree";


export default {
  name: "SupplyPlan",
  components: { ElSelectTree },
  data() {
    return {
      client: "",
      depot: "",
      factory: "",
      partNumber: "",
      selectedYearMonth: dayjs().format("YYYY-MM"),
      additionalInfo: {
        client: "",
        depot: "",
        factory: "",
        partNumber: "",
        monthlyRequirement: {
          NMonth: "",
          N1Month: "",
          N2Month: "",
        },
        monthlyPlan: "",
        dailyPlan: "",
        deliveryMethod: "",
        supplyType: "",
        supplyMethod: "",
        deliveryLocation: "",
        basicUnit: "",
        orderUnit: "",
        allocationLot: "",
        currentYearMonth: "",
        clientInventory: "",
        confirmedDate: "",
        updatedDateTime: "",
      },
      planData: {
        lastMonthRemaining: "",
        monthlyTotal: "",
        weeks: []
      },
      actualData: {
        lastMonthRemaining: "",
        monthlyTotal: "",
        weeks: []
      },
      weeksData: [],
      factoryOptions: [],
      depotOptions: [],
      clientOptions: [],
      pageSize: 1,
      pageNum: 1,
      total: 0,
      supplierTreeData: [],
      printTxtLoading: false
    };
  },
  created() {
    this.getDicts('supply_plan_depot').then(response => {
      this.depotOptions = response.data
    })
    this.getDicts('supply_plan_required_factory').then(response => {
      this.factoryOptions = response.data
    })
    this.getSupplierTree();
  },

  computed: {
    // 判断是否为固定仓库的运输商
    isCarrierWithDepot() {
      console.log(this.$store.state.user)
      const roles = this.$store.state.user.roles;
      const remark = this.$store.state.user.remark;
      return roles && roles.some(role => role === 'carrier') && remark;
    }
  },
  methods: {

    handleCheckChange(data, checked, indeterminate) {
      if (checked) {
        this.client = data.code
      } else {
        this.client = ''
      }
    },
    getSupplierTree() {
      treeselect().then((res) => {
        this.supplierTreeData = res.data;
        this.client = this.supplierTreeData[0].code;
        this.query();
      })
    },
    handlePageChange(page) {
      this.pageNum = page;
      this.query();
    },

    async query() {
      try {
        const requestData = {
          selectedYearMonth: this.selectedYearMonth,
          client: this.client,
          depot: this.depot,
          factory: this.factory,
          partNumber: this.partNumber,
          pageSize: this.pageSize,
          pageNum: this.pageNum,
        };
        const compact = {
          ...Object.fromEntries(
            Object.entries({
              ...requestData
            }).filter(
              ([key, value]) =>
                value !== "" && // 非空字符串
                value !== null && // 非null
                value !== undefined && // 非undefined
                !(typeof value === "object" && !Array.isArray(value) && Object.keys(value).length === 0) // 非空对象
            )
          )
        }

        const response = await querySupplyPlan(compact);
        if (response.code === 200) {
          const data = Array.isArray(response.rows) ? response.rows[0] : response.rows;
          if (data) {
            const { basicInfo, weeksData } = data;
            this.additionalInfo = basicInfo || {};
            this.weeksData = weeksData || [];
            const { planData, actualData } = this.transformData(data)
            this.planData = planData || {};
            this.actualData = actualData || {};
            this.total = response.total || 0;
          } else {
            this.additionalInfo = {};
            this.planData = {};
            this.actualData = {};
            this.weeksData = [];
            this.total = 0;
          }
        }
      } catch (error) {
        console.error("Query data error:", error);
      }
    },

    // 重置表单
    resetQuery() {
      this.client = "";
      this.depot = "";
      this.factory = "";
      this.partNumber = "";
      this.pageNum = 1;
      this.pageSize = 1;
      this.total = 0;
      this.query();
    },
    transformData(data) {
      // Initialize planData and actualData structures
      const planData = {
        lastMonthRemaining: (data.planData && data.planData.lastMonthRemaining) || 0,
        monthlyTotal: 0,
        weeks: []
      };

      const actualData = {
        lastMonthRemaining: (data.actualData && data.actualData.lastMonthRemaining) || 0,
        monthlyTotal: 0,
        weeks: []
      };

      // Loop through each week's data in weeksData
      data.weeksData.forEach((weekData) => {
        let weeklyPlanTotal = 0;
        let weeklyActualTotal = 0;

        // Accumulate daily plan and actual values for the week
        weekData.days.forEach(day => {
          weeklyPlanTotal += day.plan || 0;
          weeklyActualTotal += day.actual || 0;
        });

        // Add weekly total to weeks array in planData and actualData
        planData.weeks.push(weeklyPlanTotal);
        actualData.weeks.push(weeklyActualTotal);

        // Add weekly totals to monthly totals
        planData.monthlyTotal += weeklyPlanTotal;
        actualData.monthlyTotal += weeklyActualTotal;
      });

      return { planData, actualData };
    },
    handlePrintTxt() {
      this.printTxtLoading = true;

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close();
      }, 15000);

      const requestData = {
          selectedYearMonth: this.selectedYearMonth,
          client: this.client,
          depot: this.depot,
          factory: this.factory,
          partNumber: this.partNumber,
          pageSize: this.pageSize,
          pageNum: this.pageNum,
        };
        const compact = {
          ...Object.fromEntries(
            Object.entries({
              ...requestData
            }).filter(
              ([key, value]) =>
                value !== "" && // 非空字符串
                value !== null && // 非null
                value !== undefined && // 非undefined
                !(typeof value === "object" && !Array.isArray(value) && Object.keys(value).length === 0) // 非空对象
            )
          )
        }
      printTxt(compact).then(response => {
        // 通过 window.open 打开 PDF
        window.open(process.env.VUE_APP_BASE_API + "/common/download?fileName=" + encodeURI(response.msg) + "&delete=true");
        loading?.close()
        this.printTxtLoading = false;
      }).catch(() => {
        loading?.close()
        this.printTxtLoading = false;  // 如果请求失败，重置 loading 状态

      });
    },
  },
};
</script>
<style scoped>
.app-container {
  padding: 20px;
}

.card-header {
  text-align: center;
  margin-bottom: 20px;
}

.plan-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.plan-table th,
.plan-table td {
  border: 1px solid #aaa;
  padding: 8px;
  text-align: center;
}

.plan-table th {
  background-color: #d0e4fe;
}

.plan {
  background-color: #f9f9f9;
}

.actual {
  background-color: #fff;
}

.week-header {
  background-color: #d0e4fe;
  font-weight: bold;
}

.text-center {
  text-align: center;
}

h3 {
  margin-top: 20px;
}

.custom-pagination {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  overflow: auto;
}
</style>
