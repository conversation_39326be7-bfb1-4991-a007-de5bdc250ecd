<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblEventMapper">
    
    <resultMap type="TblEvent" id="TblEventResult">
        <result property="eventId"    column="Event_ID"    />
        <result property="eventType"    column="Event_Type"    />
        <result property="content" column="Content" typeHandler="com.datalink.json.handler.JsonObjectNodeTypeHandler" />
        <result property="serviceName"    column="Service_Name"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="methodName"    column="Method_Name"    />
        <result property="createBy"    column="Create_By"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
    </resultMap>

    <sql id="selectTblEventVo">
        select Event_ID, Event_Type, Content, Service_Name, Create_Time, Method_Name, Create_By, Comp_Code, Update_Time, Supp_Code, Update_By, Supp_Name, Direction, Kafka_Status from tbl_event
    </sql>

    <select id="selectTblEventList" parameterType="TblEvent" resultMap="TblEventResult">
        <include refid="selectTblEventVo"/>
        <where>  
            <if test="eventType != null  and eventType != ''"> and Event_Type = #{eventType}</if>
            <if test="content != null  and content != ''"> and Content = #{content,typeHandler=com.datalink.json.handler.JsonObjectNodeTypeHandler}</if>
            <if test="serviceName != null  and serviceName != ''"> and Service_Name like concat('%', #{serviceName}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and Create_Time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="methodName != null  and methodName != ''"> and Method_Name like concat('%', #{methodName}, '%')</if>
            <if test="createBy != null  and createBy != ''"> and Create_By = #{createBy}</if>
            <if test="compCode != null  and compCode != ''"> and Comp_Code = #{compCode}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and Update_Time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code = #{suppCode}</if>
            <if test="updateBy != null  and updateBy != ''"> and Update_By = #{updateBy}</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
        </where>
    </select>
    
    <select id="selectTblEventById" parameterType="Long" resultMap="TblEventResult">
        <include refid="selectTblEventVo"/>
        where Event_ID = #{eventId}
    </select>
        
    <insert id="insertTblEvent" parameterType="TblEvent" useGeneratedKeys="true" keyProperty="eventId">
        insert into tbl_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="eventType != null">Event_Type,</if>
            <if test="content != null">Content,</if>
            <if test="serviceName != null">Service_Name,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="methodName != null">Method_Name,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="suppName != null and suppName != ''">Supp_Name,</if>
            <if test="direction != null and direction != ''">Direction,</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="eventType != null">#{eventType},</if>
            <if test="content != null">#{content,typeHandler=com.datalink.json.handler.JsonObjectNodeTypeHandler},</if>
            <if test="serviceName != null">#{serviceName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="methodName != null">#{methodName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">#{kafkaStatus},</if>
         </trim>
    </insert>

    <update id="updateTblEvent" parameterType="TblEvent">
        update tbl_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="eventType != null">Event_Type = #{eventType},</if>
            <if test="content != null">Content = #{content,typeHandler=com.datalink.json.handler.JsonObjectNodeTypeHandler},</if>
            <if test="serviceName != null">Service_Name = #{serviceName},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="methodName != null">Method_Name = #{methodName},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="direction != null and direction != ''">Direction = #{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status = #{kafkaStatus},</if>
        </trim>
        where Event_ID = #{eventId}
    </update>

    <delete id="deleteTblEventById" parameterType="Long">
        delete from tbl_event where Event_ID = #{eventId}
    </delete>

    <delete id="deleteTblEventByIds" parameterType="String">
        delete from tbl_event where Event_ID in 
        <foreach item="eventId" collection="array" open="(" separator="," close=")">
            #{eventId}
        </foreach>
    </delete>
</mapper>