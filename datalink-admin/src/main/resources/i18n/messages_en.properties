#????
not.null=* must be filled in
user.jcaptcha.error=Incorrect verification code
user.jcaptcha.expire=Verification code has expired
user.not.exists=User does not exist/password error
user.password.not.match=User does not exist/password error
user.password.retry.limit.count=Wrong password entered {0} times
user.password.retry.limit.exceed=Wrong password entered {0} times, account locked for 10 minutes
user.password.delete=Sorry, your account has been deleted
user.blocked=User has been banned. Please contact the administrator.
role.blocked=Role has been banned. Please contact the administrator.
user.logout.success=Logout successful.

length.not.valid=The length must be between {min} and {max} characters.

user.username.not.valid=* Consists of 2 to 20 Chinese characters, letters, numbers or underscores, and must start with a non-digit.
user.password.not.valid=* 5-50 characters.

user.email.not.valid=Incorrect email format.
user.mobile.phone.number.not.valid=Incorrect mobile phone number format.
user.login.success=Login successful.
user.notfound=Please log in again.
user.forcelogout=Administrator forced logout. Please log in again.
user.unknown.error=Unknown error. Please log in again.

##??????
upload.exceed.maxSize=The uploaded file size exceeds the limit! <br/>The maximum allowed file size is: {0}MB!
upload.filename.exceed.length=The uploaded file name can be at most {0} characters long.

##??
no.permission=You do not have permission for the data. Please contact the administrator to add permission [{0}].
no.create.permission=You do not have permission to create data. Please contact the administrator to add permission [{0}].
no.update.permission=You do not have permission to modify data. Please contact the administrator to add permission [{0}].
no.delete.permission=You do not have permission to delete data. Please contact the administrator to add permission [{0}].
no.export.permission=You do not have permission to export data. Please contact the administrator to add permission [{0}].
no.view.permission=You do not have permission to view data. Please contact the administrator to add permission [{0}].

forecast.week.no.data=No weekly forecast data found
forecast.month.no.data=No three-month forecast data found
forecast.year.no.data=No annual forecast data found

supply.plan.no.data=Supply plan data not found
supply.plan.yearmonth.empty=Year/Month cannot be empty

order.is.complete=Order {} has already been completed. Creating a new ASN is not allowed
order.is.not.confirmed=Order {} has not been confirmed. Creating a new ASN is not allowed
pack.count.max=Order {} has too many boxes. A maximum of 999 is supported
asn.is.sent=ASN[{}] has been sent. Duplicate operations are not allowed
asn.not.exists=ASN[{}] does not exist. Please refresh the page and try again
asn.code.gen.error=ASN code generation failed. Please try again later.
sap.asn.error=SAP system returned error [{}]. Please contact the administrator.