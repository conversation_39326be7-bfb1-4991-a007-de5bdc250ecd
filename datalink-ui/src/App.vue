<template>
  <div id="app">
    <!-- 加载动画 -->
    <div id="loader-wrapper" v-if="isLoading">
      <div id="loader"></div>
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
      <div class="load_title">{{ loadingText }}</div>
    </div>

    <!-- 其他的 Vue 内容 -->
    <router-view />
  </div>
</template>

<script>
export default {
  data() {
    return {
      isLoading: true, // 控制加载动画的显示
      loadingText: this.$t('loadingMessage') // 加载的国际化文本
    };
  },
  mounted() {
    setTimeout(() => {
      this.isLoading = false; // 加载完成后隐藏加载动画
    }, 100); // 模拟3秒的加载时间
  }
}
</script>

<style scoped>
/* 将之前的 loader 样式放在这里 */
#loader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
}

#loader {
  display: block;
  position: relative;
  left: 50%;
  top: 50%;
  width: 150px;
  height: 150px;
  margin: -75px 0 0 -75px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #fff;
  animation: spin 2s linear infinite;
  z-index: 1001;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loader-section {
  position: fixed;
  top: 0;
  width: 51%;
  height: 100%;
  background: #7171c6;
  z-index: 1000;
}

.loader-section.section-left {
  left: 0;
}

.loader-section.section-right {
  right: 0;
}

.load_title {
  font-family: "Open Sans";
  color: #fff;
  font-size: 19px;
  width: 100%;
  text-align: center;
  position: absolute;
  top: 60%;
}
::v-deep .el-table .cell {
  white-space: pre-wrap;
}
</style>
