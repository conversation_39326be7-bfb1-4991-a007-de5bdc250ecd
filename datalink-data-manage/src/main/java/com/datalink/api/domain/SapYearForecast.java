package com.datalink.api.domain;

import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class SapYearForecast extends BaseEntity {
    private Long id;
    private String flief;    // 取引先
    private String zdepot;   // デポ
    private String name1;    // 取引先会社名
    private String isRead;   // 是否已读(Y/N)
    private String zmonth1;
    private String zmonth2;
    private String zmonth3;
    private String zmonth4;
    private String zmonth5;
    private String zmonth6;
    private String zmonth7;
    private String zmonth8;
    private String zmonth9;
    private String zmonth10;
    private String zmonth11;
    private String zmonth12;

    private List<SapYearForecastItem> subItems;
}