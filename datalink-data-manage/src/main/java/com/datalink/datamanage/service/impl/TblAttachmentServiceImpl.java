package com.datalink.datamanage.service.impl;

import com.datalink.common.utils.DateUtils;
import com.datalink.datamanage.domain.TblAttachment;
import com.datalink.datamanage.mapper.TblAttachmentMapper;
import com.datalink.datamanage.service.ITblAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-01-24
 */
@Service
public class TblAttachmentServiceImpl implements ITblAttachmentService
{
    @Autowired
    private TblAttachmentMapper tblAttachmentMapper;

    /**
     * 查询附件
     *
     * @param attachmentId 附件ID
     * @return 附件
     */
    @Override
    public TblAttachment selectTblAttachmentById(Long attachmentId)
    {
        return tblAttachmentMapper.selectTblAttachmentById(attachmentId);
    }

    /**
     * 查询附件列表
     *
     * @param tblAttachment 附件
     * @return 附件
     */
    @Override
    public List<TblAttachment> selectTblAttachmentList(TblAttachment tblAttachment)
    {
        return tblAttachmentMapper.selectTblAttachmentList(tblAttachment);
    }

    /**
     * 新增附件
     *
     * @param tblAttachment 附件
     * @return 结果
     */
    @Override
    public int insertTblAttachment(TblAttachment tblAttachment)
    {
        tblAttachment.setCreateTime(DateUtils.getNowDate());
        return tblAttachmentMapper.insertTblAttachment(tblAttachment);
    }

    /**
     * 修改附件
     *
     * @param tblAttachment 附件
     * @return 结果
     */
    @Override
    public int updateTblAttachment(TblAttachment tblAttachment)
    {
        tblAttachment.setUpdateTime(DateUtils.getNowDate());
        return tblAttachmentMapper.updateTblAttachment(tblAttachment);
    }

    /**
     * 批量删除附件
     *
     * @param attachmentIds 需要删除的附件ID
     * @return 结果
     */
    @Override
    public int deleteTblAttachmentByIds(Long[] attachmentIds)
    {
        return tblAttachmentMapper.deleteTblAttachmentByIds(attachmentIds);
    }

    /**
     * 删除附件信息
     *
     * @param attachmentId 附件ID
     * @return 结果
     */
    @Override
    public int deleteTblAttachmentById(Long attachmentId)
    {
        return tblAttachmentMapper.deleteTblAttachmentById(attachmentId);
    }
}
