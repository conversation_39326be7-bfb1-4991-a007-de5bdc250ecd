package com.datalink.datamanage.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TransportPlan;
import com.datalink.datamanage.service.ITransportPlanService;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.common.core.page.TableDataInfo;

/**
 * 货量提示Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/datamanage/loadProposal")
public class TransportPlanController extends BaseController
{
    @Autowired
    private ITransportPlanService transportPlanService;

    /**
     * 查询货量提示列表
     */
    @PreAuthorize("@ss.hasAnyPermi('loadProposalVehicleRegistration:list,loadProposalVehicleConfirmation:list')")
    @GetMapping("/list")
    public TableDataInfo list(TransportPlan transportPlan, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        startPage();
        List<TransportPlan> list = transportPlanService.selectTransportPlanList(transportPlan, tz);
        return getDataTable(list);
    }

    /**
     * 导出货量提示列表
     */
//    @PreAuthorize("@ss.hasPermi('datamanage:loadProposal:export')")
    @Log(title = "货量提示", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TransportPlan transportPlan, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        List<TransportPlan> list = transportPlanService.selectTransportPlanList(transportPlan, tz);
        ExcelUtil<TransportPlan> util = new ExcelUtil<TransportPlan>(TransportPlan.class);
        return util.exportExcel(list, "货量提示数据");
    }

    /**
     * 获取货量提示详细信息
     */
    @PreAuthorize("@ss.hasAnyPermi('loadProposalVehicleRegistration:query,loadProposalVehicleConfirmation:query')")
    @GetMapping(value = "/{transportPlanId}")
    public AjaxResult getInfo(@PathVariable("transportPlanId") Long transportPlanId, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        return AjaxResult.success(transportPlanService.selectTransportPlanById(transportPlanId, tz));
    }

    /**
     * 新增货量提示
     */
    @PreAuthorize("@ss.hasPermi('loadProposalVehicleRegistration:add')")
    @Log(title = "货量提示", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransportPlan transportPlan, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        return toAjax(transportPlanService.insertTransportPlan(transportPlan, tz));
    }

    /**
     * 修改货量提示
     */
    @PreAuthorize("@ss.hasAnyPermi('loadProposalVehicleRegistration:edit,loadProposalVehicleConfirmation:edit')")
    @Log(title = "货量提示", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransportPlan transportPlan, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        return toAjax(transportPlanService.updateTransportPlan(transportPlan, tz));
    }

    /**
     * 删除货量提示
     */
    @PreAuthorize("@ss.hasPermi('loadProposalVehicleRegistration:remove')")
    @Log(title = "货量提示", businessType = BusinessType.DELETE)
	@DeleteMapping("/{transportPlanIds}")
    public AjaxResult remove(@PathVariable Long[] transportPlanIds)
    {
        return toAjax(transportPlanService.deleteTransportPlanByIds(transportPlanIds));
    }
}
