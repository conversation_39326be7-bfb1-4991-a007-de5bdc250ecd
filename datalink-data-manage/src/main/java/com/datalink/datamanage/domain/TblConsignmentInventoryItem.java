package com.datalink.datamanage.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 寄售行项目对象 tbl_consignment_inventory_item
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
public class TblConsignmentInventoryItem implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 寄售行项目ID */
    private Long itemId;

    /** 物料编码 */
    @Excel(name = "物料编码")
    @JsonAlias("articleno")
    @NotEmpty(message = "物料编码不能为空")
    @Size(max = 10, message = "物料编码超长")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    @JsonAlias("articlename")
    @NotEmpty(message = "物料名称不能为空")
    @Size(max = 40, message = "物料名称超长")
    private String articleName;

    /** 数量 */
    @Excel(name = "数量")
    @NotNull(message = "数量不能为空")
    private BigDecimal quantity;

    /** 天数 */
    @Excel(name = "天数")
    private BigDecimal days;

    /** 单位 */
    @Excel(name = "单位")
    @Size(max = 30, message = "单位超长")
    private String unit;

    /** 寄售ID */
    @Excel(name = "寄售ID")
    private Long consignmentId;

    /** 备注 */
    private String remark;

    /** 搜索值 */
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeSys;

    /** 请求参数 */
    private Map<String, Object> params;

    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setArticleNo(String articleNo) 
    {
        this.articleNo = articleNo;
    }

    public String getArticleNo() 
    {
        return articleNo;
    }
    public void setArticleName(String articleName) 
    {
        this.articleName = articleName;
    }

    public String getArticleName() 
    {
        return articleName;
    }
    public void setQuantity(BigDecimal quantity) 
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity() 
    {
        return quantity;
    }
    public void setDays(BigDecimal days) 
    {
        this.days = days;
    }

    public BigDecimal getDays() 
    {
        return days;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setConsignmentId(Long consignmentId) 
    {
        this.consignmentId = consignmentId;
    }

    public Long getConsignmentId() 
    {
        return consignmentId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTimeSys() {
        return updateTimeSys;
    }

    public void setUpdateTimeSys(Date updateTimeSys) {
        this.updateTimeSys = updateTimeSys;
    }

    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("quantity", getQuantity())
            .append("days", getDays())
            .append("unit", getUnit())
            .append("remark", getRemark())
            .append("consignmentId", getConsignmentId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTimeSys())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
