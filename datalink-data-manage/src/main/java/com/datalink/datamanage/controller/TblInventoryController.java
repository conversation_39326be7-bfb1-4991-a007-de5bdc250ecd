package com.datalink.datamanage.controller;

import com.datalink.common.annotation.DataScope;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblInventory;
import com.datalink.datamanage.domain.TblInventoryItem;
import com.datalink.datamanage.service.ITblInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 寄售库存Controller
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
@RestController
@RequestMapping("/datamanage/inventory")
public class TblInventoryController extends BaseController
{
    @Autowired
    private ITblInventoryService tblInventoryService;

    /**
     * 查询库存列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(TblInventory tblInventory)
    {
        startPage();
        List<TblInventory> list = tblInventoryService.selectTblInventoryList(tblInventory);
        return getDataTable(list);
    }

    /**
     * 导出库存列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:export')")
    @Log(title = "库存", businessType = BusinessType.EXPORT)
    @DataScope(supplierAlias = "a")
    @GetMapping("/export")
    public AjaxResult export(TblInventory tblInventory)
    {
        List<TblInventory> list = tblInventoryService.selectTblInventoryList(tblInventory);
        ExcelUtil<TblInventory> util = new ExcelUtil<TblInventory>(TblInventory.class);
        return util.exportExcel(list, "寄售库存数据");
    }

    /**
     * 获取库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:query')")
    @GetMapping(value = "/{Id}")
    public AjaxResult getInfo(@PathVariable("Id") Long Id)
    {
        return AjaxResult.success(tblInventoryService.selectTblInventoryById(Id));
    }

    /**
     * 新增库存
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:add')")
    @Log(title = "库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblInventory tblInventory)
    {
        return toAjax(tblInventoryService.insertTblInventory(tblInventory));
    }

    /**
     * 修改寄售库存
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:edit')")
    @Log(title = "库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblInventory tblInventory)
    {
        return toAjax(tblInventoryService.updateTblInventory(tblInventory));
    }

    /**
     * 删除寄售库存
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:remove')")
    @Log(title = "库存", businessType = BusinessType.DELETE)
	@DeleteMapping("/{Ids}")
    public AjaxResult remove(@PathVariable Long[] Ids)
    {
        return toAjax(tblInventoryService.deleteTblInventoryByIds(Ids));
    }

    /**
     * 查询库存行项目列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:list')")
    @GetMapping("/listItems")
    public TableDataInfo listItems(TblInventoryItem tblInventoryItem)
    {
        startPage();
        List<TblInventoryItem> list = tblInventoryService.selectTblInventoryItemList(tblInventoryItem);
        return getDataTable(list);
    }

    /**
     * 获取库存详细信息(不包含行项目)
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:query')")
    @GetMapping(value = "/head/{Id}")
    public AjaxResult getInventoryOnly(@PathVariable("Id") Long Id)
    {
        return AjaxResult.success(tblInventoryService.selectTblInventoryOnlyById(Id));
    }
}
