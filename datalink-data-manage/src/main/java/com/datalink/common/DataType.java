package com.datalink.common;

public enum DataType {
    PO_TYPE {
        @Override
        public String toString() {
            return DataConstants.PO_TYPE;
        }
    },
    CONSIGNMENT_TYPE {
        @Override
        public String toString() {
            return DataConstants.CONSIGNMENT_TYPE;
        }
    },
    FORECAST_TYPE {
        @Override
        public String toString() {
            return DataConstants.FORECAST_TYPE;
        }
    },
    ASN_TYPE {
        @Override
        public String toString() {
            return DataConstants.ASN_TYPE;
        }
    },
    FEEDBACK_TYPE {
        @Override
        public String toString() {
            return DataConstants.FEEDBACK_TYPE;
        }
    },
    INVENTORY_TYPE {
        @Override
        public String toString() {
            return DataConstants.INVENTORY_TYPE;
        }
    },
    EVENT_TYPE {
        @Override
        public String toString() { return DataConstants.EVENT_TYPE; }
    },
    NOTICE_TYPE{
        @Override
        public String toString() { return DataConstants.NOTICE_TYPE; }
    },
    SA_TYPE{
        @Override
        public String toString() { return DataConstants.SA_TYPE; }
    }
}
