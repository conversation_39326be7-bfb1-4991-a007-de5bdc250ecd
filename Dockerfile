FROM redis:latest@sha256:c2cbe8a592927bb74033f9c29b103ebc8e1ab3ed9598a9e937aaa2a723d5b8a7
LABEL maintainer="orangebox.datalink"
WORKDIR /usr/local
RUN mkdir -p /usr/local/java
ADD jre.tar.gz /usr/local/java
COPY datalink-admin/target/datalink-admin.jar ./
COPY entrypoint.sh ./
EXPOSE 8080
# 设置容器内 JAVA_HOME 环境变量
ENV JAVA_HOME /usr/local/java/jre1.8.0_202
ENV PATH $PATH:$JAVA_HOME/bin
ENV REDIS_HOST 127.0.0.1
ENV REDIS_PORT 6379
ENV MYSQL_HOST 127.0.0.1
ENV MYSQL_PORT 3306
ENV MYSQL_USER datalink
ENV MYSQL_PASS P@ss1234
ENV MYSQL_SCHEMA datalink
ENV APP_PORT 8080
ENV KAFKA_TOPIC editest
ENV KAFKA_PARTITION 0
ENV KAFKA_URL ***********:9092
ENV KAFKA_GROUP editestgroup
ENV SHARE_FOLDER /usr/local/data/share
ENV NAME datalink
ENTRYPOINT ["sh", "./entrypoint.sh"]
