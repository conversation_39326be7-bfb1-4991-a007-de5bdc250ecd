package com.datalink.api.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 支给计划对象 tbl_sap_delivery_plan
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SapDeliveryPlan extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long deliveryPlanId;
    // 取引先
    private String client;
    // デポ
    private String depot;
    // 要求工場
    private String factory;
    // 部品番号
    private String partNumber;
    // 製品区分
    private String productCategory;
    // 納入方式
    private String deliveryMethod;
    // 納入場所
    private String deliveryLocation;
    // 手配ロット
    private String allocationLot;
    // 支給区分
    private String supplyType;
    // 基本数量単位
    private String basicUnit;
    // SNEP
    private String snep;
    // 支給方式
    private String supplyMethod;
    // 発注単位
    private String orderUnit;
    // 単位条件
    private String unitCondition;
    // 作成年月日
    private String creationDate;
    // 取引先在庫
    private String clientInventory;
    // 確定済み年月日
    private String confirmedDate;
    // 更新年月日－時分
    private String updatedDateTime;

    /** 选择的年月 */
    @JsonFormat(pattern = "yyyy-MM")
    private String selectedYearMonth;

    /** 供应计划明细信息 */
    private List<SapDeliveryPlanDetail> sapDeliveryPlanDetailList;

}
