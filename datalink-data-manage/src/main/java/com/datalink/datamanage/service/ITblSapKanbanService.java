package com.datalink.datamanage.service;

import java.util.List;

import com.datalink.common.core.domain.AjaxResult;
import com.datalink.datamanage.domain.TblSapKanbanKd;
import com.datalink.datamanage.domain.TblSapKanbanSv;

/**
 * 看板KDService接口
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
public interface ITblSapKanbanService
{
    /**
     * 查询看板KD
     *
     * @param id 看板KDID
     * @return 看板KD
     */
    public TblSapKanbanKd selectTblSapKanbanKdById(Long id);

    /**
     * 查询看板LINE
     *
     * @param id 看板LINEID
     * @return 看板LINE
     */
    public TblSapKanbanKd selectTblSapKanbanLineById(Long id);

    /**
     * 查询看板SV
     *
     * @param id 看板SVID
     * @return 看板SV
     */
    public TblSapKanbanSv selectTblSapKanbanSvById(Long id);

    /**
     * 查询看板KD列表
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 看板KD集合
     */
    public List<TblSapKanbanKd> selectTblSapKanbanKdList(TblSapKanbanKd tblSapKanbanKd);

    /**
     * 新增看板KD
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 结果
     */
    public int insertTblSapKanbanKd(TblSapKanbanKd tblSapKanbanKd);

    /**
     * 修改看板KD
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 结果
     */
    public int updateTblSapKanbanKd(TblSapKanbanKd tblSapKanbanKd);

    /**
     * 批量删除看板KD
     * 
     * @param fileNos 需要删除的看板KDID
     * @return 结果
     */
    public int deleteTblSapKanbanKdByIds(String[] fileNos);

    /**
     * 删除看板KD信息
     * 
     * @param fileNo 看板KDID
     * @return 结果
     */
    public int deleteTblSapKanbanKdById(String fileNo);

    /**
     * 新增看板LINE
     *
     * @param tblSapKanbanLine 看板LINE
     * @return 结果
     */
    public int insertTblSapKanbanLine(TblSapKanbanKd tblSapKanbanLine);

    /**
     * 新增看板SV
     *
     * @param tblSapKanbanSv 看板SV
     * @return 结果
     */
    public int insertTblSapKanbanSv(TblSapKanbanSv tblSapKanbanSv);

    /**
     * 批量新增看板KD
     *
     * @param list 看板KD列表
     * @return 结果
     */
    public int batchInsertTblSapKanbanKd(List<TblSapKanbanKd> list);

    /**
     * 批量新增看板LINE
     *
     * @param list 看板LINE列表
     * @return 结果
     */
    public int batchInsertTblSapKanbanLine(List<TblSapKanbanKd> list);

    /**
     * 批量新增看板SV
     *
     * @param list 看板SV列表
     * @return 结果
     */
    public int batchInsertTblSapKanbanSv(List<TblSapKanbanSv> list);

    List<TblSapKanbanKd> selectTblSapKanbanLineList(TblSapKanbanKd tblSapKanbanLine);

    List<TblSapKanbanSv> selectTblSapKanbanSvList(TblSapKanbanSv tblSapKanbanSv);

    AjaxResult downloadKanbanKdTxt(List<Long> ids,  String tz);

    AjaxResult downloadKanbanLineTxt(List<Long> ids, String tz);

    AjaxResult downloadKanbanSvTxt(List<Long> ids, String tz);
}
