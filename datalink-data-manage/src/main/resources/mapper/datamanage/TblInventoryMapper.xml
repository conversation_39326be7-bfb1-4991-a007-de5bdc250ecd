<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblInventoryMapper">
    
    <resultMap type="TblInventory" id="TblInventoryResult">
        <result property="inventoryId"    column="Inventory_ID"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="updateDate"    column="Update_Date"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTimeSys"    column="Update_Time_Sys"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
    </resultMap>

    <resultMap id="TblInventoryTblInventoryItemResult" type="TblInventory" extends="TblInventoryResult">
        <collection property="detail" notNullColumn="sub_Item_ID" javaType="java.util.List" resultMap="TblInventoryItemResult" />
    </resultMap>

    <resultMap id="TblInventoryTblInventoryItemApiResult" type="TblApiInventory" extends="TblInventoryResult">
        <collection property="detail" ofType="com.datalink.api.domain.TblApiInventoryItem" select="selectTblApiInventoryItemByInventoryId" column="Inventory_ID"/>
    </resultMap>

    <resultMap type="TblInventoryItem" id="TblInventoryItemResult">
        <result property="itemId"    column="sub_Item_ID"    />
        <result property="articleNo"    column="sub_Article_No"    />
        <result property="articleName"    column="sub_Article_Name"    />
        <result property="quantity"    column="sub_Quantity"    />
        <result property="days"    column="sub_Days"    />
        <result property="unit"    column="sub_Unit"    />
        <result property="remark"    column="sub_Remark"    />
        <result property="inventoryId"    column="sub_Inventory_ID"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTimeSys"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
    </resultMap>

    <resultMap id="TblApiInventoryItemResult" type="TblApiInventoryItem" extends="TblInventoryItemResult">
    </resultMap>

    <sql id="selectTblInventoryVo">
        select Inventory_ID, Comp_Code, Plant_Code, Plant_Name, Supp_Code, Supp_Name, Update_Date, Update_Time, Create_Time, Create_By, Update_Time_Sys, Update_By, Direction, Kafka_Status from tbl_inventory a
    </sql>

    <sql id="selectTblInventoryItemVo">
        select Item_ID as sub_Item_ID, Article_No as sub_Article_No, Article_Name as sub_Article_Name, Quantity as sub_Quantity, Days as sub_Days, Unit as sub_Unit, Remark as sub_Remark, Inventory_ID as sub_Inventory_ID, Create_Time as sub_Create_Time, Create_By as sub_Create_By, Update_Time as sub_Update_Time, Update_By as sub_Update_By from tbl_inventory_item
    </sql>

    <select id="selectTblInventoryItemList" parameterType="TblInventoryItem" resultMap="TblInventoryItemResult">
        <include refid="selectTblInventoryItemVo"/>
        <where>
            <if test="articleNo != null  and articleNo != ''"> and Article_No = #{articleNo}</if>
            <if test="articleName != null  and articleName != ''"> and Article_Name like concat('%', #{articleName}, '%')</if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and Unit = #{unit}</if>
            <if test="remark != null  and remark != ''"> and Remark = #{remark}</if>
            <if test="inventoryId != null "> and Inventory_ID = #{inventoryId}</if>
            <if test="days != null"> and Days = #{days},</if>
        </where>
    </select>

    <select id="selectTblInventoryList" parameterType="TblInventory" resultMap="TblInventoryResult">
        <include refid="selectTblInventoryVo"/>
        <where>  
            <if test="compCode != null  and compCode != ''"> and Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="params.beginUpdateDate != null and params.beginUpdateDate != '' and params.endUpdateDate != null and params.endUpdateDate != ''"> and Update_Date between #{params.beginUpdateDate} and #{params.endUpdateDate}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and Update_Time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectTblInventoryWithItemList" parameterType="TblInventory" resultMap="TblInventoryTblInventoryItemResult">
        select a.Inventory_ID, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Update_Date, a.Update_Time, a.Create_Time, a.Create_By, a.Update_Time_Sys, a.Update_By, a.Direction, a.Kafka_Status,
               b.Item_ID as sub_Item_ID, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Quantity as sub_Quantity, b.Days as sub_Days, b.Unit as sub_Unit, b.Remark as sub_Remark, b.Inventory_ID as sub_Inventory_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_inventory a
                 left join tbl_inventory_item b on b.Inventory_ID = a.Inventory_ID
        <where>
            <if test="compCode != null  and compCode != ''"> and a.Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="plantCode != null  and plantCode != ''"> and a.Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and a.Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and a.Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and a.Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="params.beginUpdateDate != null and params.beginUpdateDate != '' and params.endUpdateDate != null and params.endUpdateDate != ''"> and a.Update_Date between #{params.beginUpdateDate} and #{params.endUpdateDate}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and a.Update_Time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="direction != null  and direction != ''"> and a.Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and a.Kafka_Status = #{kafkaStatus}</if>
        </where>
    </select>

    <select id="selectTblInventoryOnlyById" parameterType="Long" resultMap="TblInventoryResult">
        <include refid="selectTblInventoryVo"/>
        where Inventory_ID = #{inventoryId}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>
    
    <select id="selectTblInventoryById" parameterType="Long" resultMap="TblInventoryTblInventoryItemResult">
        select a.Inventory_ID, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Update_Date, a.Update_Time, a.Create_Time, a.Create_By, a.Update_Time_Sys, a.Update_By, a.Direction, a.Kafka_Status,
               b.Item_ID as sub_Item_ID, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Quantity as sub_Quantity, b.Days as sub_Days, b.Unit as sub_Unit, b.Remark as sub_Remark, b.Inventory_ID as sub_Inventory_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_inventory a
        left join tbl_inventory_item b on b.Inventory_ID = a.Inventory_ID
        where a.Inventory_ID = #{inventoryId}
    </select>

    <select id="selectTblApiInventoryItemByInventoryId" parameterType="Long" resultMap="TblApiInventoryItemResult">
        <include refid="selectTblInventoryItemVo"/>
        where Inventory_ID = #{inventoryId}
    </select>

    <select id="selectTblInventoryFullList" parameterType="TblInventory" resultMap="TblInventoryTblInventoryItemApiResult">
        <include refid="selectTblInventoryVo"/>
        <where>
            Direction = 'I'
            <if test="params.cursor !=null">and Inventory_ID > #{params.cursor}</if>
            <if test="params.cursorInclude !=null">and Inventory_ID >= #{params.cursorInclude}</if>
            <if test="params.time !=null">and Create_Time > #{params.time}</if>
            <if test="params.timeInclude !=null">and Create_Time >= #{params.timeInclude}</if>
        </where>
        order by Inventory_ID asc
        <if test="params.limit !=null and params.limit !=''">limit #{params.limit}</if>
    </select>
        
    <insert id="insertTblInventory" parameterType="TblInventory" useGeneratedKeys="true" keyProperty="inventoryId">
        insert into tbl_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code,</if>
            <if test="plantName != null and plantName != ''">Plant_Name,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="suppName != null and suppName != ''">Supp_Name,</if>
            <if test="updateDate != null">Update_Date,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTimeSys != null">Update_Time_Sys,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="direction != null and direction != ''">Direction,</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="plantCode != null and plantCode != ''">#{plantCode},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTimeSys != null">#{updateTimeSys},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">#{kafkaStatus},</if>
         </trim>
    </insert>

    <update id="updateTblInventory" parameterType="TblInventory">
        update tbl_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">Plant_Name = #{plantName},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="updateDate != null">Update_Date = #{updateDate},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTimeSys != null">Update_Time_Sys = #{updateTimeSys},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="direction != null and direction != ''">Direction = #{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status = #{kafkaStatus},</if>
        </trim>
        where Inventory_ID = #{inventoryId}
    </update>

    <delete id="deleteTblInventoryById" parameterType="Long">
        delete from tbl_inventory where Inventory_ID = #{inventoryId}
    </delete>

    <delete id="deleteTblInventoryByIds" parameterType="String">
        delete from tbl_inventory where Inventory_ID in 
        <foreach item="inventoryId" collection="array" open="(" separator="," close=")">
            #{inventoryId}
        </foreach>
    </delete>
    
    <delete id="deleteTblInventoryItemByInventoryIds" parameterType="String">
        delete from tbl_inventory_item where Inventory_ID in 
        <foreach item="inventoryId" collection="array" open="(" separator="," close=")">
            #{inventoryId}
        </foreach>
    </delete>

    <delete id="deleteTblInventoryItemByInventoryId" parameterType="Long">
        delete from tbl_inventory_item where Inventory_ID = #{inventoryId}
    </delete>

    <insert id="batchTblInventoryItem">
        insert into tbl_inventory_item( Article_No, Article_Name, Quantity, Days, Unit, Remark, Inventory_ID, Create_Time, Create_By, Update_Time, Update_By) values
		<foreach item="item" index="index" collection="list" separator=",">
            (  #{item.articleNo}, #{item.articleName}, #{item.quantity}, #{item.days}, #{item.unit}, #{item.remark}, #{item.inventoryId}, #{item.createTime}, #{item.createBy}, #{item.updateTimeSys}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="selectLastId" resultType="Long">
        select max(Inventory_ID) from tbl_inventory;
    </select>
</mapper>