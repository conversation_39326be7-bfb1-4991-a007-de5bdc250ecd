package com.datalink.datamanage.service.impl;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.*;

import com.datalink.common.annotation.DataScope;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.TextFileUtil;
import com.datalink.datamanage.domain.TblSapKanbanSv;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.datalink.datamanage.mapper.TblSapKanbanMapper;
import com.datalink.datamanage.domain.TblSapKanbanKd;
import com.datalink.datamanage.service.ITblSapKanbanService;

/**
 * 看板KDService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class TblSapKanbanServiceImpl implements ITblSapKanbanService
{
    @Autowired
    private TblSapKanbanMapper tblSapKanbanMapper;

    private static final Logger log = LoggerFactory.getLogger(TblSapKanbanServiceImpl.class);

    private static final int BATCH_SIZE = 500;

    /**
     * 查询看板KD
     *
     * @param id 看板KDID
     * @return 看板KD
     */
    @Override
    public TblSapKanbanKd selectTblSapKanbanKdById(Long id)
    {
        return tblSapKanbanMapper.selectTblSapKanbanKdById(id);
    }

    /**
     * 查询看板LINE
     *
     * @param id 看板LINEID
     * @return 看板LINE
     */
    @Override
    public TblSapKanbanKd selectTblSapKanbanLineById(Long id)
    {
        return tblSapKanbanMapper.selectTblSapKanbanLineById(id);
    }

    /**
     * 查询看板SV
     *
     * @param id 看板SVID
     * @return 看板SV
     */
    @Override
    public TblSapKanbanSv selectTblSapKanbanSvById(Long id)
    {
        return tblSapKanbanMapper.selectTblSapKanbanSvById(id);
    }

    /**
     * 查询看板KD列表
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 看板KD
     */
    @Override
    @DataScope(supplierAlias = "a")
    public List<TblSapKanbanKd> selectTblSapKanbanKdList(TblSapKanbanKd tblSapKanbanKd)
    {
        return tblSapKanbanMapper.selectTblSapKanbanKdList(tblSapKanbanKd);
    }

    /**
     * 新增看板KD
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 结果
     */
    @Override
    public int insertTblSapKanbanKd(TblSapKanbanKd tblSapKanbanKd)
    {
        return tblSapKanbanMapper.insertTblSapKanbanKd(tblSapKanbanKd);
    }

    /**
     * 修改看板KD
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 结果
     */
    @Override
    public int updateTblSapKanbanKd(TblSapKanbanKd tblSapKanbanKd)
    {
        return tblSapKanbanMapper.updateTblSapKanbanKd(tblSapKanbanKd);
    }

    /**
     * 批量删除看板KD
     * 
     * @param fileNos 需要删除的看板KDID
     * @return 结果
     */
    @Override
    public int deleteTblSapKanbanKdByIds(String[] fileNos)
    {
        return tblSapKanbanMapper.deleteTblSapKanbanKdByIds(fileNos);
    }

    /**
     * 删除看板KD信息
     * 
     * @param fileNo 看板KDID
     * @return 结果
     */
    @Override
    public int deleteTblSapKanbanKdById(String fileNo)
    {
        return tblSapKanbanMapper.deleteTblSapKanbanKdById(fileNo);
    }

    @Override
    public int insertTblSapKanbanLine(TblSapKanbanKd tblSapKanbanLine) {
        return tblSapKanbanMapper.insertTblSapKanbanLine(tblSapKanbanLine);
    }

    /**
     * 新增看板SV
     *
     * @param tblSapKanbanSv 看板SV
     * @return 结果
     */
    @Override
    public int insertTblSapKanbanSv(TblSapKanbanSv tblSapKanbanSv)
    {
        return tblSapKanbanMapper.insertTblSapKanbanSv(tblSapKanbanSv);
    }

    /**
     * 批量新增看板KD
     *
     * @param list 看板KD列表
     * @return 结果
     */
    @Override
    public int batchInsertTblSapKanbanKd(List<TblSapKanbanKd> list) {
        int total = 0;
        List<TblSapKanbanKd> insertList = new ArrayList<>();
        
        for (TblSapKanbanKd item : list) {
            // 查询是否存在
            TblSapKanbanKd exist = tblSapKanbanMapper.selectTblSapKanbanKdByBizKey(
                item.getDemandCode(), item.getIssueNo(), item.getCustomerPartsNo());
                
            if (exist != null) {
                // 存在则更新
                item.setId(exist.getId()); // 保持原有主键
                total += tblSapKanbanMapper.updateTblSapKanbanKd(item);
            } else {
                // 不存在则插入
                insertList.add(item);
                if (insertList.size() >= BATCH_SIZE) {
                    total += tblSapKanbanMapper.batchInsertTblSapKanbanKd(insertList);
                    insertList.clear();
                }
            }
        }
        
        // 处理剩余的插入
        if (!insertList.isEmpty()) {
            total += tblSapKanbanMapper.batchInsertTblSapKanbanKd(insertList);
        }
        
        return total;
    }

    /**
     * 批量新增看板LINE
     *
     * @param list 看板LINE列表
     * @return 结果
     */
    @Override
    public int batchInsertTblSapKanbanLine(List<TblSapKanbanKd> list) {
        int total = 0;
        List<TblSapKanbanKd> insertList = new ArrayList<>();
        
        for (TblSapKanbanKd item : list) {
            // 查询是否存在
            TblSapKanbanKd exist = tblSapKanbanMapper.selectTblSapKanbanLineByBizKey(
                item.getDemandCode(), item.getIssueNo(), item.getCustomerPartsNo());
                
            if (exist != null) {
                // 存在则更新
                item.setId(exist.getId());
                total += tblSapKanbanMapper.updateTblSapKanbanLine(item);
            } else {
                // 不存在则插入
                insertList.add(item);
                if (insertList.size() >= BATCH_SIZE) {
                    total += tblSapKanbanMapper.batchInsertTblSapKanbanLine(insertList);
                    insertList.clear();
                }
            }
        }
        
        if (!insertList.isEmpty()) {
            total += tblSapKanbanMapper.batchInsertTblSapKanbanLine(insertList);
        }
        
        return total;
    }

    /**
     * 批量新增看板SV
     *
     * @param list 看板SV列表
     * @return 结果
     */
    @Override
    public int batchInsertTblSapKanbanSv(List<TblSapKanbanSv> list) {
        int total = 0;
        List<TblSapKanbanSv> insertList = new ArrayList<>();
        
        for (TblSapKanbanSv item : list) {
            // 查询是否存在
            TblSapKanbanSv exist = tblSapKanbanMapper.selectTblSapKanbanSvByBizKey(
                item.getDemandCode(), item.getIssueNoId(), item.getCustomerPartsNo());
                
            if (exist != null) {
                // 存在则更新
                item.setId(exist.getId());
                total += tblSapKanbanMapper.updateTblSapKanbanSv(item);
            } else {
                // 不存在则插入
                insertList.add(item);
                if (insertList.size() >= BATCH_SIZE) {
                    total += tblSapKanbanMapper.batchInsertTblSapKanbanSv(insertList);
                    insertList.clear();
                }
            }
        }
        
        if (!insertList.isEmpty()) {
            total += tblSapKanbanMapper.batchInsertTblSapKanbanSv(insertList);
        }
        
        return total;
    }

    @Override
    @DataScope(supplierAlias = "a")
    public List<TblSapKanbanKd> selectTblSapKanbanLineList(TblSapKanbanKd tblSapKanbanLine) {
        return tblSapKanbanMapper.selectTblSapKanbanLineList(tblSapKanbanLine);
    }

    @Override
    @DataScope(supplierAlias = "a")
    public List<TblSapKanbanSv> selectTblSapKanbanSvList(TblSapKanbanSv tblSapKanbanSv) {
        return tblSapKanbanMapper.selectTblSapKanbanSvList(tblSapKanbanSv);
    }

    @Override
    public AjaxResult downloadKanbanKdTxt(List<Long> ids, String tz) {
        try {
            // 默认取第一条的fromWorkArea拼接文件名
            TblSapKanbanKd kanban = this.selectTblSapKanbanKdById(ids.get(0));
            // 文件名规则：WDP0230_YYYYmmddHHMMSS_00_M4028_01.txt，其中YYYYmmddHHMMSS为当前时间，M4028为fromWorkArea前5位，01为fromWorkArea后两位（为空则默认01）
            String fileName = String.format("WDP0230_%s_00_%s_%s.txt",
                    DateUtils.parseDateToStrWithTz(DateUtils.YYYYMMDDHHMMSS, new Date(), tz),
                    kanban.getFromWorkArea().substring(0, 5),
                    kanban.getFromWorkArea().substring(5).isEmpty() ? "01" : kanban.getFromWorkArea().substring(5));
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    Files.newOutputStream(file.toPath()), "Shift-JIS"))) {

                for (Long id : ids) {
                    TblSapKanbanKd kd = this.selectTblSapKanbanKdById(id);
                    if (kd == null) {
                        continue;
                    }

                    // 创建固定长度的字符数组 - 500位
                    char[] record = new char[500];
                    Arrays.fill(record, ' ');

                    fillTxtForKdAndLine(record, kd);

                    // 写入记录
                    writer.write(new String(record));
                    writer.write("\r\n");
                }

                return AjaxResult.success(fileName);
            }
        } catch (Exception e) {
            log.error("生成看板KD txt文件失败", e);
            return AjaxResult.error("生成看板KD txt文件失败：" + e.getMessage());
        }
    }

    private void fillTxtForKdAndLine(char[] record, TblSapKanbanKd kd) {
        int pos = 1;
        int length;

        length = 2;
        TextFileUtil.fillField(record, kd.getFileNo(), pos, pos + length - 1, "Ｆ／＃");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getReserve1(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getDemandCode(), pos, pos + length - 1, "要元");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, kd.getIssueNo(), pos, pos + length - 1, "発行ＮＯ");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, kd.getDeliveryInstructionDate(), pos, pos + length - 1, "納入指示年月日");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, kd.getDeliveryInstructionTime(), pos, pos + length - 1, "納入指示時分");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, kd.getMaker(), pos, pos + length - 1, "メーカー");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getDepot(), pos, pos + length - 1, "デポ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getClassification(), pos, pos + length - 1, "区分");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getDeliveryLocation(), pos, pos + length - 1, "納入場所");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getUnloadingUnit(), pos, pos + length - 1, "荷卸単位");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, kd.getCustomerPartsNo(), pos, pos + length - 1, "部品番号");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, kd.getCurrentProcessClass(), pos, pos + length - 1, "当工順");
        pos += length;

        length = 20;
        TextFileUtil.fillField(record, kd.getPartsName(), pos, pos + length - 1, "部品名称");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(kd.getDeliveryInstructionNumber() != null ?
            kd.getDeliveryInstructionNumber() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getSupplyLocation(), pos, pos + length - 1, "供給場所");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(kd.getSnep() != null ?
            kd.getSnep() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getSafetyFlag(), pos, pos + length - 1, "重保マーク");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getLabelCount(), pos, pos + length - 1, "容器数・ラベル枚数");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getManagementPic(), pos, pos + length - 1, "生担");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getIssueReason(), pos, pos + length - 1, "発行理由");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getVipFlag(), pos, pos + length - 1, "特便マーク");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, kd.getPackageCode(), pos, pos + length - 1, "荷姿コード");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getInternalExternalClass(), pos, pos + length - 1, "内外区分");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(kd.getDeliveryInstructionCount() != null ?
            kd.getDeliveryInstructionCount() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getDeliveryMethod(), pos, pos + length - 1, "納入方式");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getOrderClass(), pos, pos + length - 1, "発注区分");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getLabelId(), pos, pos + length - 1, "ラベル識別");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getDelayPreventionMakerSymbol(), pos, pos + length - 1, "遅防法メーカーシンボル");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getShiptoClass(), pos, pos + length - 1, "送り先区分");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, kd.getShiptoCode(), pos, pos + length - 1, "送り先コード");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getLabelOutput(), pos, pos + length - 1, "ラベル出力先");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getReserve2(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getLabelNoChangeMark(), pos, pos + length - 1, "ラベル変更不可マーク");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getSelfProcurementLine(), pos, pos + length - 1, "自己調達区分");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getLabelSpecification(), pos, pos + length - 1, "ラベル仕様");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getSelfProcurementKd(), pos, pos + length - 1, "自己調達区分");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getReserve3(), pos, pos + length - 1, "予備");
        pos += length;

        length = 60;
        TextFileUtil.fillField(record, kd.getKdElements(), pos, pos + length - 1, "ＫＤ諸元");
        pos += length;

        length = 10;
        TextFileUtil.fillField(record, kd.getTransitCode(), pos, pos + length - 1, "経由地コード");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, kd.getTransitDeliveryDate(), pos, pos + length - 1, "経由地納入指示年月日");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, kd.getPartsId(), pos, pos + length - 1, "部品識別");
        pos += length;

        length = 15;
        TextFileUtil.fillField(record, kd.getDeliveryTicketNo(), pos, pos + length - 1, "納品チケットＮＯ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getWithdrawalClass(), pos, pos + length - 1, "引取区分");
        pos += length;

        length = 11;
        TextFileUtil.fillNumber(record, new BigDecimal(kd.getPartPrice() != null ?
            kd.getPartPrice() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getPrototypeSymbol(), pos, pos + length - 1, "試作シンボル");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getResendSymbol(), pos, pos + length - 1, "再送シンボル");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, kd.getOrderDate(), pos, pos + length - 1, "発注年月日");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getLabelSize(), pos, pos + length - 1, "ラベルサイズ");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, kd.getReserve4(), pos, pos + length - 1, "予備");
        pos += length;

        length = 89;
        TextFileUtil.fillField(record, kd.getReserve5(), pos, pos + length - 1, "予備");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getLabelTerminalNo(), pos, pos + length - 1, "ラベル端末ＮＯ");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getListTerminalNo(), pos, pos + length - 1, "リスト端末ＮＯ");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getMakerPic(), pos, pos + length - 1, "メーカー担当");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, kd.getMakerSnp(), pos, pos + length - 1, "メーカーＳＮＰ");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getResendDepot(), pos, pos + length - 1, "再配信デポ");
        pos += length;

        length = 25;
        TextFileUtil.fillField(record, kd.getCustomerPartsNo(), pos, pos + length - 1, "部品番号");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getProductClass(), pos, pos + length - 1, "製品区分");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getProductionMethod(), pos, pos + length - 1, "生産方式");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getDeliveryMethod2(), pos, pos + length - 1, "納入方式");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getFromProcess(), pos, pos + length - 1, "ＦＲＯＭ工順");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, kd.getFromWorkArea(), pos, pos + length - 1, "ＦＲＯＭ工程");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getShippingPic(), pos, pos + length - 1, "出荷担当");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, kd.getPartsNoIdCode(), pos, pos + length - 1, "部品番号識別コード");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, kd.getInstructionDate(), pos, pos + length - 1, "指示年月日");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, kd.getInstructionTime(), pos, pos + length - 1, "指示時分");
        pos += length;

        length = 10;
        TextFileUtil.fillField(record, kd.getIssueNo(), pos, pos + length - 1, "指示ＮＯ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getProductionHandlingMethod(), pos, pos + length - 1, "生産対応方式");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, kd.getPlantCode(), pos, pos + length - 1, "工場コード");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, kd.getShippingLocation(), pos, pos + length - 1, "出荷場所");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, kd.getShippingPort(), pos, pos + length - 1, "出荷ポート");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getShippingCheckFlag(), pos, pos + length - 1, "出荷チェックフラグ");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, kd.getReserve6(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getLabelIssueAssignment(), pos, pos + length - 1, "ラベル発行指定");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getDivisionDepot(), pos, pos + length - 1, "振分デポ");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, kd.getCustomerCode(), pos, pos + length - 1, "得意先コード");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getDemandCode2(), pos, pos + length - 1, "要求元コード");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, kd.getShippingLocationCode2(), pos, pos + length - 1, "出荷場所コード２");
        pos += length;

        length = 5;
        TextFileUtil.fillNumber(record, new BigDecimal(kd.getMovementLeadTime() != null ?
                kd.getMovementLeadTime() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getRehandlingFlag(), pos, pos + length - 1, "再処理フラグ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, kd.getNoMasterFlag(), pos, pos + length - 1, "ノーマスターフラグ");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, kd.getReserve7(), pos, pos + length - 1, "予備");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, kd.getDivisionDepot2(), pos, pos + length - 1, "振分デポ");
        pos += length;
    }

    @Override
    public AjaxResult downloadKanbanLineTxt(List<Long> ids, String tz) {
        try {
            // 默认取第一条的fromWorkArea拼接文件名
            TblSapKanbanKd kanban = this.selectTblSapKanbanLineById(ids.get(0));
            // 文件名规则：WDP0210_YYYYmmddHHMMSS_00_M4028_01.txt，其中YYYYmmddHHMMSS为当前时间，M4028为fromWorkArea前5位，01为fromWorkArea后两位（为空则默认01）
            String fileName = String.format("WDP0210_%s_00_%s_%s.txt",
                    DateUtils.parseDateToStrWithTz(DateUtils.YYYYMMDDHHMMSS, new Date(), tz),
                    kanban.getFromWorkArea().substring(0, 5),
                    kanban.getFromWorkArea().substring(5).isEmpty() ? "01" : kanban.getFromWorkArea().substring(5));
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    Files.newOutputStream(file.toPath()), "Shift-JIS"))) {

                for (Long id : ids) {
                    TblSapKanbanKd line = this.selectTblSapKanbanLineById(id);
                    if (line == null) {
                        continue;
                    }

                    // 创建固定长度的字符数组 - 500位
                    char[] record = new char[500];
                    Arrays.fill(record, ' ');

                    fillTxtForKdAndLine(record, line);

                    // 写入记录
                    writer.write(new String(record));
                    writer.write("\r\n");
                }

                return AjaxResult.success(fileName);
            }
        } catch (Exception e) {
            log.error("生成看板LINE txt文件失败", e);
            return AjaxResult.error("生成看板LINE txt文件失败：" + e.getMessage());
        }
    }

    @Override
    public AjaxResult downloadKanbanSvTxt(List<Long> ids,  String tz) {
        try {
            // 默认取第一条的fromWorkArea拼接文件名
            TblSapKanbanSv kanban = this.selectTblSapKanbanSvById(ids.get(0));
            // 文件名规则：WDP0220_YYYYmmddHHMMSS_00_M4028_01.txt，其中YYYYmmddHHMMSS为当前时间，M4028为fromWorkArea前5位，01为fromWorkArea后两位（为空则默认01）
            String fileName = String.format("WDP0220_%s_00_%s_%s.txt",
                    DateUtils.parseDateToStrWithTz(DateUtils.YYYYMMDDHHMMSS, new Date(), tz),
                    kanban.getFromWorkArea().substring(0, 5),
                    kanban.getFromWorkArea().substring(5).isEmpty() ? "01" : kanban.getFromWorkArea().substring(5));
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    Files.newOutputStream(file.toPath()), "Shift-JIS"))) {

                for (Long id : ids) {
                    TblSapKanbanSv sv = this.selectTblSapKanbanSvById(id);
                    if (sv == null) {
                        continue;
                    }

                    // 创建固定长度的字符数组 - 700位
                    char[] record = new char[700];
                    Arrays.fill(record, ' ');

                    fillTxtForSv(record, sv);

                    // 写入记录
                    writer.write(new String(record));
                    writer.write("\r\n");
                }

                return AjaxResult.success(fileName);
            }
        } catch (Exception e) {
            log.error("生成看板SV txt文件失败", e);
            return AjaxResult.error("生成看板SV txt文件失败：" + e.getMessage());
        }
    }

    private void fillTxtForSv(char[] record, TblSapKanbanSv sv) {
        int pos = 1;
        int length;

        // 基本信息 (1-35)
        length = 2;
        TextFileUtil.fillField(record, sv.getFileNo(), pos, pos + length - 1, "Ｆ／＃");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getReserve1(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getDemandCode(), pos, pos + length - 1, "要元");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, sv.getIssueNoId(), pos, pos + length - 1, "発行ＮＯ");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, sv.getDeliveryInstructionYy(), pos, pos + length - 1, "納入指示日年月日時分");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, sv.getMaker(), pos, pos + length - 1, "メーカー");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDepot(), pos, pos + length - 1, "デポ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getClassification(), pos, pos + length - 1, "区分");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, sv.getDeliveryLocation(), pos, pos + length - 1, "納入場所");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, sv.getCustomerPartsNo(), pos, pos + length - 1, "部品番号");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, sv.getCurrentProcessClass(), pos, pos + length - 1, "当工順");
        pos += length;

        length = 20;
        TextFileUtil.fillField(record, sv.getPartsName(), pos, pos + length - 1, "部品名称");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getDeliveryInstructionNumber() != null ?
                sv.getDeliveryInstructionNumber() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getSupplyLocation(), pos, pos + length - 1, "供給場所");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getSnep() != null ?
                sv.getSnep() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getSafetyFlag(), pos, pos + length - 1, "重保マーク");
        pos += length;

        length = 3;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getLabelCount() != null ?
                sv.getLabelCount() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getManagementPic(), pos, pos + length - 1, "管理担当");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getIssueReason(), pos, pos + length - 1, "発行理由");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getVipFlag(), pos, pos + length - 1, "特便マーク");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, sv.getPackageCode(), pos, pos + length - 1, "荷姿コード");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getInternalExternalClass(), pos, pos + length - 1, "内外区分");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getDeliveryInstructionCount() != null ?
                sv.getDeliveryInstructionCount() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDeliveryMethod(), pos, pos + length - 1, "納入方式");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getOrderClass(), pos, pos + length - 1, "発注区分");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getReserve2(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getDelayPreventionMakerSymbol(), pos, pos + length - 1, "遅防法メーカーシンボル");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getReserve3(), pos, pos + length - 1, "予備");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, sv.getReserve4(), pos, pos + length - 1, "予備");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getReserve5(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getLabelSize(), pos, pos + length - 1, "ラベルサイズ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getLabelSpecification(), pos, pos + length - 1, "ラベル仕様");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getOrderType(), pos, pos + length - 1, "発注種類");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getCheckSpecification(), pos, pos + length - 1, "検査仕様");
        pos += length;

        length = 20;
        TextFileUtil.fillField(record, sv.getLocation(), pos, pos + length - 1, "ロケーション");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getPackClass(), pos, pos + length - 1, "包装区分");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, sv.getTradeSpecification(), pos, pos + length - 1, "商品化仕様");
        pos += length;

        length = 9;
        TextFileUtil.fillField(record, sv.getOutpackMaterialCode(), pos, pos + length - 1, "外装資材コード");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getOutpackMaterialProcClass(), pos, pos + length - 1, "外装資材調達区分");
        pos += length;

        length = 9;
        TextFileUtil.fillField(record, sv.getBindPackCode(), pos, pos + length - 1, "結束資材コード");
        pos += length;

        length = 3;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getBindUnit() != null ?
                sv.getBindUnit() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 3;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getPackUnit() != null ?
                sv.getPackUnit() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getConsistPackClass(), pos, pos + length - 1, "一貫バレチ区分");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getElementsFinishClass(), pos, pos + length - 1, "諸元設定完了区分");
        pos += length;


        // 内装資材1-9 (186-302)
        fillInpackMaterial(record, sv.getInpackCode1(), sv.getInpackProcClass1(), sv.getInpackNecessaryNumber1(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode2(), sv.getInpackProcClass2(), sv.getInpackNecessaryNumber2(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode3(), sv.getInpackProcClass3(), sv.getInpackNecessaryNumber3(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode4(), sv.getInpackProcClass4(), sv.getInpackNecessaryNumber4(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode5(), sv.getInpackProcClass5(), sv.getInpackNecessaryNumber5(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode6(), sv.getInpackProcClass6(), sv.getInpackNecessaryNumber6(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode7(), sv.getInpackProcClass7(), sv.getInpackNecessaryNumber7(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode8(), sv.getInpackProcClass8(), sv.getInpackNecessaryNumber8(), pos);
        pos += 13;
        fillInpackMaterial(record, sv.getInpackCode9(), sv.getInpackProcClass9(), sv.getInpackNecessaryNumber9(), pos);
        pos += 13;

        // モジュール関連 (303-329)
        length = 9;
        TextFileUtil.fillField(record, sv.getModuleMaterial(), pos, pos + length - 1, "モジュール資材");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, sv.getModuleUnit(), pos, pos + length - 1, "モジュール単位");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getModuleClass(), pos, pos + length - 1, "モジュール区分");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, sv.getOriginalPartNo(), pos, pos + length - 1, "オリジナル部品番号");
        pos += length;

        // 部品管理関連 (330-367)
        length = 1;
        TextFileUtil.fillField(record, sv.getPartManageClass(), pos, pos + length - 1, "部管区分");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, sv.getNewcarProgramCode(), pos, pos + length - 1, "新車展開コード");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, sv.getOriginalIssueNo(), pos, pos + length - 1, "オリジナル発行番号");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDesignChangeNo(), pos, pos + length - 1, "図面変番");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getNormalContainerCode(), pos, pos + length - 1, "標準容器コード");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, sv.getBoForecastDate(), pos, pos + length - 1, "Ｂ／Ｏ予測日");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getBoSymbol(), pos, pos + length - 1, "Ｂ／Ｏシンボル");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getTrusteeId(), pos, pos + length - 1, "受託先識別ＮＯ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getDeliveryDocType(), pos, pos + length - 1, "納品書種類");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getPackageLabelType(), pos, pos + length - 1, "荷姿ラベル種別");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getLabelIssueOrg(), pos, pos + length - 1, "ラベル発行単位");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDeliveryContainer(), pos, pos + length - 1, "納入容器");
        pos += length;

        length = 5;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getUlContainerCount() != null ?
                sv.getUlContainerCount() : "0"), pos, pos + length - 1, 0);
        pos += length;

        // アイテム枝番1-20 (368-407)
        fillItemSubno(record, sv, pos);
        pos += 40;

        // バーコード情報 (408-437)
        length = 30;
        TextFileUtil.fillField(record, sv.getBarcodeInfo(), pos, pos + length - 1, "バーコード情報");
        pos += length;

        // フリーカラム1-10 (438-477)
        fillFreeColumns(record, sv, pos);
        pos += 40;

        // ヤードから振分デポまで (478-700)
        length = 4;
        TextFileUtil.fillField(record, sv.getYard(), pos, pos + length - 1, "ヤード");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getEnlargeMark1(), pos, pos + length - 1, "拡大表示マーク１");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getEnlargeMark2(), pos, pos + length - 1, "拡大表示マーク２");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getNoDiffDeliveryClass(), pos, pos + length - 1, "異数納入不可区分");
        pos += length;

        length = 6;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getUlSnp() != null ?
                sv.getUlSnp() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 15;
        TextFileUtil.fillField(record, sv.getDeliveryTicketNo(), pos, pos + length - 1, "納品チケットＮＯ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getWithdrawalClass(), pos, pos + length - 1, "引取区分");
        pos += length;

        length = 9;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getPartPrice() != null ?
                sv.getPartPrice() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getPrototypeSymbol(), pos, pos + length - 1, "試作シンボル");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getResendSymbol(), pos, pos + length - 1, "再送シンボル");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, sv.getOrderDate(), pos, pos + length - 1, "発注年月日");
        pos += length;

        length = 4;
        TextFileUtil.fillField(record, sv.getReserve6(), pos, pos + length - 1, "予備");
        pos += length;

        length = 22;
        TextFileUtil.fillField(record, sv.getReserve7(), pos, pos + length - 1, "予備");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getLabelTerminalNo(), pos, pos + length - 1, "ラベル端末ＮＯ");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getListTerminalNo(), pos, pos + length - 1, "リスト端末ＮＯ");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getMakerPic(), pos, pos + length - 1, "メーカー担当");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, sv.getMakerSnp(), pos, pos + length - 1, "メーカーＳＮＰ");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getResendDepot(), pos, pos + length - 1, "再配信デポ");
        pos += length;

        length = 25;
        TextFileUtil.fillField(record, sv.getPartsNo(), pos, pos + length - 1, "部品番号");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getProductClass(), pos, pos + length - 1, "製品区分");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getProductionMethod(), pos, pos + length - 1, "生産方式");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDeliveryMethod2(), pos, pos + length - 1, "納入方式2");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getFromProcess(), pos, pos + length - 1, "ＦＲＯＭ工順");
        pos += length;

        length = 7;
        TextFileUtil.fillField(record, sv.getFromWorkArea(), pos, pos + length - 1, "ＦＲＯＭ工程");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getShippingPic(), pos, pos + length - 1, "出荷担当");
        pos += length;

        length = 12;
        TextFileUtil.fillField(record, sv.getPartsNoIdCode(), pos, pos + length - 1, "部品番号識別コード");
        pos += length;

        length = 8;
        TextFileUtil.fillField(record, sv.getInstructionDate(), pos, pos + length - 1, "指示年月日");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, sv.getInstructionTime(), pos, pos + length - 1, "指示時分");
        pos += length;

        length = 10;
        TextFileUtil.fillField(record, sv.getInstructionNo(), pos, pos + length - 1, "指示ＮＯ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getProductionHandlingMethod(), pos, pos + length - 1, "生産対応方式");
        pos += length;

        length = 3;
        TextFileUtil.fillField(record, sv.getPlantCode(), pos, pos + length - 1, "工場コード");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, sv.getShippingLocation(), pos, pos + length - 1, "出荷場所");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, sv.getShippingPort(), pos, pos + length - 1, "出荷ポート");
        pos += length;

        length = 6;
        TextFileUtil.fillField(record, sv.getReserve8(), pos, pos + length - 1, "予備");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getLabelIssueAssignment(), pos, pos + length - 1, "ラベル発行指定");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDivisionDepot(), pos, pos + length - 1, "振分デポ");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, sv.getCustomerCode(), pos, pos + length - 1, "得意先コード");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDemandCode2(), pos, pos + length - 1, "要求元コード");
        pos += length;

        length = 5;
        TextFileUtil.fillField(record, sv.getShippingLocationCode2(), pos, pos + length - 1, "出荷場所コード２");
        pos += length;

        length = 5;
        TextFileUtil.fillNumber(record, new BigDecimal(sv.getMovementLeadTime() != null ?
                sv.getMovementLeadTime() : "0"), pos, pos + length - 1, 0);
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getRehandlingFlag(), pos, pos + length - 1, "再処理フラグ");
        pos += length;

        length = 1;
        TextFileUtil.fillField(record, sv.getNoMasterFlag(), pos, pos + length - 1, "ノーマスターフラグ");
        pos += length;

        length = 14;
        TextFileUtil.fillField(record, sv.getReserve7(), pos, pos + length - 1, "予備");
        pos += length;

        length = 2;
        TextFileUtil.fillField(record, sv.getDivisionDepot2(), pos, pos + length - 1, "振分デポ");
        pos += length;

    }

    private void fillItemSubno(char[] record, TblSapKanbanSv sv, int startPos) {
        String[] subnos = new String[]{
            sv.getItemSubno1(), sv.getItemSubno2(), sv.getItemSubno3(), sv.getItemSubno4(),
            sv.getItemSubno5(), sv.getItemSubno6(), sv.getItemSubno7(), sv.getItemSubno8(),
            sv.getItemSubno9(), sv.getItemSubno10(), sv.getItemSubno11(), sv.getItemSubno12(),
            sv.getItemSubno13(), sv.getItemSubno14(), sv.getItemSubno15(), sv.getItemSubno16(),
            sv.getItemSubno17(), sv.getItemSubno18(), sv.getItemSubno19(), sv.getItemSubno20()
        };
        
        for (int i = 0; i < subnos.length; i++) {
            TextFileUtil.fillField(record, subnos[i], startPos + i * 2, startPos + i * 2 + 1, 
                String.format("アイテム枝番(%d)", i + 1));
        }
    }

    private void fillFreeColumns(char[] record, TblSapKanbanSv sv, int startPos) {
        String[] columns = new String[]{
            sv.getFreeColumn1(), sv.getFreeColumn2(), sv.getFreeColumn3(), sv.getFreeColumn4(),
            "", "", "", "", "", ""  // 补充到10个
        };
        
        for (int i = 0; i < 10; i++) {
            TextFileUtil.fillField(record, columns[i], startPos + i * 4, startPos + i * 4 + 3, 
                String.format("フリーカラム(%d)", i + 1));
        }
    }

    private void fillInpackMaterial(char[] record, String code, String procClass, String number, int startPos) {
        TextFileUtil.fillField(record, code, startPos, startPos + 8, "内装資材コード");
        TextFileUtil.fillField(record, procClass, startPos + 9, startPos + 9, "調達区分");
        TextFileUtil.fillNumber(record, new BigDecimal(number != null ? number : "0"), 
            startPos + 10, startPos + 12, 0);
    }
}
