package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblSa;
import com.datalink.datamanage.domain.TblSaItem;
import com.datalink.datamanage.domain.TblSaScheduleLine;
import com.datalink.datamanage.service.ITblSaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api("SA接口")
@Validated
@RestController
@RequestMapping("/api/sa")
public class TblSaApi {
    @Resource
    private ITblSaService tblSaService;

    //    @ApiOperation("查询协议")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"saId","searchValue","createBy","updateBy","updateTime","remark","params","createTime","itemId","direction","kafkaStatus","lineId"}, value={TblSa.class, TblSaItem.class, TblSaScheduleLine.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","orderid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblOrderItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询协议", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblSa> saList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblSa searchParam = new TblSa();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            saList = tblSaService.selectTblSaFullList(searchParam);
        }
        if(saList.isEmpty()){
            Long lastId = tblSaService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+saList.get(saList.size()-1).getSaId());
            ajax.put("time", saList.get(saList.size()-1).getCreateTime());
        }

        ajax.put("items", saList);
        return ajax;
    }

    //    @ApiOperation("发送")
    @ApiImplicitParam(name = "sa", value = "协议信息", dataType = "TblSa")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送协议", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblSa tblSa) {
        tblSa.setDirection(DataConstants.DIRECTION_OUT);
        tblSa.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblSa.setCreateBy(SecurityUtils.getUsername());
        tblSaService.insertTblSa(tblSa);
        return ApiResult.success();
    }

    //    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "sa", value = "协议信息", dataType = "TblSa", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送协议", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblSa> saList) {
        for (TblSa sa : saList){
            sa.setDirection(DataConstants.DIRECTION_OUT);
            sa.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            sa.setCreateBy(SecurityUtils.getUsername());
            tblSaService.insertTblSa(sa);
        }

        return ApiResult.success();
    }
}
