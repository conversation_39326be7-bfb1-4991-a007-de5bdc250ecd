package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 协议行项目对象 tbl_sa_item
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public class TblSaItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 行项目ID */
    private Long itemId;

    /** 行号 */
    @Excel(name = "行号")
    private String itemNo;

    /** 物料编码 */
    @Excel(name = "物料编码")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    private String articleName;

    /** 物料名称英文 */
    @Excel(name = "物料名称英文")
    private String articleNameEn;

    /** 卸货点 */
    @Excel(name = "卸货点")
    private String unloadingNo;

    /** 工厂 */
    @Excel(name = "工厂")
    private String plantCode;

    /** 上次交货接收交货单号 */
    @Excel(name = "上次交货接收交货单号")
    private Long lastDeliveryReceivedOrderNo;

    /** 上次接收交货数量 */
    @Excel(name = "上次接收交货数量")
    private BigDecimal lastDeliveryReceivedQuantity;

    /** 上次交货日期 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "上次交货日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastDeliveryDate;

    /** 当前累计订货量 */
    @Excel(name = "当前累计订货量")
    private BigDecimal cumulativeQuantityOrdered;

    /** 当前累计接收量 */
    @Excel(name = "当前累计接收量")
    private BigDecimal cumulativeQuantityReceived;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 生产提前终止点 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "生产提前终止点", width = 30, dateFormat = "yyyy-MM-dd")
    private Date productionGoAhead;

    /** 物料提前终止点 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "物料提前终止点", width = 30, dateFormat = "yyyy-MM-dd")
    private Date materialGoAhead;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 上一个版本号 */
    @Excel(name = "上一个版本号")
    private String lastVersion;

    /** 上一版本发布日期 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "上一版本发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastVersionReleaseDate;

    /** 箱型 */
    @Excel(name = "箱型")
    private String workbinNo;

    /** 标准包装数量 */
    @Excel(name = "标准包装数量")
    private BigDecimal qtyPerPack;

    /** 所用车型 */
    @Excel(name = "所用车型")
    private String motorcycleType;

    /** A特性零件 */
    @Excel(name = "A特性零件")
    private String attribute;

    /** 生产消耗周期 */
    @Excel(name = "生产消耗周期")
    private String productionConsumptionCycle;

    /** 收货质保周期 */
    @Excel(name = "收货质保周期")
    private String receivingWarrantyPeriod;

    /** 预留字段1 */
    @Excel(name = "预留字段1")
    private String info1;

    /** 预留字段2 */
    @Excel(name = "预留字段2")
    private String info2;

    /** 预留字段3 */
    @Excel(name = "预留字段3")
    private String info3;

    /** 协议ID */
    @Excel(name = "协议ID")
    private Long saId;

    /** 协议计划行信息 */
    private List<TblSaScheduleLine> scheduleLines;

    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }
    public void setItemNo(String itemNo)
    {
        this.itemNo = itemNo;
    }

    public String getItemNo()
    {
        return itemNo;
    }
    public void setArticleNo(String articleNo)
    {
        this.articleNo = articleNo;
    }

    public String getArticleNo()
    {
        return articleNo;
    }
    public void setArticleName(String articleName)
    {
        this.articleName = articleName;
    }

    public String getArticleName()
    {
        return articleName;
    }
    public void setArticleNameEn(String articleNameEn)
    {
        this.articleNameEn = articleNameEn;
    }

    public String getArticleNameEn()
    {
        return articleNameEn;
    }
    public void setUnloadingNo(String unloadingNo)
    {
        this.unloadingNo = unloadingNo;
    }

    public String getUnloadingNo()
    {
        return unloadingNo;
    }
    public void setPlantCode(String plantCode)
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode()
    {
        return plantCode;
    }
    public void setLastDeliveryReceivedOrderNo(Long lastDeliveryReceivedOrderNo)
    {
        this.lastDeliveryReceivedOrderNo = lastDeliveryReceivedOrderNo;
    }

    public Long getLastDeliveryReceivedOrderNo()
    {
        return lastDeliveryReceivedOrderNo;
    }
    public void setLastDeliveryReceivedQuantity(BigDecimal lastDeliveryReceivedQuantity)
    {
        this.lastDeliveryReceivedQuantity = lastDeliveryReceivedQuantity;
    }

    public BigDecimal getLastDeliveryReceivedQuantity()
    {
        return lastDeliveryReceivedQuantity;
    }
    public void setLastDeliveryDate(Date lastDeliveryDate)
    {
        this.lastDeliveryDate = lastDeliveryDate;
    }

    public Date getLastDeliveryDate()
    {
        return lastDeliveryDate;
    }
    public void setCumulativeQuantityOrdered(BigDecimal cumulativeQuantityOrdered)
    {
        this.cumulativeQuantityOrdered = cumulativeQuantityOrdered;
    }

    public BigDecimal getCumulativeQuantityOrdered()
    {
        return cumulativeQuantityOrdered;
    }
    public void setCumulativeQuantityReceived(BigDecimal cumulativeQuantityReceived)
    {
        this.cumulativeQuantityReceived = cumulativeQuantityReceived;
    }

    public BigDecimal getCumulativeQuantityReceived()
    {
        return cumulativeQuantityReceived;
    }
    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }
    public void setProductionGoAhead(Date productionGoAhead)
    {
        this.productionGoAhead = productionGoAhead;
    }

    public Date getProductionGoAhead()
    {
        return productionGoAhead;
    }
    public void setMaterialGoAhead(Date materialGoAhead)
    {
        this.materialGoAhead = materialGoAhead;
    }

    public Date getMaterialGoAhead()
    {
        return materialGoAhead;
    }
    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    public Date getCreateDate()
    {
        return createDate;
    }
    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getVersion()
    {
        return version;
    }
    public void setLastVersion(String lastVersion)
    {
        this.lastVersion = lastVersion;
    }

    public String getLastVersion()
    {
        return lastVersion;
    }
    public void setLastVersionReleaseDate(Date lastVersionReleaseDate)
    {
        this.lastVersionReleaseDate = lastVersionReleaseDate;
    }

    public Date getLastVersionReleaseDate()
    {
        return lastVersionReleaseDate;
    }
    public void setWorkbinNo(String workbinNo)
    {
        this.workbinNo = workbinNo;
    }

    public String getWorkbinNo()
    {
        return workbinNo;
    }
    public void setQtyPerPack(BigDecimal qtyPerPack)
    {
        this.qtyPerPack = qtyPerPack;
    }

    public BigDecimal getQtyPerPack()
    {
        return qtyPerPack;
    }
    public void setMotorcycleType(String motorcycleType)
    {
        this.motorcycleType = motorcycleType;
    }

    public String getMotorcycleType()
    {
        return motorcycleType;
    }
    public void setAttribute(String attribute)
    {
        this.attribute = attribute;
    }

    public String getAttribute()
    {
        return attribute;
    }
    public void setProductionConsumptionCycle(String productionConsumptionCycle)
    {
        this.productionConsumptionCycle = productionConsumptionCycle;
    }

    public String getProductionConsumptionCycle()
    {
        return productionConsumptionCycle;
    }
    public void setReceivingWarrantyPeriod(String receivingWarrantyPeriod)
    {
        this.receivingWarrantyPeriod = receivingWarrantyPeriod;
    }

    public String getReceivingWarrantyPeriod()
    {
        return receivingWarrantyPeriod;
    }
    public void setInfo1(String info1)
    {
        this.info1 = info1;
    }

    public String getInfo1()
    {
        return info1;
    }
    public void setInfo2(String info2)
    {
        this.info2 = info2;
    }

    public String getInfo2()
    {
        return info2;
    }
    public void setInfo3(String info3)
    {
        this.info3 = info3;
    }

    public String getInfo3()
    {
        return info3;
    }
    public void setSaId(Long saId)
    {
        this.saId = saId;
    }

    public Long getSaId()
    {
        return saId;
    }

    public List<TblSaScheduleLine> getScheduleLines()
    {
        return scheduleLines;
    }

    public void setScheduleLines(List<TblSaScheduleLine> scheduleLines)
    {
        this.scheduleLines = scheduleLines;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("itemNo", getItemNo())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("articleNameEn", getArticleNameEn())
            .append("unloadingNo", getUnloadingNo())
            .append("plantCode", getPlantCode())
            .append("lastDeliveryReceivedOrderNo", getLastDeliveryReceivedOrderNo())
            .append("lastDeliveryReceivedQuantity", getLastDeliveryReceivedQuantity())
            .append("lastDeliveryDate", getLastDeliveryDate())
            .append("cumulativeQuantityOrdered", getCumulativeQuantityOrdered())
            .append("cumulativeQuantityReceived", getCumulativeQuantityReceived())
            .append("unit", getUnit())
            .append("productionGoAhead", getProductionGoAhead())
            .append("materialGoAhead", getMaterialGoAhead())
            .append("createDate", getCreateDate())
            .append("version", getVersion())
            .append("lastVersion", getLastVersion())
            .append("lastVersionReleaseDate", getLastVersionReleaseDate())
            .append("workbinNo", getWorkbinNo())
            .append("qtyPerPack", getQtyPerPack())
            .append("motorcycleType", getMotorcycleType())
            .append("attribute", getAttribute())
            .append("productionConsumptionCycle", getProductionConsumptionCycle())
            .append("receivingWarrantyPeriod", getReceivingWarrantyPeriod())
            .append("info1", getInfo1())
            .append("info2", getInfo2())
            .append("info3", getInfo3())
            .append("saId", getSaId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("tblSaScheduleLineList", getScheduleLines())
            .toString();
    }
}
