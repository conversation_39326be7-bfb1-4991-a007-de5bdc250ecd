<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('order.orderDetail.baseInfoTitle') }}
      </div>
      <el-table border :show-header="false" :data="baseTableData">
        <el-table-column
          prop="title1"
          :label="$t('order.orderDetail.title')"
          width="185"
          >
        </el-table-column>

        <el-table-column :label="$t('order.orderDetail.content')" min-width="310">
          <template slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          :label="$t('order.orderDetail.title')"
          width="185">
        </el-table-column>

        <el-table-column :label="$t('order.orderDetail.content')" min-width="310">
          <template slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <p></p>
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('order.orderDetail.itemsInfoTitle') }}
      </div>
      <el-table v-loading="loading" :data="orderItems">
        <el-table-column min-width="100" :label="$t('order.orderDetail.itemNo')" align="center" prop="itemNo" />
        <el-table-column min-width="200" :label="$t('order.orderDetail.articleNo')" align="center" prop="articleNo" />
        <el-table-column min-width="120" :label="$t('order.orderDetail.articleName')" align="center" prop="articleName" />
        <el-table-column :label="$t('order.orderDetail.quantity')" align="center" prop="quantity" />
        <el-table-column :label="$t('order.orderDetail.unit')" align="center" prop="unit" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.netPrice')" align="center" prop="netPrice" />
        <el-table-column min-width="100" :label="$t('order.orderDetail.priceUnit')" align="center" prop="priceUnit" />
        <el-table-column min-width="120" :label="$t('order.orderDetail.currencyCode')" align="center" prop="currencyCode" />
        <el-table-column min-width="120" :label="$t('order.orderDetail.deliveryDate')" align="center" prop="deliveryDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column min-width="220" :label="$t('order.orderDetail.qtyPerPack')" align="center" prop="qtyPerPack" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.deliverySplit')" align="center" prop="deliverySplit" />

        <el-table-column min-width="150" :label="$t('order.orderDetail.workbinNo')" align="center" prop="workbinNo" />
        <!-- <el-table-column min-width="150" :label="$t('order.orderDetail.workbinName')" align="center" prop="workbinName" /> -->
        <!-- <el-table-column :label="$t('order.orderDetail.state')" align="center" prop="state" /> -->
        <el-table-column min-width="180" :label="$t('order.orderDetail.purDocType')" align="center" prop="purDocType" />
        <!-- <el-table-column min-width="150" :label="$t('order.orderDetail.itemType')" align="center" prop="itemType" /> -->
        <!-- <el-table-column min-width="280" :label="$t('order.orderDetail.text')" align="center" prop="text" /> -->
        <!-- <el-table-column min-width="350" :label="$t('order.orderDetail.orderNetWorth')" align="center" prop="orderNetWorth" /> -->
        <!-- <el-table-column min-width="280" :label="$t('order.orderDetail.delIden')" align="center" prop="delIden" /> -->
        <!-- <el-table-column min-width="280" :label="$t('order.orderDetail.shortText')" align="center" prop="shortText" /> -->
        <!-- <el-table-column min-width="150" :label="$t('order.orderDetail.oldArticleNo')" align="center" prop="oldArticleNo" /> -->
        <el-table-column :label="$t('order.orderDetail.unloadingNo')" align="center" prop="unloadingNo" />
        <el-table-column min-width="200" :label="$t('order.orderDetail.unloadingName')" align="center" prop="unloadingName" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.stockLoc')" align="center" prop="stockLoc" />
        <!-- <el-table-column min-width="250" :label="$t('order.orderDetail.locDes')" align="center" prop="locDes" /> -->
        <!-- <el-table-column min-width="250" :label="$t('order.orderDetail.locAdd')" align="center" prop="locAdd" /> -->
        <el-table-column min-width="150" :label="$t('order.orderDetail.rcvName')" align="center" prop="rcvName" />

        <!-- add at 10.22 START-->
        <el-table-column min-width="150" :label="$t('order.table.customerCode')" align="center" prop="customerCode" >
          <template slot-scope="scope">
            <span>{{order.customerCode }}</span>
          </template>
          </el-table-column>

        <el-table-column min-width="150" :label="$t('order.orderDetail.customerOrderCode')" align="center" prop="customerOrderCode" />
        <!-- <el-table-column min-width="150" :label="$t('order.orderDetail.customerOrderLineCode')" align="center" prop="customerOrderLineCode" /> -->
        <el-table-column min-width="180" :label="$t('order.orderDetail.customerDeliveryDate')" align="center" prop="customerDeliveryDate" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.customerDeliveryDate }}</span>
          </template>
        </el-table-column>
        <el-table-column min-width="150" :label="$t('order.orderDetail.productType')" align="center" prop="productType" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.rcvType')" align="center" prop="rcvType" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.purchaseType')" align="center" prop="purchaseType" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.depot')" align="center" prop="depot" />

        <!-- add at 10.22 END-->
        <!-- <el-table-column min-width="150" :label="$t('order.orderDetail.rcvTel')" align="center" prop="rcvTel" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.inspeStrategy')" align="center" prop="inspeStrategy" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.zipCode')" align="center" prop="zipCode" />
        <el-table-column :label="$t('order.orderDetail.city')" align="center" prop="city" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.countryCode')" align="center" prop="countryCode" />
        <el-table-column min-width="150" :label="$t('order.orderDetail.addTimeZone')" align="center" prop="addTimeZone" />
        <el-table-column min-width="200" :label="$t('order.orderDetail.street2')" align="center" prop="street2" />
        <el-table-column min-width="200" :label="$t('order.orderDetail.street3')" align="center" prop="street3" />
        <el-table-column min-width="200" :label="$t('order.orderDetail.street4')" align="center" prop="street4" /> -->
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getItemList"
      />
    </el-card>
  </div>
</template>

<script>
import { getOrderOnly, listOrderItems} from "@/api/datamanage/order";

export default {
  name: "OrderDetail",
  data(){
    return {
      order:{},
      orderItems:[],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderId:null
      },};
  },
  created() {
    const orderId = this.$route.params.orderId;
    this.queryParams.orderId = orderId;
    this.getOrder(orderId);
    this.getItemList();
  },
  methods:{
    getOrder(orderId){
      getOrderOnly(orderId).then(res=>{
        this.order = res.data;
      });
    },
    getItemList(){
      this.loading = true;
      this.queryParams.params = {};
      listOrderItems(this.queryParams).then(res=>{
        this.orderItems = res.rows;
        console.log(this.orderItems)
        console.log(2222)
        this.total = res.total;
        this.loading = false;
      });
    }
  },
  computed: {
    baseTableData() {
      return [
          { title1: this.$t('order.orderDetail.orderCode'), content1: this.order.orderCode},
          {title1: this.$t('order.orderDetail.compCode'), content1: this.order.compCode, title2: this.$t('order.orderDetail.compName'), content2: this.order.compName },
          { title1: this.$t('order.orderDetail.suppCode'), content1: this.order.suppCode, title2: this.$t('order.orderDetail.suppName'), content2: this.order.suppName },
          { title1: this.$t('order.orderDetail.plantCode'), content1: this.order.plantCode, title2: this.$t('order.orderDetail.plantName'), content2: this.order.plantName },
        ];
    },
  }
}
</script>

<style scoped>

</style>
