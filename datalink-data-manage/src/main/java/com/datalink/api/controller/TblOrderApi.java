package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.api.common.SapApiResult;
import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.SapListRequst;
import com.datalink.api.domain.SapOrderRequest;
import com.datalink.api.domain.SapPoItem;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblOrder;
import com.datalink.datamanage.domain.TblOrderItem;
import com.datalink.datamanage.service.ITblOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api("订单接口")
@Validated
@RestController
@RequestMapping("/api/order")
public class TblOrderApi {

    @Autowired
    private ITblOrderService tblOrderService;

    private static final Logger logger = LoggerFactory.getLogger(TblOrderApi.class);

    //    @ApiOperation("查询订单")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"orderid","searchvalue","createby","updateby","updatetime","remark","params","createtime","itemid","direction","kafkastatus"}, value={TblOrder.class, TblOrderItem.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","orderid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblOrderItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询订单", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblOrder> orderList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblOrder searchParam = new TblOrder();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            orderList = tblOrderService.selectTblOrderFullList(searchParam);
        }
        if(orderList.isEmpty()){
            Long lastId = tblOrderService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+orderList.get(orderList.size()-1).getOrderId());
            ajax.put("time", orderList.get(orderList.size()-1).getCreateTime());
        }

        ajax.put("items", orderList);
        return ajax;
    }

    //    @ApiOperation("发送")
    @ApiImplicitParam(name = "order", value = "订单信息", dataType = "TblOrder")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送订单", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblOrder tblOrder) {
        tblOrder.setDirection(DataConstants.DIRECTION_OUT);
        tblOrder.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblOrder.setCreateBy(SecurityUtils.getUsername());
        tblOrderService.insertTblOrder(tblOrder);
        return ApiResult.success();
    }

    //    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "order", value = "订单信息", dataType = "TblOrder", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送订单", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblOrder> orders) {
        for (TblOrder order : orders){
            order.setDirection(DataConstants.DIRECTION_OUT);
            order.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            order.setCreateBy(SecurityUtils.getUsername());
            tblOrderService.insertTblOrder(order);
        }

        return ApiResult.success();
    }

    //    @ApiOperation("SAP发送订单")
    @PostMapping("/sapPo")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送订单", businessType = BusinessType.IMPORT)
    public SapApiResult sapGr(@Valid @RequestBody SapListRequst<SapPoItem> request) {
        try{
            List<TblOrder> orders = SapObjectConverter.convertToTblOrder(request);
            for (TblOrder order : orders) {
                // 根据orderCode查询订单
                TblOrder searchParam = new TblOrder();
                searchParam.setOrderCode(order.getOrderCode());
                List<TblOrder> existingOrders = tblOrderService.selectTblOrderList(searchParam);

                if (!existingOrders.isEmpty()) {
                    // 订单已存在,执行更新
                    TblOrder existingOrder = existingOrders.get(0);
                    order.setOrderId(existingOrder.getOrderId());
                    tblOrderService.updateTblOrder(order);
                } else {
                    // 订单不存在,执行插入
                    tblOrderService.insertTblOrder(order);
                }
            }
        } catch(Exception e) {
            logger.error("处理SAP订单数据失败：", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return SapApiResult.error(e.getMessage());
        }

        return SapApiResult.success();
    }

    @ApiOperation("批量接收订单")
    @PostMapping("/batchReceive")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量接收订单", businessType = BusinessType.IMPORT)
    public AjaxResult batchReceiveOrders(@Valid @RequestBody SapOrderRequest request) {
        try{
            List<TblOrder> orders = SapObjectConverter.convertNewFormatToTblOrder(request);
            for (TblOrder order : orders) {
                // 根据orderCode查询订单
                TblOrder searchParam = new TblOrder();
                searchParam.setOrderCode(order.getOrderCode());
                List<TblOrder> existingOrders = tblOrderService.selectTblOrderList(searchParam);

                if (!existingOrders.isEmpty()) {
                    // 订单已存在,执行更新
                    TblOrder existingOrder = existingOrders.get(0);
                    order.setOrderId(existingOrder.getOrderId());
                    tblOrderService.updateTblOrder(order);
                } else {
                    // 订单不存在,执行插入，设置状态为New
                    order.setStatus(DataConstants.ORDER_STATUS_NEW);
                    tblOrderService.insertTblOrder(order);
                }
            }
        } catch(Exception e) {
            logger.error("处理订单数据失败：", e);
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            return AjaxResult.error(e.getCause().getMessage());
            throw new RuntimeException("处理订单数据失败: " + e.getCause().getMessage());
        }

        return AjaxResult.success();
    }
}
