package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 物料信息对象 tbl_material
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TblMaterial extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 物料ID */
    private Long materialId;

    /** 物料编号 */
    @Excel(name = "物料编号")
    private String materialCode;

    /** 物料描述 */
    @Excel(name = "物料描述")
    private String materialName;

    /** 基本计量单位 */
    @Excel(name = "基本计量单位")
    private String baseUnit;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 工厂信息 */
    private List<TblMaterialPlant> plants;
} 