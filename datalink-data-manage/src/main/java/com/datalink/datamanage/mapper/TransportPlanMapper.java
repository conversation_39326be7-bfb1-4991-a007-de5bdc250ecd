package com.datalink.datamanage.mapper;

import java.util.List;
import com.datalink.datamanage.domain.TransportPlan;
import org.apache.ibatis.annotations.Mapper;

/**
 * 货量提示Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface TransportPlanMapper 
{
    /**
     * 查询货量提示
     * 
     * @param transportPlanId 货量提示ID
     * @return 货量提示
     */
    public TransportPlan selectTransportPlanById(Long transportPlanId);

    /**
     * 查询货量提示列表
     * 
     * @param transportPlan 货量提示
     * @return 货量提示集合
     */
    public List<TransportPlan> selectTransportPlanList(TransportPlan transportPlan);

    /**
     * 新增货量提示
     * 
     * @param transportPlan 货量提示
     * @return 结果
     */
    public int insertTransportPlan(TransportPlan transportPlan);

    /**
     * 修改货量提示
     * 
     * @param transportPlan 货量提示
     * @return 结果
     */
    public int updateTransportPlan(TransportPlan transportPlan);

    /**
     * 删除货量提示
     * 
     * @param transportPlanId 货量提示ID
     * @return 结果
     */
    public int deleteTransportPlanById(Long transportPlanId);

    /**
     * 批量删除货量提示
     * 
     * @param transportPlanIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTransportPlanByIds(Long[] transportPlanIds);
}
