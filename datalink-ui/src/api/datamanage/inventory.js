import request from '@/utils/request'

// 查询库存列表
export function listInventory(query) {
  return request({
    url: '/datamanage/inventory/list',
    method: 'get',
    params: query
  })
}

// 查询库存详细
export function getInventory(inventoryid) {
  return request({
    url: '/datamanage/inventory/' + inventoryid,
    method: 'get'
  })
}

// 查询库存详细(不包含行项目)
export function getInventoryOnly(inventoryid) {
  return request({
    url: '/datamanage/inventory/head/' + inventoryid,
    method: 'get'
  })
}

// 新增库存
export function addInventory(data) {
  return request({
    url: '/datamanage/inventory',
    method: 'post',
    data: data
  })
}

// 修改库存
export function updateInventory(data) {
  return request({
    url: '/datamanage/inventory',
    method: 'put',
    data: data
  })
}

// 删除库存
export function delInventory(inventoryid) {
  return request({
    url: '/datamanage/inventory/' + inventoryid,
    method: 'delete'
  })
}

// 导出库存
export function exportInventory(query) {
  return request({
    url: '/datamanage/inventory/export',
    method: 'get',
    params: query
  })
}

// 查询库存行项目列表
export function listInventoryItems(query) {
  return request({
    url: '/datamanage/inventory/listItems',
    method: 'get',
    params: query
  })
}
