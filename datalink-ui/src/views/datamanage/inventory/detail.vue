<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('inventory.detail.consignmentInfo') }}
      </div>
      <el-table
        border
        :show-header="false"
        :data="baseTableData">

        <el-table-column
          prop="title1"
          :label="$t('inventory.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('inventory.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          :label="$t('inventory.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('inventory.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <p></p>
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('inventory.detail.lineItemInfo') }}
      </div>
      <el-table v-loading="loading" :data="inventoryItemList">
        <el-table-column :label="$t('inventory.detail.articleNo')" align="center" prop="articleNo" />
        <el-table-column :label="$t('inventory.detail.articleName')" align="center" prop="articleName" />
        <el-table-column :label="$t('inventory.detail.quantity')" align="center" prop="quantity" />
        <el-table-column :label="$t('inventory.detail.unit')" align="center" prop="unit" />
        <el-table-column :label="$t('inventory.detail.days')" align="center" prop="days" />
        <el-table-column :label="$t('inventory.detail.remark')" align="center" prop="remark" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { getInventoryOnly, listInventoryItems} from "@/api/datamanage/inventory";

export default {
  name: "InventoryDetail",
  data(){
    return {inventory:{},
      inventoryItemList:[],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        inventoryId:null
      },};
  },
  created() {
    const inventoryId = this.$route.params.inventoryId;
    this.queryParams.inventoryId = inventoryId;
    this.getInventory(inventoryId)
    this.getList();
  },
  methods:{
    getInventory(inventoryId){
      getInventoryOnly(inventoryId).then(res=>{
        this.inventory = res.data;
      });
    },
    /** 查询订单行项目列表 */
    getList() {
      this.loading = true;
      listInventoryItems(this.queryParams).then(response => {
        this.inventoryItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    }
  },
  computed: {
    // 基本信息-表格数据
    baseTableData: function () {
      if (this.inventory.direction === 'O') {
        return [
          {
            title1: this.$t('inventory.detail.compCode'),
            content1: this.inventory.compCode
          },
          {
            title1: this.$t('inventory.detail.plantCode'),
            content1: this.inventory.plantCode,
            title2: this.$t('inventory.detail.plantName'),
            content2: this.inventory.plantName
          },
          {
            title1: this.$t('inventory.detail.suppCode'),
            content1: this.inventory.suppCode,
            title2: this.$t('inventory.detail.suppName'),
            content2: this.inventory.suppName
          },
          {
            title1: this.$t('inventory.detail.updateDate'),
            content1: this.inventory && this.inventory.updateDate && this.inventory.updateDate.length >= 8 ? this.inventory.updateDate.slice(0, 4) + '-' + this.inventory.updateDate.slice(4, 6) + '-' + this.inventory.updateDate.slice(6, 8) : '',
            title2: this.$t('inventory.detail.updateTime'),
            content2: this.inventory && this.inventory.updateTime && this.inventory.updateTime.length >= 6 ? this.inventory.updateTime.slice(0, 2) + ':' + this.inventory.updateTime.slice(2, 4) + ':' + this.inventory.updateTime.slice(4, 6) : ''
          }
        ];
      } else {
        return [
          {
            title1: this.$t('inventory.detail.suppCode'),
            content1: this.inventory.suppCode,
            title2: this.$t('inventory.detail.suppName'),
            content2: this.inventory.suppName
          },
          {
            title1: this.$t('inventory.detail.plantCode'),
            content1: this.inventory.plantCode,
            title2: this.$t('inventory.detail.plantName'),
            content2: this.inventory.plantName
          },
          {
            title1: this.$t('inventory.detail.suppCode'),
            content1: this.inventory.suppCode,
            title2: this.$t('inventory.detail.suppName'),
            content2: this.inventory.suppName
          },
          {
            title1: this.$t('inventory.detail.updateDate'),
            content1: this.inventory && this.inventory.updateDate && this.inventory.updateDate.length >= 8 ? this.inventory.updateDate.slice(0, 4) + '-' + this.inventory.updateDate.slice(4, 6) + '-' + this.inventory.updateDate.slice(6, 8) : '',
            title2: this.$t('inventory.detail.updateTime'),
            content2: this.inventory && this.inventory.updateTime && this.inventory.updateTime.length >= 6 ? this.inventory.updateTime.slice(0, 2) + ':' + this.inventory.updateTime.slice(2, 4) + ':' + this.inventory.updateTime.slice(4, 6) : ''
          }
        ];
      }
    }
  }
}
</script>

<style scoped>

</style>
