package com.datalink.api.common;

import java.util.HashMap;

public class ApiResult  extends HashMap<String, Object> {
    private static final long serialVersionUID = 11L;

    /** 状态码 */
    public static final String CODE_TAG = "code";

    /** 返回内容 */
    public static final String MSG_TAG = "msg";

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    public ApiResult()
    {
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg 返回内容
     */
    public ApiResult(int code, String msg)
    {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static ApiResult success()
    {
        return new ApiResult(ApiConstants.SUCCESS_CODE, "Success");
    }

    public static ApiResult formatError(String msg)
    {
        return new ApiResult(ApiConstants.FORMAT_ERR_CODE, msg);
    }

    public static ApiResult emptyError(String msg)
    {
        return new ApiResult(ApiConstants.EMPTY_ERR_CODE, msg);
    }

    public boolean isSuccess(){
        if (containsKey(CODE_TAG)){
            return ApiConstants.SUCCESS_CODE == (int)get(CODE_TAG);
        }
        return false;
    }
}
