package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 收货反馈行项目对象 tbl_feedback_item
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
public class TblFeedbackItem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 收货反馈行项目ID */
    private Long itemId;

    /** 序号 */
    @Excel(name = "序号")
    private String seqNo;

    /** 工厂代码 */
    @Excel(name = "工厂代码")
    private String plantCode;

    /** 采购订单号 */
    @Excel(name = "采购订单号")
    @JsonAlias("ordercode")
    private String orderCode;

    /** 采购订单行号 */
    @Excel(name = "采购订单行号")
    @JsonAlias("orderlineno")
    private String orderLineNo;

    /** 物料编码 */
    @Excel(name = "物料编码")
    @JsonAlias("articleno")
    private String articleNo;

    /** 物料名称 */
    @Excel(name = "物料名称")
    @JsonAlias("articlename")
    private String articleName;

    /** 收货日期 */
    @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    @Excel(name = "收货日期", width = 30, dateFormat = "yyyyMMdd")
    @JsonAlias("rcvdate")
    private Date rcvDate;

    /** 时间 */
    @JsonFormat(pattern = "HHmmss", timezone = "GMT+8")
    @Excel(name = "时间", width = 30, dateFormat = "HHmmss")
    @JsonAlias("rcvtime")
    private Date rcvTime;

    /** 交货数量 */
    @Excel(name = "交货数量")
    private BigDecimal quantity;

    /** 单位 */
    @Excel(name = "单位")
    private String unit;

    /** 行项目金额 */
    @Excel(name = "行项目金额")
    private String itemAmount;

    /** 税码 */
    @Excel(name = "税码")
    private String taxCode;

    /** 收货凭证号码 */
    @Excel(name = "收货凭证号码")
    @JsonAlias("rcvdocno")
    private String rcvDocNo;

    /** 物料凭证年度 */
    @Excel(name = "物料凭证年度")
    @JsonAlias("articledocannual")
    private Integer articleDocAnnual;

    /** 收货凭证项目号 */
    @Excel(name = "收货凭证项目号")
    @JsonAlias("rcvdocitemno")
    private Integer rcvDocItemNo;

    /** 收货反馈ID */
    @Excel(name = "收货反馈ID")
    private Long feedId;

    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }

    public void setSeqNo(String seqNo)
    {
        this.seqNo = seqNo;
    }

    public String getSeqNo()
    {
        return seqNo;
    }

    public void setPlantCode(String plantCode)
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode()
    {
        return plantCode;
    }

    public void setOrderCode(String orderCode)
    {
        this.orderCode = orderCode;
    }

    public String getOrderCode()
    {
        return orderCode;
    }
    public void setOrderLineNo(String orderLineNo)
    {
        this.orderLineNo = orderLineNo;
    }

    public String getOrderLineNo()
    {
        return orderLineNo;
    }
    public void setArticleNo(String articleNo)
    {
        this.articleNo = articleNo;
    }

    public String getArticleNo()
    {
        return articleNo;
    }
    public void setArticleName(String articleName)
    {
        this.articleName = articleName;
    }

    public String getArticleName()
    {
        return articleName;
    }
    public void setRcvDate(Date rcvDate)
    {
        this.rcvDate = rcvDate;
    }

    public Date getRcvDate()
    {
        return rcvDate;
    }
    public void setRcvTime(Date rcvTime)
    {
        this.rcvTime = rcvTime;
    }

    public Date getRcvTime()
    {
        return rcvTime;
    }
    public void setQuantity(BigDecimal quantity)
    {
        this.quantity = quantity;
    }

    public BigDecimal getQuantity()
    {
        return quantity;
    }
    public void setUnit(String unit)
    {
        this.unit = unit;
    }

    public String getUnit()
    {
        return unit;
    }

    public void setItemAmount(String itemAmount)
    {
        this.itemAmount = itemAmount;
    }

    public String getItemAmount()
    {
        return itemAmount;
    }

    public void setTaxCode(String taxCode)
    {
        this.taxCode = taxCode;
    }

    public String getTaxCode()
    {
        return taxCode;
    }

    public void setRcvDocNo(String rcvDocNo)
    {
        this.rcvDocNo = rcvDocNo;
    }

    public String getRcvDocNo()
    {
        return rcvDocNo;
    }
    public void setArticleDocAnnual(Integer articleDocAnnual)
    {
        this.articleDocAnnual = articleDocAnnual;
    }

    public Integer getArticleDocAnnual()
    {
        return articleDocAnnual;
    }
    public void setRcvDocItemNo(Integer rcvDocItemNo)
    {
        this.rcvDocItemNo = rcvDocItemNo;
    }

    public Integer getRcvDocItemNo()
    {
        return rcvDocItemNo;
    }
    public void setFeedId(Long feedId)
    {
        this.feedId = feedId;
    }

    public Long getFeedId()
    {
        return feedId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("itemId", getItemId())
            .append("seqNo", getSeqNo())
            .append("plantCode", getPlantCode())
            .append("orderCode", getOrderCode())
            .append("itemNo", getOrderLineNo())
            .append("articleNo", getArticleNo())
            .append("articleName", getArticleName())
            .append("rcvDate", getRcvDate())
            .append("rcvTime", getRcvTime())
            .append("quantity", getQuantity())
            .append("unit", getUnit())
            .append("itemAmount", getItemAmount())
            .append("taxCode", getTaxCode())
            .append("rcvDocNo", getRcvDocNo())
            .append("articleDocAnnual", getArticleDocAnnual())
            .append("rcvDocItemNo", getRcvDocItemNo())
            .append("feedId", getFeedId())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
