package com.datalink.datamanage.controller;

import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblSa;
import com.datalink.datamanage.service.ITblSaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 协议Controller
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
@RestController
@RequestMapping("/datamanage/sa")
public class TblSaController extends BaseController
{
    @Autowired
    private ITblSaService tblSaService;

    /**
     * 查询协议列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:sa:list')")
    @GetMapping("/list")
    public TableDataInfo list(TblSa tblSa)
    {
        startPage();
        List<TblSa> list = tblSaService.selectTblSaList(tblSa);
        return getDataTable(list);
    }

    /**
     * 导出协议列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:sa:export')")
    @Log(title = "协议", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblSa tblSa)
    {
        List<TblSa> list = tblSaService.selectTblSaList(tblSa);
        ExcelUtil<TblSa> util = new ExcelUtil<TblSa>(TblSa.class);
        return util.exportExcel(list, "协议数据");
    }

    /**
     * 获取协议详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:sa:query')")
    @GetMapping(value = "/{saId}")
    public AjaxResult getInfo(@PathVariable("saId") Long saId)
    {
        return AjaxResult.success(tblSaService.selectTblSaById(saId));
    }

    /**
     * 新增协议
     */
    @PreAuthorize("@ss.hasPermi('datamanage:sa:add')")
    @Log(title = "协议", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblSa tblSa)
    {
        return toAjax(tblSaService.insertTblSa(tblSa));
    }

    /**
     * 修改协议
     */
    @PreAuthorize("@ss.hasPermi('datamanage:sa:edit')")
    @Log(title = "协议", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblSa tblSa)
    {
        return toAjax(tblSaService.updateTblSa(tblSa));
    }

    /**
     * 删除协议
     */
    @PreAuthorize("@ss.hasPermi('datamanage:sa:remove')")
    @Log(title = "协议", businessType = BusinessType.DELETE)
	@DeleteMapping("/{saIds}")
    public AjaxResult remove(@PathVariable Long[] saIds)
    {
        return toAjax(tblSaService.deleteTblSaByIds(saIds));
    }
}
