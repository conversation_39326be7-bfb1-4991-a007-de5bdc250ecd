package com.datalink.datamanage.service;

import java.util.List;
import java.util.Map;

import com.datalink.api.domain.SapDeliveryPlan;
import com.datalink.api.domain.SapDeliveryPlanDetail;
import com.datalink.api.domain.dto.SapDeliveryPlanDTO;
import com.datalink.api.domain.dto.SapRequestDTO;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;

/**
 * 支给计划Service接口
 * 
 * <AUTHOR>
 */
public interface ISapDeliveryPlanService
{
    /**
     * 查询支给计划明细
     * 
     * @param sapDeliveryPlan 支给计划
     * @return 支给计划
     */
    List<SapDeliveryPlanDetail> selectTblSapDeliveryPlanDetail(SapDeliveryPlan sapDeliveryPlan);

    /**
     * 查询支给计划列表
     * 
     * @param tblSapDeliveryPlan 支给计划
     * @return 支给计划集合
     */
    public List<SapDeliveryPlan> selectTblSapDeliveryPlanList(SapDeliveryPlan tblSapDeliveryPlan);

    /**
     * 新增支给计划
     *
     * @param tblSapDeliveryPlan 支给计划
     */
    public void insertTblSapDeliveryPlan(SapRequestDTO<SapDeliveryPlanDTO> tblSapDeliveryPlan);

    /**
     * 修改支给计划
     * 
     * @param tblSapDeliveryPlan 支给计划
     * @return 结果
     */
    public int updateTblSapDeliveryPlan(SapDeliveryPlan tblSapDeliveryPlan);

    Map<String, Object> convertToWeekData(SapDeliveryPlan plan, String selectedYearMonth);

    AjaxResult downloadDeliveryPlanTxt(TableDataInfo result);
}
