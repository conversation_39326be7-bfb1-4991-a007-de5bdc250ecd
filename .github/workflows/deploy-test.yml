name: Deploy Java Project

on:
  workflow_dispatch:
    inputs:
      build:
        description: '选择构建的部分'
        required: true
        type: choice
        options:
          - frontend
          - backend
          - templates
          - all
        default: 'backend'

jobs:
  frontend:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'frontend' || github.event.inputs.build == 'all' }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        paths:
          - datalink-ui/

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'

    - name: Install dependencies
      run: npm install

    - name: Build
      run: npm run build:prod

    - name: Empty Dir
      run: ssh ${{secrets.SPS_TEST_SERVER_USERNAME}}@${{ secrets.SPS_TEST_SERVER }} 'rm -rf /home/<USER>/sps-app/ui/*'

    - name: Deploy to Server via SFTP
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SPS_TEST_SERVER }}
        username: ${{ secrets.SPS_TEST_SERVER_USERNAME }}
        password: ${{ secrets.SPS_TEST_SERVER_PASS }}
        source: datalink-ui/dist/
        target: /home/<USER>/sps-app/ui/

    - name: Reload Nginx
      run: ssh ${{secrets.SPS_TEST_SERVER_USERNAME}}@${{ secrets.SPS_TEST_SERVER }} 'sudo nginx -s reload'

  backend:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'backend' || github.event.inputs.build == 'all' }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK 8
      uses: actions/setup-java@v2
      with:
        java-version: '8'
        distribution: 'adopt'

    - name: Build Backend
      run: ./mvnw clean package -DskipTests

    - name: Deploy Backend to Server via SSH
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SPS_TEST_SERVER }}
        username: ${{ secrets.SPS_TEST_SERVER_USERNAME }}
        password: ${{ secrets.SPS_TEST_SERVER_PASS }}
        source: "target/*.jar"
        target: "/home/<USER>/sps-app/app/"

    - name: Restart Backend Service
      run: ssh ${{secrets.SPS_TEST_SERVER_USERNAME}}@${{ secrets.SPS_TEST_SERVER }} 'sudo systemctl restart sps-app.service'

  templates:
    runs-on: [self-hosted, linux, x64]
    if: ${{ github.event.inputs.build == 'templates' || github.event.inputs.build == 'all' }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        paths:
          - datalink-data-manage/src/main/resources/template/

    - name: Deploy Templates to Server via SSH
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SPS_TEST_SERVER }}
        username: ${{ secrets.SPS_TEST_SERVER_USERNAME }}
        password: ${{ secrets.SPS_TEST_SERVER_PASS }}
        source: "datalink-data-manage/src/main/resources/template/*"
        target: "/home/<USER>/sps-app/template/"
