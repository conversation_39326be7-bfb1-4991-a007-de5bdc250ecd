package com.datalink.datamanage.mapper;

import com.datalink.api.domain.SapDeliveryPlan;
import com.datalink.api.domain.SapDeliveryPlanDetail;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 支给计划Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface SapDeliveryPlanMapper
{
    /**
     * 查询支给计划
     *
     * @param sapDeliveryPlan 支给计划
     * @return 支给计划
     */
    public List<SapDeliveryPlanDetail> selectTblSapDeliveryPlanDetail(SapDeliveryPlan sapDeliveryPlan);

    /**
     * 查询支给计划列表
     * 
     * @param sapDeliveryPlan 支给计划
     * @return 支给计划集合
     */
    public List<SapDeliveryPlan> selectTblSapDeliveryPlanList(SapDeliveryPlan sapDeliveryPlan);

    /**
     * 新增支给计划
     * 
     * @param sapDeliveryPlan 支给计划
     * @return 结果
     */
    public int insertTblSapDeliveryPlan(SapDeliveryPlan sapDeliveryPlan);

    /**
     * 修改支给计划
     * 
     * @param sapDeliveryPlan 支给计划
     * @return 结果
     */
    public int updateTblSapDeliveryPlan(SapDeliveryPlan sapDeliveryPlan);


    /**
     * 批量删除供应计划明细
     * 
     * @param deliveryPlanIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblSapDeliveryPlanDetailByDeliveryPlanIds(Long[] deliveryPlanIds);
    
    /**
     * 批量新增供应计划明细
     * 
     * @param tblSapDeliveryPlanDetailList 供应计划明细列表
     * @return 结果
     */
    public int batchTblSapDeliveryPlanDetail(List<SapDeliveryPlanDetail> tblSapDeliveryPlanDetailList);
    

    /**
     * 通过支给计划ID删除供应计划明细信息
     * 
     * @param deliveryPlanId 支给计划ID
     * @return 结果
     */
    public int deleteTblSapDeliveryPlanDetailByDeliveryPlanId(Long deliveryPlanId);

    /**
     * 新增或更新支给计划
     */
    int insertOrUpdateDeliveryPlan(SapDeliveryPlan sapDeliveryPlan);

    /**
     * 新增或更新（删除）支给计划明细
     */
    int insertOrUpdateDeliveryPlanDetail(SapDeliveryPlanDetail sapDeliveryPlanDetail);

    /**
     * 新增或更新支给实际明细
     */
    void insertOrUpdateDeliveryActualDetail(SapDeliveryPlanDetail detail);
}
