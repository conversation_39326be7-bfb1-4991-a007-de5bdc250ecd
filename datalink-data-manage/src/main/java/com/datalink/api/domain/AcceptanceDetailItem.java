package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 验收明细项目对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class AcceptanceDetailItem {

    /** 序号 */
    @JsonProperty("ZSENO")
    private String zseno;

    /** 物料凭证号 */
    @JsonProperty("MBLNR")
    @NotEmpty(message = "物料凭证号不能为空")
    private String mblnr;

    /** 物料凭证行项目 */
    @JsonProperty("MBLPO")
    private String mblpo;

    /** 物料凭证年度 */
    @JsonProperty("MJAHR")
    private String mjahr;

    /** 过账日期 */
    @JsonProperty("BUDAT")
    private String budat;

    /** 采购订单号 */
    @JsonProperty("EBELN")
    @NotEmpty(message = "采购订单号不能为空")
    private String ebeln;

    /** 采购订单行项目 */
    @JsonProperty("EBELP")
    @NotEmpty(message = "采购订单行项目不能为空")
    private String ebelp;

    /** 工厂 */
    @JsonProperty("WERKS")
    @NotEmpty(message = "工厂不能为空")
    private String werks;

    /** 物料编号 */
    @JsonProperty("MATNR")
    @NotEmpty(message = "物料编号不能为空")
    private String matnr;

    /** 物料描述 */
    @JsonProperty("TXZ01")
    private String txz01;

    /** 数量 */
    @JsonProperty("MENGE")
    @NotNull(message = "数量不能为空")
    private String menge;

    /** 单位 */
    @JsonProperty("MEINS")
    @NotEmpty(message = "单位不能为空")
    private String meins;

    /** 金额 */
    @JsonProperty("DMBTR")
    private String dmbtr;

    /** 税码 */
    @JsonProperty("MWSKZ")
    private String mwskz;

    // Getters and Setters
    public String getZseno() {
        return zseno;
    }

    public void setZseno(String zseno) {
        this.zseno = zseno;
    }

    public String getMblnr() {
        return mblnr;
    }

    public void setMblnr(String mblnr) {
        this.mblnr = mblnr;
    }

    public String getMblpo() {
        return mblpo;
    }

    public void setMblpo(String mblpo) {
        this.mblpo = mblpo;
    }

    public String getMjahr() {
        return mjahr;
    }

    public void setMjahr(String mjahr) {
        this.mjahr = mjahr;
    }

    public String getBudat() {
        return budat;
    }

    public void setBudat(String budat) {
        this.budat = budat;
    }

    public String getEbeln() {
        return ebeln;
    }

    public void setEbeln(String ebeln) {
        this.ebeln = ebeln;
    }

    public String getEbelp() {
        return ebelp;
    }

    public void setEbelp(String ebelp) {
        this.ebelp = ebelp;
    }

    public String getWerks() {
        return werks;
    }

    public void setWerks(String werks) {
        this.werks = werks;
    }

    public String getMatnr() {
        return matnr;
    }

    public void setMatnr(String matnr) {
        this.matnr = matnr;
    }

    public String getTxz01() {
        return txz01;
    }

    public void setTxz01(String txz01) {
        this.txz01 = txz01;
    }

    public String getMenge() {
        return menge;
    }

    public void setMenge(String menge) {
        this.menge = menge;
    }

    public String getMeins() {
        return meins;
    }

    public void setMeins(String meins) {
        this.meins = meins;
    }

    public String getDmbtr() {
        return dmbtr;
    }

    public void setDmbtr(String dmbtr) {
        this.dmbtr = dmbtr;
    }

    public String getMwskz() {
        return mwskz;
    }

    public void setMwskz(String mwskz) {
        this.mwskz = mwskz;
    }
}
