<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('forecast.detail.forecastInfo') }}
      </div>
      <el-table
        border
        :show-header="false"
        :data="baseTableData">

        <el-table-column
          prop="title1"
          :label="$t('forecast.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('forecast.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          :label="$t('forecast.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('forecast.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <p></p>

    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('forecast.detail.lineItemInfo') }}
      </div>
      <el-table v-loading="loading" :data="forecastitemList">
        <el-table-column :label="$t('forecast.detail.articleNo')" align="center" prop="articleNo" />
        <el-table-column :label="$t('forecast.detail.articleName')" align="center" prop="articleName" />
        <el-table-column :label="$t('forecast.detail.deliveryDate')" align="center" prop="deliveryDate" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('forecast.detail.quantity')" align="center" prop="quantity" />
        <el-table-column :label="$t('forecast.detail.unit')" align="center" prop="unit" />
        <el-table-column :label="$t('forecast.detail.durType')" align="center" prop="durType" />
        <el-table-column :label="$t('forecast.detail.proType')" align="center" prop="proType" />
        <el-table-column :label="$t('forecast.detail.poddet')" align="center" prop="poddet" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { getForecastOnly, listForecastItems} from "@/api/datamanage/forecast";

export default {
  name: "ForecastDetail",
  data(){
    return {
      forecast: {},
      forecastitemList: [],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        forecastId: null
      }
    };
  },
  created() {
    const forecastId = this.$route.params.forecastId;
    this.queryParams.forecastId = forecastId;
    this.getForecast(forecastId);
    this.getList();
  },
  methods:{
    getForecast(forecastId){
      getForecastOnly(forecastId).then(res => {
        this.forecast = res.data;
      });
    },
    /** 查询订单行项目列表 */
    getList() {
      this.loading = true;
      listForecastItems(this.queryParams).then(response => {
        this.forecastitemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    }
  },
  computed: {
    // 基本信息-表格数据
    baseTableData: function () {
      if (this.forecast.direction === 'I') {
        return [
          {
            title1: this.$t('forecast.detail.forecastCode'),
            content1: this.forecast.forecastCode,
            title2: this.$t('forecast.detail.compCode'),
            content2: this.forecast.compCode
          },
          {
            title1: this.$t('forecast.detail.plantCode'),
            content1: this.forecast.plantCode,
            title2: this.$t('forecast.detail.plantName'),
            content2: this.forecast.plantName
          },
          {
            title1: this.$t('forecast.detail.version'),
            content1: this.forecast.version,
            title2: '',
            content2: ''
          }
        ];
      } else {
        return [
          {
            title1: this.$t('forecast.detail.forecastCode'),
            content1: this.forecast.forecastCode,
            title2: this.$t('forecast.detail.version'),
            content2: this.forecast.version
          },
          {
            title1: this.$t('forecast.detail.plantCode'),
            content1: this.forecast.plantCode,
            title2: this.$t('forecast.detail.plantName'),
            content2: this.forecast.plantName
          },
          {
            title1: this.$t('forecast.detail.suppCode'),
            content1: this.forecast.suppCode,
            title2: this.$t('forecast.detail.suppName'),
            content2: this.forecast.suppName
          }
        ];
      }
    }
  }
}
</script>

<style scoped>

</style>
