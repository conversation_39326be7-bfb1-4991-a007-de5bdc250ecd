<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblNoticeMapper">

    <resultMap type="TblNotice" id="TblNoticeResult">
        <result property="noticeId"    column="notice_id"    />
        <result property="noticeCode"    column="notice_code"    />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="status"    column="status"    />
        <result property="needReply"    column="need_reply"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="createBy"    column="create_by"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="direction"    column="Direction"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap id="TblNoticeTblNoticeReplyResult" type="TblNotice" extends="TblNoticeResult">
        <collection property="tblNoticeReplyList" notNullColumn="sub_reply_id" javaType="java.util.List" resultMap="TblNoticeReplyResult" />
    </resultMap>

    <resultMap id="NoticeReplyResult" type="NoticeReply">
        <result property="noticeCode"    column="notice_code"    />
        <result property="noticeTitle"    column="notice_title"    />
        <result property="noticeType"    column="notice_type"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="status"    column="status"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="createBy"    column="create_by"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="replyId"    column="sub_reply_id"    />
        <result property="content"    column="sub_content"    />
        <result property="suppCode"    column="sub_Supp_Code"    />
        <result property="isRead"    column="sub_is_read"    />
        <result property="suppName"    column="sub_Supp_Name"    />
        <result property="noticeId"    column="sub_notice_id"    />
        <result property="replyTime"    column="sub_reply_time"    />
        <result property="needReply"    column="need_reply"    />
        <result property="kafkaStatus"    column="sub_Kafka_Status"    />
        <result property="direction"    column="Direction"    />
    </resultMap>

    <resultMap type="TblNoticeReply" id="TblNoticeReplyResult">
        <result property="replyId"    column="sub_reply_id"    />
        <result property="content"    column="sub_content"    />
        <result property="suppCode"    column="sub_Supp_Code"    />
        <result property="suppName"    column="sub_Supp_Name"    />
        <result property="noticeId"    column="sub_notice_id"    />
        <result property="isRead"    column="sub_is_read"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="replyTime"    column="sub_reply_time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTime"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
        <result property="kafkaStatus"    column="sub_Kafka_Status"    />
        <collection property="replyAttachmentList" ofType="TblAttachment" javaType="List" column="sub_reply_id" select="com.datalink.datamanage.mapper.TblAttachmentMapper.selectNoticeReplyTblAttachmentListByReplyId"/>
    </resultMap>

    <sql id="selectTblNoticeVo">
        select notice_id, notice_code, notice_title, notice_type, notice_content, status, need_reply, Comp_Code, Plant_Code, Plant_Name, Direction, create_by, publish_time, create_time, update_by, update_time, remark from tbl_notice
    </sql>

    <sql id="selectTblNoticeReplyVo">
        select reply_id as sub_reply_id, content as sub_content, is_read as sub_is_read, Supp_Code as sub_Supp_Code, Supp_Name as sub_Supp_Name, notice_id as sub_notice_id, Create_Time as sub_Create_Time, reply_time as sub_reply_time, Create_By as sub_Create_By, Update_Time as sub_Update_Time, Update_By as sub_Update_By, Kafka_Status as sub_Kafka_Status
        from tbl_notice_reply
    </sql>

    <select id="selectTblNoticeList" parameterType="TblNotice" resultMap="TblNoticeResult">
        <include refid="selectTblNoticeVo"/>
        <where>
            <if test="noticeCode != null  and noticeCode != ''"> and notice_code = #{noticeCode}</if>
            <if test="noticeTitle != null  and noticeTitle != ''"> and notice_title like concat('%', #{noticeTitle}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginPublishTime != null and params.beginPublishTime != '' and params.endPublishTime != null and params.endPublishTime != ''"> and publish_time between #{params.beginPublishTime} and #{params.endPublishTime}</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
    </select>

    <select id="selectTblNoticeById" parameterType="Long" resultMap="TblNoticeTblNoticeReplyResult">
        select a.notice_id, a.notice_code, a.notice_title, a.notice_type, a.notice_content, a.status, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.create_by, a.publish_time, a.create_time, a.update_by, a.update_time, a.remark,a.Direction,a.need_reply,
               b.reply_id as sub_reply_id, b.content as sub_content, b.Supp_Code as sub_Supp_Code, b.Supp_Name as sub_Supp_Name, b.notice_id as sub_notice_id, b.is_read as sub_is_read, b.Create_Time as sub_Create_Time, b.reply_time as sub_reply_time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Kafka_Status as sub_Kafka_Status
        from tbl_notice a
        left join tbl_notice_reply b on b.notice_id = a.notice_id
        where a.notice_id = #{noticeId}
    </select>

    <select id="selectToSendNoticeReplyList" resultMap="NoticeReplyResult">
        select a.notice_id, a.notice_code, a.notice_title, a.notice_type, a.notice_content, a.status, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.create_by, a.publish_time, a.create_time, a.update_by, a.update_time, a.remark,a.Direction,a.need_reply,
               b.reply_id as sub_reply_id, b.content as sub_content, b.Supp_Code as sub_Supp_Code, b.Supp_Name as sub_Supp_Name, b.notice_id as sub_notice_id, b.Create_Time as sub_Create_Time, b.reply_time as sub_reply_time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Kafka_Status as sub_Kafka_Status, b.is_read as sub_is_read
        from tbl_notice_reply b left join tbl_notice a on b.notice_id = a.notice_id
        where a.Direction = 'O' and b.Kafka_Status='1';
    </select>
    <select id="selectTblNoticeReplyList" parameterType="TblNoticeReply" resultMap="TblNoticeReplyResult">
        <include refid="selectTblNoticeReplyVo"/>
        <where>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code = #{suppCode}</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="noticeId != null "> and notice_id = #{noticeId}</if>
            <if test="isRead != null "> and is_read = #{isRead}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and Create_Time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="params.beginReplyTime != null and params.beginReplyTime != '' and params.endReplyTime != null and params.endReplyTime != ''"> and reply_time between #{params.beginReplyTime} and #{params.endReplyTime}</if>
            <if test="createBy != null  and createBy != ''"> and Create_By = #{createBy}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and Update_Time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and Update_By = #{updateBy}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <if test="params.isReply != null  and params.isReply == 'Y'.toString()"> and reply_time is not null</if>
            <if test="params.isReply != null  and params.isReply == 'N'.toString()"> and reply_time is null</if>
        </where>
    </select>
    <select id="selectNoticeReplyList" resultMap="NoticeReplyResult" parameterType="NoticeReply">
        select a.notice_id, a.notice_code, a.notice_title, a.notice_type, a.notice_content, a.status, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.create_by, a.publish_time, a.create_time, a.update_by, a.update_time, a.remark,a.Direction,a.need_reply,
               b.reply_id as sub_reply_id, b.content as sub_content, b.Supp_Code as sub_Supp_Code, b.Supp_Name as sub_Supp_Name, b.notice_id as sub_notice_id, b.Create_Time as sub_Create_Time, b.reply_time as sub_reply_time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Kafka_Status as sub_Kafka_Status, b.is_read as sub_is_read
        from tbl_notice_reply b left join tbl_notice a on b.notice_id = a.notice_id
        <where>
            <if test="suppCode != null  and suppCode != ''"> and b.Supp_Code = #{suppCode}</if>
            <if test="suppName != null  and suppName != ''"> and b.Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="noticeId != null "> and a.notice_id = #{noticeId}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and b.Create_Time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
            <if test="params.beginReplyTime != null and params.beginReplyTime != '' and params.endReplyTime != null and params.endReplyTime != ''"> and b.reply_time between #{params.beginReplyTime} and #{params.endReplyTime}</if>
            <if test="createBy != null  and createBy != ''"> and b.Create_By = #{createBy}</if>
            <if test="params.beginUpdateTime != null and params.beginUpdateTime != '' and params.endUpdateTime != null and params.endUpdateTime != ''"> and b.Update_Time between #{params.beginUpdateTime} and #{params.endUpdateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and b.Update_By = #{updateBy}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and b.Kafka_Status = #{kafkaStatus}</if>
            <if test="params.isReply != null  and params.isReply == 'Y'.toString()"> and b.reply_time is not null</if>
            <if test="params.isReply != null  and params.isReply == 'N'.toString()"> and b.reply_time is null</if>
            <if test="noticeCode != null  and noticeCode != ''"> and a.notice_code = #{noticeCode}</if>
            <if test="noticeTitle != null  and noticeTitle != ''"> and a.notice_title like concat('%', #{noticeTitle}, '%')</if>
            <if test="status != null  and status != ''"> and a.status = #{status}</if>
            <if test="params.beginPublishTime != null and params.beginPublishTime != '' and params.endPublishTime != null and params.endPublishTime != ''"> and a.publish_time between #{params.beginPublishTime} and #{params.endPublishTime}</if>
            <if test="direction != null  and direction != ''"> and a.Direction = #{direction}</if>
            <if test="compCode != null  and compCode != ''"> and a.Comp_Code = #{compCode}</if>
        </where>
    </select>
    <select id="selectTblNoticeReplyByNoticeCodeAndSuppCode" resultMap="TblNoticeReplyResult">
        select * from tbl_notice_reply
        where notice_id in (select notice_id from tbl_notice where notice_code = #{noticeCode})  and Supp_Code = #{suppCode}
    </select>

    <insert id="insertTblNotice" parameterType="TblNotice" useGeneratedKeys="true" keyProperty="noticeId">
        insert into tbl_notice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeCode != null">notice_code,</if>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title,</if>
            <if test="noticeType != null and noticeType != ''">notice_type,</if>
            <if test="noticeContent != null">notice_content,</if>
            <if test="status != null">status,</if>
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code,</if>
            <if test="plantName != null and plantName != ''">Plant_Name,</if>
            <if test="needReply != null and needReply != ''">need_reply,</if>
            <if test="createBy != null">create_by,</if>
            <if test="direction != null and direction != ''">Direction,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeCode != null">#{noticeCode},</if>
            <if test="noticeTitle != null and noticeTitle != ''">#{noticeTitle},</if>
            <if test="noticeType != null and noticeType != ''">#{noticeType},</if>
            <if test="noticeContent != null">#{noticeContent},</if>
            <if test="status != null">#{status},</if>
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="plantCode != null and plantCode != ''">#{plantCode},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="needReply != null and needReply != ''">#{needReply},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblNotice" parameterType="TblNotice">
        update tbl_notice
        <trim prefix="SET" suffixOverrides=",">
            <if test="noticeCode != null">notice_code = #{noticeCode},</if>
            <if test="noticeTitle != null and noticeTitle != ''">notice_title = #{noticeTitle},</if>
            <if test="noticeType != null and noticeType != ''">notice_type = #{noticeType},</if>
            <if test="noticeContent != null">notice_content = #{noticeContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">Plant_Name = #{plantName},</if>
            <if test="needReply != null and needReply != ''">need_reply = #{needReply},</if>
            <if test="direction != null and direction != ''">Direction = #{direction},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="direction != null">Direction = #{direction},</if>
        </trim>
        where notice_id = #{noticeId}
    </update>

    <update id="updateTblNoticeReply" parameterType="TblNoticeReply">
        update tbl_notice_reply
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="kafkaStatus != null">Kafka_Status = #{kafkaStatus},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="replyTime != null">reply_time = #{replyTime}</if>
            <if test="isRead != null and isRead != ''">is_read = #{isRead},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where reply_id = #{replyId}
    </update>
    <update id="updateTblNoticeReplyByNoticeCodeMap" parameterType="java.util.Map">
        update tbl_notice_reply
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="isRead != null and isRead != ''">is_read = #{isRead},</if>
<!--            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>-->
<!--            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>-->
<!--            <if test="kafkaStatus != null">Kafka_Status = #{kafkaStatus},</if>-->
<!--            <if test="createTime != null">Create_Time = #{createTime},</if>-->
<!--            <if test="createBy != null">Create_By = #{createBy},</if>-->
<!--            <if test="updateTime != null">Update_Time = #{updateTime},</if>-->
<!--            <if test="updateBy != null">Update_By = #{updateBy},</if>-->
            <if test="replyTime != null">reply_time = #{replyTime}</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where notice_id in (select notice_id from tbl_notice where notice_code = #{noticeCode})  and Supp_Code = #{suppCode}
    </update>

    <delete id="deleteTblNoticeById" parameterType="Integer">
        delete from tbl_notice where notice_id = #{noticeId}
    </delete>

    <delete id="deleteTblNoticeByIds" parameterType="String">
        delete from tbl_notice where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <delete id="deleteTblNoticeReplyByNoticeIds" parameterType="String">
        delete from tbl_notice_reply where notice_id in
        <foreach item="noticeId" collection="array" open="(" separator="," close=")">
            #{noticeId}
        </foreach>
    </delete>

    <delete id="deleteTblNoticeReplyByNoticeId" parameterType="Long">
        delete from tbl_notice_reply where notice_id = #{noticeId}
    </delete>

    <insert id="batchTblNoticeReply">
        insert into tbl_notice_reply( content, Supp_Code, Supp_Name, notice_id, Create_Time, reply_time, Create_By, Update_Time, Update_By, Kafka_Status) values
		<foreach item="item" index="index" collection="list" separator=",">
            (  #{item.content}, #{item.suppCode}, #{item.suppName}, #{item.noticeId}, #{item.createTime}, #{item.replyTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.kafkaStatus})
        </foreach>
    </insert>
</mapper>
