package com.datalink.api.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 供应计划明细对象 tbl_sap_delivery_plan_detail
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SapDeliveryPlanDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long deliveryPlanDetailId;

    /** 外键 */
    private Long deliveryPlanId;

    // F/#
    private String formatType;

    // 支給计划/実績月日
    private String supplyPlanDate;
    // 支給计划数量
    private String supplyPlanQuantity;
    // 删除标识
    private String deleteFlag;
    // 预留号
    private String rsnum;
    // 预留行号
    private String rspos;
    // 前月支給残计划
    private BigDecimal lastMonthRemainingPlan;

    // 交货单
    private String deliveryNote;
    // 交货单行号
    private String deliveryNoteItemNumber;
    // 支給実績月日
    private String supplyActualDate;
    // 支給実績数量
    private String supplyActualQuantity;
    // 支給実績重量
    private String supplyActualWeight;
    // 前月支給残实际
    private BigDecimal lastMonthRemainingActual;
}
