package com.datalink.datamanage.service;

import com.datalink.datamanage.domain.TblInventory;
import com.datalink.datamanage.domain.TblInventoryItem;

import java.util.List;

/**
 * 库存Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
public interface ITblInventoryService 
{
    /**
     * 查询库存
     *
     * @param inventoryId 库存ID
     * @return 库存
     */
    public TblInventory selectTblInventoryById(Long inventoryId);

    /**
     * 查询库存列表
     *
     * @param TblInventory 库存
     * @return 库存集合
     */
    public List<TblInventory> selectTblInventoryList(TblInventory TblInventory);

    /**
     * 新增库存
     *
     * @param TblInventory 库存
     * @return 结果
     */
    public int insertTblInventory(TblInventory TblInventory);

    /**
     * 修改库存
     *
     * @param TblInventory 库存
     * @return 结果
     */
    public int updateTblInventory(TblInventory TblInventory);

    /**
     * 修改库存(不包含行项目)
     *
     * @param TblInventory 库存
     * @return 结果
     */
    public int updateTblInventoryOnly(TblInventory TblInventory);

    /**
     * 删除库存
     *
     * @param inventoryId 库存ID
     * @return 结果
     */
    public int deleteTblInventoryById(Long inventoryId);

    /**
     * 批量删除库存
     *
     * @param inventoryIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblInventoryByIds(Long[] inventoryIds);

    /**
     * 查询库存列表(接口专用)
     *
     * @param inventory 库存
     * @return 库存集合
     */
    public List<TblInventory> selectTblInventoryFullList(TblInventory inventory);

    /**
     * 查询库存列表(包含行项目)
     *
     * @param inventory 库存
     * @return 库存集合
     */
    public List<TblInventory> selectTblInventoryWithItemList(TblInventory inventory);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询库存行项目列表
     *
     * @param inventoryItem 库存行项目
     * @return 库存行项目集合
     */
    public List<TblInventoryItem> selectTblInventoryItemList(TblInventoryItem inventoryItem);

    /**
     * 查询库存(不包含行项目)
     *
     * @param inventoryId 库存ID
     * @return 库存
     */
    public TblInventory selectTblInventoryOnlyById(Long inventoryId);
}
