import request from '@/utils/request'

// List kanban data
export function listKanban(query) {
  return request({
    url: '/datamanage/kanban/listKd',
    method: 'get',
    params: query
  })
}
export function listKd(query) {
  return request({
    url: '/datamanage/kanban/listKd',
    method: 'get',
    params: query
  })
}
export function listSv(query) {
  return request({
    url: '/datamanage/kanban/listSv',
    method: 'get',
    params: query
  })
}
export function listLine(query) {
  return request({
    url: '/datamanage/kanban/listLine',
    method: 'get',
    params: query
  })
}

// Print SV spec file
export function printSvSpec(kanbanIds) {
  return request({
    url: '/datamanage/kanban/printSvSpec',
    method: 'post',
    data: kanbanIds
  })
}

// Print Line KD spec file
export function printLineKdSpec(kanbanIds) {
  return request({
    url: '/datamanage/kanban/downloadKanbanKdTxt',
    method: 'post',
    data: kanbanIds
  })
}

// Print Line spec file
export function printLineSpec(kanbanIds) {
  return request({
    url: '/datamanage/kanban/printLineSpec',
    method: 'post',
    data: kanbanIds
  })
}

export function downloadKanbanKdTxt(kanbanIds) {
  return request({
    url: '/datamanage/kanban/downloadKanbanKdTxt',
    method: 'post',
    data: kanbanIds
  })
}
export function downloadKanbanSvTxt(kanbanIds) {
  return request({
    url: '/datamanage/kanban/downloadKanbanSvTxt',
    method: 'post',
    data: kanbanIds
  })
}
export function downloadKanbanLineTxt(kanbanIds) {
  return request({
    url: '/datamanage/kanban/downloadKanbanLineTxt',
    method: 'post',
    data: kanbanIds
  })
}
