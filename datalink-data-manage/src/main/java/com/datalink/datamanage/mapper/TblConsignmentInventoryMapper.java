package com.datalink.datamanage.mapper;

import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblConsignmentInventory;
import com.datalink.datamanage.domain.TblConsignmentInventoryItem;

/**
 * 寄售库存Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
public interface TblConsignmentInventoryMapper 
{
    /**
     * 查询寄售库存
     * 
     * @param consignmentId 寄售库存ID
     * @return 寄售库存
     */
    public TblConsignmentInventory selectTblConsignmentInventoryById(Long consignmentId);

    /**
     * 查询寄售库存列表
     * 
     * @param tblConsignmentInventory 寄售库存
     * @return 寄售库存集合
     */
    public List<TblConsignmentInventory> selectTblConsignmentInventoryList(TblConsignmentInventory tblConsignmentInventory);

    /**
     * 新增寄售库存
     * 
     * @param tblConsignmentInventory 寄售库存
     * @return 结果
     */
    public int insertTblConsignmentInventory(TblConsignmentInventory tblConsignmentInventory);

    /**
     * 修改寄售库存
     * 
     * @param tblConsignmentInventory 寄售库存
     * @return 结果
     */
    public int updateTblConsignmentInventory(TblConsignmentInventory tblConsignmentInventory);

    /**
     * 删除寄售库存
     * 
     * @param consignmentId 寄售库存ID
     * @return 结果
     */
    public int deleteTblConsignmentInventoryById(Long consignmentId);

    /**
     * 批量删除寄售库存
     * 
     * @param consignmentIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblConsignmentInventoryByIds(Long[] consignmentIds);

    /**
     * 批量删除寄售行项目
     * 
     * @param consignmentIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblConsignmentInventoryItemByConsignmentIds(Long[] consignmentIds);
    
    /**
     * 批量新增寄售行项目
     * 
     * @param tblConsignmentInventoryItemList 寄售行项目列表
     * @return 结果
     */
    public int batchTblConsignmentInventoryItem(List<TblConsignmentInventoryItem> tblConsignmentInventoryItemList);
    

    /**
     * 通过寄售库存ID删除寄售行项目信息
     * 
     * @param consignmentId 寄售库存ID
     * @return 结果
     */
    public int deleteTblConsignmentInventoryItemByConsignmentId(Long consignmentId);

    /**
     * 查询寄售库存列表(接口专用)
     *
     * @param consignmentInventory 寄售库存
     * @return 寄售库存集合
     */
    public List<TblConsignmentInventory> selectTblConsignmentInventoryFullList(TblConsignmentInventory consignmentInventory);

    /**
     * 查询寄售库存列表(包含行项目)
     *
     * @param consignmentInventory 寄售库存
     * @return 寄售库存集合
     */
    public List<TblConsignmentInventory> selectTblConsignmentInventoryWithItemList(TblConsignmentInventory consignmentInventory);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询寄售库存行项目列表
     *
     * @param consignmentInventoryItem 寄售库存行项目
     * @return 寄售库存行项目集合
     */
    public List<TblConsignmentInventoryItem> selectTblConsignmentInventoryItemList(TblConsignmentInventoryItem consignmentInventoryItem);

    /**
     * 查询寄售库存(不包含行项目)
     *
     * @param consignment 寄售库存
     * @return 寄售库存
     */
    @DataScope(supplierAlias = "a")
    public TblConsignmentInventory selectTblConsignmentInventoryOnlyById(TblConsignmentInventory consignment);
}
