package com.datalink.datamanage.mapper;

import com.datalink.datamanage.domain.TblSa;
import com.datalink.datamanage.domain.TblSaItem;
import com.datalink.datamanage.domain.TblSaScheduleLine;

import java.util.List;

/**
 * 协议Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public interface TblSaMapper
{
    /**
     * 查询协议
     *
     * @param saId 协议ID
     * @return 协议
     */
    public TblSa selectTblSaById(Long saId);

    /**
     * 查询协议列表
     *
     * @param tblSa 协议
     * @return 协议集合
     */
    public List<TblSa> selectTblSaList(TblSa tblSa);

    /**
     * 新增协议
     *
     * @param tblSa 协议
     * @return 结果
     */
    public int insertTblSa(TblSa tblSa);

    /**
     * 修改协议
     *
     * @param tblSa 协议
     * @return 结果
     */
    public int updateTblSa(TblSa tblSa);

    /**
     * 删除协议
     *
     * @param saId 协议ID
     * @return 结果
     */
    public int deleteTblSaById(Long saId);

    /**
     * 批量删除协议
     *
     * @param saIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblSaByIds(Long[] saIds);

    /**
     * 批量删除协议行项目
     *
     * @param customerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblSaItemBySaIds(Long[] saIds);

    /**
     * 批量新增协议行项目
     *
     * @param tblSaItemList 协议行项目列表
     * @return 结果
     */
    public int batchTblSaItem(List<TblSaItem> tblSaItemList);


    /**
     * 通过协议ID删除协议行项目信息
     *
     * @param saId 协议ID
     * @return 结果
     */
    public int deleteTblSaItemBySaId(Long saId);

    public int batchTblSaScheduleLine(List<TblSaScheduleLine> tblSaScheduleLineList);

    public int deleteTblSaScheduleLineByItemIds(Long[] itemIds);

    public List<TblSa> selectTblSaFullList(TblSa tblSa);

    public Long selectLastId();

    public List<TblSa> selectTblSaWithItemList(TblSa tblSa);
}
