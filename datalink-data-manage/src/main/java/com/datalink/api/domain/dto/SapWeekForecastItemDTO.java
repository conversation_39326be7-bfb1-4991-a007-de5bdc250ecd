package com.datalink.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class SapWeekForecastItemDTO {
    @JsonProperty("FLIEF")
    private String flief;
    
    @JsonProperty("ZDEPOT")
    private String zdepot;
    
    @JsonProperty("NAME1")
    private String name1;
    
    @JsonProperty("SubItem")
    private List<SapWeekForecastSubItemDTO> subItems;
} 