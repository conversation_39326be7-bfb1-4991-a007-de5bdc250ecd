package com.datalink.datamanage.domain;

import java.util.List;

import com.datalink.common.DataType;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;

/**
 * 预测对象 tbl_forecast
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
//TODO: 校验
public class TblForecast extends BaseHeadEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 预测ID */
    private Long forecastId;

    /** 预测编号 */
    @Excel(name = "预测编号")
    @JsonAlias("forecastcode")
    private String forecastCode;

    /** 预测版本号 */
    @Excel(name = "预测版本号")
    private String version;

    /** 收发标志 */
    private String direction;

    /** KafKa发送状态 */
    private String kafkaStatus;

    /** 预测状态 */
    @Excel(name = "预测状态")
    private String status;

    /** 预测行项目信息 */
    private List<TblForecastItem> detail;

    public void setForecastId(Long forecastId)
    {
        this.forecastId = forecastId;
    }

    public Long getForecastId()
    {
        return forecastId;
    }
    public void setForecastCode(String forecastCode)
    {
        this.forecastCode = forecastCode;
    }

    public String getForecastCode()
    {
        return forecastCode;
    }
    public void setVersion(String version)
    {
        this.version = version;
    }

    public String getVersion()
    {
        return version;
    }

    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public List<TblForecastItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblForecastItem> detail)
    {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("forecastId", getForecastId())
            .append("forecastCode", getForecastCode())
            .append("version", getVersion())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("status", getStatus())
            .append("tblForecastItemList", getDetail())
            .toString();
    }

    @Override
    public DataType getObjectType() {
        return DataType.FORECAST_TYPE;
    }
}
