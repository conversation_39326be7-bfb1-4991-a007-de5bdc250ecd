package com.datalink.api.domain;

import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class SapMonthForecast extends BaseEntity {
    private Long id;
    private String flief;
    private String zdepot;
    private String name1;
    private String isRead;   // 是否已读(Y/N)

    private String zmonth1;
    private String zmonth2;
    private String zmonth3;
    
    private List<SapMonthForecastItem> subItems;
} 