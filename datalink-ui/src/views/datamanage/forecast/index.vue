<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item :label="$t('forecast.form.forecastCode')" prop="forecastCode">
        <el-input
          v-model="queryParams.forecastCode"
          :placeholder="$t('forecast.form.inputForecastCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecast.form.version')" prop="version">
        <el-input
          v-model="queryParams.version"
          :placeholder="$t('forecast.form.inputVersion')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'I'" :label="$t('forecast.form.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('forecast.form.inputCompCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'O'" :label="$t('forecast.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('forecast.form.inputSuppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'O'" :label="$t('forecast.form.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('forecast.form.inputSuppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecast.form.plantCode')" prop="plantCode">
        <el-input
          v-model="queryParams.plantCode"
          :placeholder="$t('forecast.form.inputPlantCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('forecast.form.plantName')" prop="plantName">
        <el-input
          v-model="queryParams.plantName"
          :placeholder="$t('forecast.form.inputPlantName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('forecast.form.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('forecast.form.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['datamanage:forecast:export']"
        >{{ $t('forecast.form.export') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="forecastList" @selection-change="handleSelectionChange">
      <el-table-column :label="$t('forecast.table.forecastCode')" align="center">
        <template slot-scope="scope">
          <router-link :to="{name: 'ForecastDetail', params: {forecastId: scope.row.forecastId}}" class="link-type">
            <span>{{ scope.row.forecastCode }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('forecast.table.version')" align="center" prop="version" />
      <el-table-column v-if="queryParams.direction === 'I'" :label="$t('forecast.table.compCode')" align="center" prop="compCode" />
      <el-table-column v-if="queryParams.direction === 'O'" :label="$t('forecast.table.suppCode')" align="center" prop="suppCode" />
      <el-table-column v-if="queryParams.direction === 'O'" :label="$t('forecast.table.suppName')" align="center" prop="suppName" />
      <el-table-column :label="$t('forecast.table.plantCode')" align="center" prop="plantCode" />
      <el-table-column :label="$t('forecast.table.plantName')" align="center" prop="plantName" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="$t('forecast.dialog.title')" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('forecast.dialog.forecastCode')" prop="forecastcode">
          <el-input v-model="form.forecastcode" :placeholder="$t('forecast.dialog.inputForecastCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.version')" prop="version">
          <el-input v-model="form.version" :placeholder="$t('forecast.dialog.inputVersion')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.compCode')" prop="compcode">
          <el-input v-model="form.compcode" :placeholder="$t('forecast.dialog.inputCompCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.plantCode')" prop="plantcode">
          <el-input v-model="form.plantcode" :placeholder="$t('forecast.dialog.inputPlantCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.plantName')" prop="plantname">
          <el-input v-model="form.plantname" :placeholder="$t('forecast.dialog.inputPlantName')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.suppCode')" prop="suppcode">
          <el-input v-model="form.suppcode" :placeholder="$t('forecast.dialog.inputSuppCode')" />
        </el-form-item>
        <el-form-item :label="$t('forecast.dialog.suppName')" prop="suppname">
          <el-input v-model="form.suppname" :placeholder="$t('forecast.dialog.inputSuppName')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('forecast.dialog.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('forecast.dialog.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listForecast, getForecast, delForecast, addForecast, updateForecast, exportForecast } from "@/api/datamanage/forecast";

export default {
  name: "Forecast",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预测表格数据
      forecastList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        forecastCode: null,
        version: null,
        compCode: null,
        plantCode: null,
        plantName: null,
        direction: 'I',
        orderByColumn: 'createTime',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        forecastcode: [
          { required: true, message: this.$t('forecast.dialog.inputForecastCode'), trigger: "blur" }
        ],
        version: [
          { required: true, message: this.$t('forecast.dialog.inputVersion'), trigger: "blur" }
        ],
        compcode: [
          { required: true, message: this.$t('forecast.dialog.inputCompCode'), trigger: "blur" }
        ],
        plantcode: [
          { required: true, message: this.$t('forecast.dialog.inputPlantCode'), trigger: "blur" }
        ],
        plantname: [
          { required: true, message: this.$t('forecast.dialog.inputPlantName'), trigger: "blur" }
        ],
        suppcode: [
          { required: true, message: this.$t('forecast.dialog.inputSuppCode'), trigger: "blur" }
        ],
        suppname: [
          { required: true, message: this.$t('forecast.dialog.inputSuppName'), trigger: "blur" }
        ]
      }
    };
  },
  created() {
    if (this.$route.path.toLowerCase().endsWith('recv/forecast')){
      this.queryParams.direction = 'I';
    } else {
      this.queryParams.direction = 'O';
    }
    this.getList();
  },
  methods: {
    /** 查询预测列表 */
    getList() {
      this.loading = true;
      listForecast(this.queryParams).then(response => {
        this.forecastList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        forecastId: null,
        forecastCode: null,
        version: null,
        compCode: null,
        plantCode: null,
        plantName: null,
        suppCode: null,
        suppName: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.forecastid);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('forecast.dialog.titleAdd');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const forecastId = row.forecastid || this.ids;
      this.$router.push("/forecast/detail/" + forecastId);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.forecastid != null) {
            updateForecast(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('forecast.messages.updateSuccess'));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addForecast(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('forecast.messages.addSuccess'));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const forecastids = row.forecastid || this.ids;
      this.$confirm(
        this.$t('forecast.messages.deleteConfirm', { id: forecastids }),
        this.$t('common.warning'),
        {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: "warning"
        }
      ).then(() => {
        return delForecast(forecastids);
      }).then(() => {
        this.getList();
        this.msgSuccess(this.$t('forecast.messages.deleteSuccess'));
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t('forecast.messages.exportConfirm'),
        this.$t('common.warning'),
        {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: "warning"
        }
      ).then(() => {
        return exportForecast(queryParams);
      }).then(response => {
        this.download(response.msg);
      }).catch(() => {});
    }
  }
};
</script>
