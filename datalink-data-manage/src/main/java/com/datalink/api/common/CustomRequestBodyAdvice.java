package com.datalink.api.common;

import com.datalink.api.domain.*;
import com.datalink.api.domain.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

@Slf4j
@ControllerAdvice
public class CustomRequestBodyAdvice extends RequestBodyAdviceAdapter {

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        Class<?> rawType;
        if (targetType instanceof ParameterizedType) {
            rawType = (Class<?>) ((ParameterizedType) targetType).getRawType();
        } else if (targetType instanceof Class) {
            rawType = (Class<?>) targetType;
        } else {
            return inputMessage; // 不支持的类型直接返回
        }

        if (rawType.equals(SapOrderRequest.class) || rawType.equals(SapSupplierRequest.class)
                || rawType.equals(TblForecastBatchRequestDTO.class) || rawType.equals(SapMaterialItem.class)
                || rawType.equals(AcceptanceDetailRequest.class)) {
            try {
                // 获取接口方法名
                String methodName = parameter.getMethod().getName();
                String className = parameter.getContainingClass().getSimpleName();

                // 读取原始请求体
                byte[] body = IOUtils.toByteArray(inputMessage.getBody());
                // 打印接口名和原始请求报文
                log.info("接口名称: {}.{}", className, methodName);
                log.info("接口原始入参: {}", new String(body, "UTF-8"));

                // 返回可重复读取的输入流
                return new HttpInputMessage() {
                    @Override
                    public InputStream getBody() {
                        return new ByteArrayInputStream(body);
                    }

                    @Override
                    public HttpHeaders getHeaders() {
                        return inputMessage.getHeaders();
                    }
                };
            } catch (Exception e) {
                log.error("读取请求体失败", e);
            }
        }
        return inputMessage;
    }
}