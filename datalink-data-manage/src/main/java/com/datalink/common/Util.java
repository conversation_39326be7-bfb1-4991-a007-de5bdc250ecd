package com.datalink.common;

import com.datalink.api.domain.SapGrItem;
import com.datalink.api.domain.SapListRequst;
import com.datalink.common.utils.DateUtils;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.domain.TblFeedbackItem;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class Util {
    public static void changeCaseJsonNaming(Class clazz, Class<? extends PropertyNamingStrategy> strategy) throws NoSuchFieldException, IllegalAccessException {
        JsonNaming naming = (JsonNaming) clazz.getAnnotation(JsonNaming.class);
        InvocationHandler handler = Proxy.getInvocationHandler(naming);
        Field memberValuesField= handler.getClass().getDeclaredField("memberValues");

        memberValuesField.setAccessible(true);

        Map memberValues=(Map)memberValuesField.get(handler);

        memberValues.put("value", strategy);
    }

    public static String fillBefore(String str, int len, char ch){
        StringBuilder sb = new StringBuilder();
        for(int i = 0; i < len - str.length(); i++){
            sb.append(ch);
        }
        sb.append(str);
        return sb.toString();
    }

    public static String fillAfter(String str, int len, char ch, Charset charset){
        String str2 = str;
        if(null==str){
            str2 = "";
        }

        int strLen = getStrLen(str2, charset);
        if (strLen==len){
            return str2;
        }else if (strLen>len){
            StringBuilder result = new StringBuilder();
            int currentLength = 0;

            for (char c : str2.toCharArray()) {
                byte[] charBytes = String.valueOf(c).getBytes(charset);

                // 如果加上当前字符后超出限制，停止
                if (currentLength + charBytes.length > len) {
                    break;
                }

                result.append(c);
                currentLength += charBytes.length;
            }
            while (currentLength < len) {
                result.append(ch);
                currentLength++;
            }

            return result.toString();
        }else{
            StringBuilder sb = new StringBuilder(str2);
            for(int i = 0; i < len - strLen; i++){
                sb.append(ch);
            }
            return sb.toString();
        }



    }

    public static String fillAfter(String str, int len, char ch){
        return fillAfter(str, len, ch, Charset.forName("MS932"));
    }

    public static int getStrLen(String str, Charset charset){
        return str.getBytes(charset).length;
    }

    public static int getStrLen(String str){
        return str.getBytes().length;
    }
}
