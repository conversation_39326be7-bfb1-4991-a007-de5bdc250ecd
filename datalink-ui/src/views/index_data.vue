<template>
  <div class="dashboard-container">
    <div class="chart-panel">
      <div class="panel-header">
        <h3>{{ $t("dashboard.orderAnalysis") }}</h3>
        <!-- <div class="header-right">
          <el-date-picker v-model="dateRange" type="daterange" :start-placeholder="$t('common.startDate')"
            :end-placeholder="$t('common.endDate')" @change="handleDateRangeChange" />
        </div> -->
      </div>
      <div class="data-overview">
        <div class="overview-item">
          <span class="label">{{ $t("dashboard.totalOrders") }}</span>
          <span class="value">{{ statisticsData.totalOrders }}</span>
          <span class="compare" :class="{
            up: statisticsData.orderGrowth >= 0,
            down: statisticsData.orderGrowth < 0,
          }">
            {{ $t('dashboard.compareToYesterday') }} {{ statisticsData.orderGrowth }}%
          </span>
        </div>
        <div class="overview-item">
          <span class="label">{{ $t("dashboard.completedOrders") }}</span>
          <span class="value">{{ statisticsData.completedOrders }}</span>
          <span class="compare" :class="{
            up: statisticsData.completionGrowth >= 0,
            down: statisticsData.completionGrowth < 0,
          }">
            {{ $t('dashboard.compareToYesterday') }} {{ statisticsData.completionGrowth }}%
          </span>
        </div>
        <div class="overview-item">
          <span class="label">{{ $t("dashboard.pendingOrders") }}</span>
          <span class="value">{{ statisticsData.pendingOrders }}</span>
          <span class="compare" :class="{
            up: statisticsData.pendingGrowth >= 0,
            down: statisticsData.pendingGrowth < 0,
          }">
            {{ $t('dashboard.compareToYesterday') }} {{ statisticsData.pendingGrowth }}%
          </span>
        </div>
      </div>
      <div class="chart-container" ref="chartContainer"></div>
    </div>
    <!-- <div v-else class="carrier-dashboard">
      <span>{{ $t('common.noData') }}</span>
    </div> -->
  </div>
</template>

<script>
import * as echarts from "echarts";
import { listOrder } from "@/api/datamanage/order";

export default {
  name: "Dashboard",
  data() {
    return {
      statisticsData: {
        totalOrders: 0,
        orderGrowth: 0,
        completedOrders: 0,
        completionGrowth: 0,
        pendingOrders: 0,
        pendingGrowth: 0,
      },
      dateRange: [],
      chart: null,
      chartData: [],
    };
  },
  computed: {
    // 通过计算属性获取当前语言
    language() {
      return this.$store.getters.language
    },
    // 判断是否为运输商用户
    isCarrier() {
      const roles = this.$store.state.user.roles;
      return roles && roles.some(role => role === 'carrier');
    }
  },
  watch: {
    language(newLang, oldLang) {
      if (!this.isCarrier) {
        this.updateChart();
      }
    }
  },
  mounted() {
    if (!this.isCarrier) {
      this.fetchOrderData(); // Only fetch data if not a carrier
      this.initChart();
      window.addEventListener("resize", this.resizeChart);
    }
  },
  beforeDestroy() {
    if (!this.isCarrier) {
      window.removeEventListener("resize", this.resizeChart);
      if (this.chart) {
        this.chart.dispose();
      }
    }
  },
  methods: {
    fetchOrderData() {
      const today = new Date();  // Current date and time
      const sevenDaysAgo = new Date(today);
      sevenDaysAgo.setDate(today.getDate() - 7);
      sevenDaysAgo.setHours(0, 0, 0, 0);  // Set time to 00:00:00

      // Format the dates as 'YYYY-MM-DD HH:mm:ss' for API usage
      const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      };

      const params = {
        pageNum: 1,
        pageSize: 100,  // or any number that suits the requirement
        orderByColumn: "createTime",
        isAsc: "",  // Ensure the sorting is by ascending order of creation time
        isRead: "",
        isComplete: "",
        // Add the date range (from 7 days ago 00:00:00 to now)
        params: {
          createTimeBegin: formatDateTime(sevenDaysAgo),  // Start date: 7 days ago at 00:00:00
          createTimeEnd: formatDateTime(today),           // End date: current time today
        }
      };

      listOrder(params)
        .then((response) => {
          const { rows } = response;

          // Ensure rows are sorted by createTime in ascending order
          rows.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));

          // Calculate total, completed, and pending orders across all data
          const totalOrders = rows.length;
          const completedOrders = rows.filter((order) => order.isComplete === "Y").length;
          console.log(rows)
          const pendingOrders = rows.filter((order) => order.isComplete === "N").length;

          // Get today's and yesterday's orders
          const today = this.formatDate(new Date());
          const yesterday = this.formatDate(new Date(new Date().setDate(new Date().getDate() - 1)));

          // Split orders into two groups: today and yesterday
          const todayOrders = rows.filter((order) => order.createTime.startsWith(today));
          const yesterdayOrders = rows.filter((order) => order.createTime.startsWith(yesterday));

          // Calculate today's totals
          const totalTodayOrders = todayOrders.length;
          const completedTodayOrders = todayOrders.filter((order) => order.isComplete === "Y").length;
          const pendingTodayOrders = todayOrders.filter((order) => order.isComplete === "N").length;

          // Calculate yesterday's totals
          const totalYesterdayOrders = yesterdayOrders.length;
          const completedYesterdayOrders = yesterdayOrders.filter((order) => order.isComplete === "Y").length;
          const pendingYesterdayOrders = yesterdayOrders.filter((order) => order.isComplete === "N").length;

          // Calculate growth: (today - yesterday) / yesterday * 100
          this.statisticsData.orderGrowth = this.calculateGrowth(totalTodayOrders, totalYesterdayOrders);
          this.statisticsData.completionGrowth = this.calculateGrowth(completedTodayOrders, completedYesterdayOrders);
          this.statisticsData.pendingGrowth = this.calculateGrowth(pendingTodayOrders, pendingYesterdayOrders);

          // Set overall statistics data
          this.statisticsData.totalOrders = totalOrders;
          this.statisticsData.completedOrders = completedOrders;
          this.statisticsData.pendingOrders = pendingOrders;

          // Prepare chart data
          this.chartData = this.processChartData(rows);
          this.updateChart();
        })
        .catch((error) => {
          console.error("Error fetching order data:", error);
        });
    },

    // Helper function to calculate growth percentage
    calculateGrowth(today, yesterday) {
      if (yesterday === 0) {
        return today > 0 ? 100 : 0; // If no data yesterday, any increase is 100% growth
      }
      return (((today - yesterday) / yesterday) * 100).toFixed(2);
    },

    // Helper function to format a Date object as 'YYYY-MM-DD'
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    processChartData(orders) {
      const orderMap = {};
      orders.forEach((order) => {
        const date = order.createTime.split(" ")[0]; // Extract only the date
        if (!orderMap[date]) {
          orderMap[date] = { total: 0, completed: 0, pending: 0 };
        }
        orderMap[date].total++;
        if (order.isComplete === "Y") {
          orderMap[date].completed++;
        } else {
          orderMap[date].pending++;
        }
      });
      return Object.entries(orderMap).map(([date, stats]) => ({
        date,
        total: stats.total,
        completed: stats.completed,
        pending: stats.pending,
      }));
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer);
      this.updateChart();
    },
    updateChart() {
      const dates = this.chartData.map((item) => item.date);
      const totalOrders = this.chartData.map((item) => item.total);
      const completedOrders = this.chartData.map((item) => item.completed);
      const pendingOrders = this.chartData.map((item) => item.pending);

      const option = {
        backgroundColor: "",  // Light background for a clean look

        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "none",
          },
          backgroundColor: "rgba(240, 255, 244, 0.9)", // Light, refreshing pastel green background
          borderColor: "#d4e6d1", // Subtle greenish border to match the background
          borderWidth: 1,
          textStyle: {
            color: "#2c3e50", // Dark color for contrast with the light background
          },
        },
        legend: {
          top: "0%",
          x: "center",
          textStyle: {
            color: "#2c3e50",
          },
          data: [
            this.$t("dashboard.chart.totalOrders"),
            this.$t("dashboard.chart.completedOrders"),
            this.$t("dashboard.chart.pendingOrders"),
          ],
        },
        grid: {
          left: "2%",
          right: "2%",
          bottom: "2%",
          top: "7%",
          containLabel: true
        },
        xAxis: [
          {
            type: "category",
            data: dates,
            axisLine: {
              lineStyle: {
                color: "#90979c",  // Lighter color for axis lines
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: "#2c3e50",
              interval: 0,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLine: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: "#e1e8ee",  // Soft gridlines
              },
            },
            axisLabel: {
              color: "#2c3e50",
            },
          },
        ],
        // dataZoom: [
        //   {
        //     type: "slider",
        //     show: true,
        //     xAxisIndex: [0],
        //     start: 0,
        //     end: 100,
        //     height: 20,
        //     handleIcon:
        //       "M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z",
        //     handleSize: "100%",
        //     handleStyle: {
        //       color: "#d3dee5",
        //     },
        //     textStyle: {
        //       color: "#2c3e50",
        //     },
        //     borderColor: "#dcdfe6",
        //   },
        //   {
        //     type: "inside",
        //     start: 0,
        //     end: 100,
        //   },
        // ],
        series: [
          {
            name: this.$t("dashboard.chart.totalOrders"),
            type: "line",
            smooth: true,
            data: totalOrders,
            symbol: "circle",
            symbolSize: 8,
            lineStyle: {
              color: "#409EFF",
              width: 2,
            },
            itemStyle: {
              color: "#409EFF",
            },
          },
          {
            name: this.$t("dashboard.chart.completedOrders"),
            type: "bar",
            barMaxWidth: 30,
            data: completedOrders,
            itemStyle: {
              color: "#67C23A",
              label: {
                show: true,
                position: "inside",
                color: "#ffffff", // White text inside bars
                fontWeight: "bold",
              },
            },
          },
          {
            name: this.$t("dashboard.chart.pendingOrders"),
            type: "bar",
            barMaxWidth: 30,
            data: pendingOrders,
            itemStyle: {
              color: "#E6A23C",
              label: {
                show: true,
                position: "inside",
                color: "#ffffff", // White text inside bars
                fontWeight: "bold",
              },
            },
          },
        ],
      };
      this.chart.setOption(option);
    },
    resizeChart() {
      this.chart && this.chart.resize();
    },
    handleDateRangeChange() {
      // Optionally refetch data based on date range change
      this.fetchOrderData();
    },
  },
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background: #f0f2f5;
  min-height: calc(100vh - 84px);

  .chart-panel {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 20px;

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #1f2f3d;
      }
    }

    .data-overview {
      display: flex;
      margin-bottom: 24px;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 24px;

      .overview-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0 24px;
        position: relative;

        &:not(:last-child):after {
          content: "";
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 40%;
          background: #ebeef5;
        }

        .label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }

        .value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 8px;
        }

        .compare {
          font-size: 13px;

          &.up {
            color: #67c23a;

            &:before {
              content: "↑ ";
            }
          }

          &.down {
            color: #f56c6c;

            &:before {
              content: "↓ ";
            }
          }
        }
      }
    }

    .chart-container {
      height: calc(60vh);
    }
  }
  .carrier-dashboard {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(60vh);
  }
}
</style>
