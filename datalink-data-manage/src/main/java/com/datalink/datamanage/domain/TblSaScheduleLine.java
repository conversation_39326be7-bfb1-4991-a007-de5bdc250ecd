package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 协议计划行对象 tbl_sa_schedule_line
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public class TblSaScheduleLine extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 计划行ID */
    private Long lineId;

    /** 日期类型 */
    @Excel(name = "日期类型")
    private String dateType;

    /** 计划行日期从 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "计划行日期从", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dateFrom;

    /** 计划行日期直到 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "计划行日期直到", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dateTo;

    /** 批准数量 */
    @Excel(name = "批准数量")
    private BigDecimal releasedQuantity;

    /** 计划行累计接收数量 */
    @Excel(name = "计划行累计接收数量")
    private BigDecimal cumulativeReceivedQuantity;

    /** 预留字段1 */
    @Excel(name = "预留字段1")
    private String info1;

    /** 预留字段2 */
    @Excel(name = "预留字段2")
    private String info2;

    /** 预留字段3 */
    @Excel(name = "预留字段3")
    private String info3;

    /** 协议行ID */
    @Excel(name = "协议行ID")
    private Long itemId;

    public void setLineId(Long lineId)
    {
        this.lineId = lineId;
    }

    public Long getLineId()
    {
        return lineId;
    }
    public void setDateType(String dateType)
    {
        this.dateType = dateType;
    }

    public String getDateType()
    {
        return dateType;
    }
    public void setDateFrom(Date dateFrom)
    {
        this.dateFrom = dateFrom;
    }

    public Date getDateFrom()
    {
        return dateFrom;
    }
    public void setDateTo(Date dateTo)
    {
        this.dateTo = dateTo;
    }

    public Date getDateTo()
    {
        return dateTo;
    }
    public void setReleasedQuantity(BigDecimal releasedQuantity)
    {
        this.releasedQuantity = releasedQuantity;
    }

    public BigDecimal getReleasedQuantity()
    {
        return releasedQuantity;
    }
    public void setCumulativeReceivedQuantity(BigDecimal cumulativeReceivedQuantity)
    {
        this.cumulativeReceivedQuantity = cumulativeReceivedQuantity;
    }

    public BigDecimal getCumulativeReceivedQuantity()
    {
        return cumulativeReceivedQuantity;
    }
    public void setInfo1(String info1)
    {
        this.info1 = info1;
    }

    public String getInfo1()
    {
        return info1;
    }
    public void setInfo2(String info2)
    {
        this.info2 = info2;
    }

    public String getInfo2()
    {
        return info2;
    }
    public void setInfo3(String info3)
    {
        this.info3 = info3;
    }

    public String getInfo3()
    {
        return info3;
    }
    public void setItemId(Long itemId)
    {
        this.itemId = itemId;
    }

    public Long getItemId()
    {
        return itemId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("lineId", getLineId())
            .append("dateType", getDateType())
            .append("dateFrom", getDateFrom())
            .append("dateTo", getDateTo())
            .append("releasedQuantity", getReleasedQuantity())
            .append("cumulativeReceivedQuantity", getCumulativeReceivedQuantity())
            .append("info1", getInfo1())
            .append("info2", getInfo2())
            .append("info3", getInfo3())
            .append("createBy", getCreateBy())
            .append("itemId", getItemId())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
