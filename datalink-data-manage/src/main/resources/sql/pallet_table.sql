-- ----------------------------
-- Table structure for tbl_pallet
-- ----------------------------
DROP TABLE IF EXISTS `tbl_pallet`;
CREATE TABLE `tbl_pallet` (
  `Pallet_ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '托盘ID',
  `Material_Plant_ID` bigint(20) NOT NULL COMMENT '物料工厂ID',
  `SNP_Quantity` int(11) DEFAULT NULL COMMENT '整托包含SNP数量',
  `Pallet_Length` int(11) DEFAULT NULL COMMENT '托长(mm)',
  `Pallet_Width` int(11) DEFAULT NULL COMMENT '托宽(mm)',
  `Pallet_Height` int(11) DEFAULT NULL COMMENT '托高(mm)',
  `Container_Type` varchar(50) DEFAULT NULL COMMENT '容器种类',
  `Create_Time` datetime DEFAULT NULL COMMENT '创建时间',
  `Create_By` varchar(64) DEFAULT NULL COMMENT '创建者',
  `Update_Time` datetime DEFAULT NULL COMMENT '更新时间',
  `Update_By` varchar(64) DEFAULT NULL COMMENT '更新者',
  `Del_Flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`Pallet_ID`),
  UNIQUE KEY `idx_material_plant` (`Material_Plant_ID`) COMMENT '物料工厂唯一索引',
  KEY `idx_material_plant_id` (`Material_Plant_ID`) COMMENT '物料工厂ID索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='托盘信息表'; 