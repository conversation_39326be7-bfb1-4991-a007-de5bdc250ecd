package com.datalink.datamanage.mapper;

import com.datalink.datamanage.domain.TblMaterialPlant;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料工厂信息Mapper接口
 */
public interface TblMaterialPlantMapper {
    /**
     * 查询物料工厂信息
     *
     * @param materialPlantId 物料工厂信息ID
     * @return 物料工厂信息
     */
    public TblMaterialPlant selectTblMaterialPlantById(Long materialPlantId);

    /**
     * 查询物料工厂信息列表
     *
     * @param tblMaterialPlant 物料工厂信息
     * @return 物料工厂信息集合
     */
    public List<TblMaterialPlant> selectTblMaterialPlantList(TblMaterialPlant tblMaterialPlant);

    /**
     * 根据物料ID查询物料工厂信息
     *
     * @param materialId 物料ID
     * @return 物料工厂信息集合
     */
    public List<TblMaterialPlant> selectTblMaterialPlantByMaterialId(Long materialId);

    /**
     * 新增物料工厂信息
     *
     * @param tblMaterialPlant 物料工厂信息
     * @return 结果
     */
    public int insertTblMaterialPlant(TblMaterialPlant tblMaterialPlant);

    /**
     * 批量新增物料工厂信息
     *
     * @param tblMaterialPlantList 物料工厂信息列表
     * @return 结果
     */
    public int batchInsertTblMaterialPlant(List<TblMaterialPlant> tblMaterialPlantList);

    /**
     * 修改物料工厂信息
     *
     * @param tblMaterialPlant 物料工厂信息
     * @return 结果
     */
    public int updateTblMaterialPlant(TblMaterialPlant tblMaterialPlant);

    /**
     * 删除物料工厂信息
     *
     * @param materialPlantId 物料工厂信息ID
     * @return 结果
     */
    public int deleteTblMaterialPlantById(Long materialPlantId);

    /**
     * 批量删除物料工厂信息
     *
     * @param materialPlantIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblMaterialPlantByIds(Long[] materialPlantIds);

    /**
     * 根据物料ID删除物料工厂信息
     *
     * @param materialId 物料ID
     * @return 结果
     */
    public int deleteTblMaterialPlantByMaterialId(Long materialId);
} 