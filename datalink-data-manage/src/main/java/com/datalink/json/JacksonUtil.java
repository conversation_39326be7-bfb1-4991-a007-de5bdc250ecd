package com.datalink.json;

import com.datalink.common.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

public class JacksonUtil {

    private static final Logger log = LoggerFactory.getLogger(JacksonUtil.class);
    /***
     * 忽略空对象
     */
    private final static ObjectMapper objectMapper = new ObjectMapper();

    /***
     * 操作空对象时使用
     */
    private final static ObjectMapper objectMapperIncludeAll = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);

        objectMapperIncludeAll.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        objectMapperIncludeAll.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapperIncludeAll.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        objectMapperIncludeAll.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapperIncludeAll.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        objectMapperIncludeAll.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapperIncludeAll.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }

    /**
     * 创建  objectNode 对象
     *
     * @return
     */
    public static ObjectNode createObjectNode() {
        return objectMapper.createObjectNode();
    }

    /**
     * 基于现有 source 数据创建  objectNode 对象
     *
     * @return
     */
    public static ObjectNode createObjectNode(String name, JsonNode source) {
        ObjectNode result = objectMapper.createObjectNode();
        if (source != null && StringUtils.isNotBlank(name)) {
            result.set(name, source);
        }
        return result;
    }

    /**
     * 创建 arrayNode 对象
     *
     * @return
     */
    public static ArrayNode createArrayNode() {
        return objectMapper.createArrayNode();
    }

    public static ArrayNode createArrayNode(JsonNode... children) {
        ArrayNode array = objectMapper.createArrayNode();
        for (JsonNode child : children) {
            array.add(child);
        }
        return array;
    }

    public static <T> ArrayNode createArrayNode(Collection<T> collection,
                                                Function<T, JsonNode> converter) {
        ArrayNode array = objectMapper.createArrayNode();
        for (T child : collection) {
            array.add(converter.apply(child));
        }
        return array;
    }

    /**
     * 基于现有 source 数据创建 arrayNode 对象
     *
     * @param source
     * @return
     */
    public static ArrayNode createArrayNode(JsonNode source) {
        ArrayNode result = objectMapper.createArrayNode();
        if (source != null) {
            if (source.isArray()) {
                result.addAll((ArrayNode)source);
            } else {
                result.add(source);
            }
        }
        return result;
    }

    /**
     * java 对象 转换成 json string 字符串
     *
     * @param object
     * @return
     */
    public static String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("toJsonString error", e);
        }
        return null;
    }

    /**
     * Java 对象 转换成 json string字符串，不忽略empty和null对象
     *
     * @param object
     * @return
     */
    public static String toJsonStringIncludeAll(Object object) {
        try {
            return objectMapperIncludeAll.writeValueAsString(object);
        } catch (Exception e) {
            log.error("toJsonStringIncludeAll error", e);
        }
        return null;
    }

    /**
     * 将java 对象，或者json字符串转换成 json 对象
     * String 调用 readTree
     * object 调用
     *
     * @param input
     * @return valueToTree
     */
    public static JsonNode toJsonNode(Object input) {
        return (input instanceof String) ? readTree((String)input) : objectMapper.valueToTree(input);
    }

    public static JsonNode toJsonNodeIncludeAll(Object input) {
        return (input instanceof String) ? readTree((String)input) : objectMapperIncludeAll.valueToTree(input);
    }

    /**
     * json string  字符串 转换成 jsonNode 对象
     *
     * @param input
     * @return
     * @throws IOException
     */
    public static JsonNode readTree(final String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        try {
            return objectMapper.readTree(input);
        } catch (IOException e) {
            log.error("readTree", e);
        }

        return null;
    }

    /**
     * json string 字符串转换成 java 对象
     *
     * @param input
     * @param clazz
     * @param <T>
     * @return
     * @throws IOException
     */
    public static <T> T stringToObject(final String input, Class<T> clazz) throws IOException {
        return objectMapper.readValue(input, clazz);
    }

    /**
     * java 对象 转换 jsonNode 对象
     *
     * @param object
     * @return
     */
    public static JsonNode valueToTree(Object object) {
        return objectMapper.valueToTree(object);
    }

    /**
     * jsonNode 对象 转换 java 对象
     */
    public static <T> T treeToValue(JsonNode node, Class<T> clazz) throws JsonProcessingException {
        return objectMapper.treeToValue(node, clazz);
    }

    /**
     * jsonNode 转换  list 对象
     *
     * @param jsonNode
     * @param tClass
     * @param <T>      Class
     * @return
     */
    public static <T> List<T> getListForJsonNode(JsonNode jsonNode, Class<T> tClass) {
        JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, tClass);
        ObjectReader objectReader = objectMapper.readerFor(javaType);
        try {
            return objectReader.readValue(jsonNode);
        } catch (Exception e) {
            log.error("readValue", e);
        }
        return null;
    }

    public static <T> T parseObject(String input, Class<T> clazz) throws JsonProcessingException {
        try {
            return objectMapper.readValue(input, clazz);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    public static <T> T parseObject(String input, TypeReference<T> reference) throws JsonProcessingException {
        try {
            return objectMapper.readValue(input, reference);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    public static <T> T parseObject(InputStream is, Class<T> clazz) throws JsonProcessingException {
        try {
            return objectMapper.readValue(is, clazz);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    public static <T> List<T> parseList(String input, Class<T> clazz) throws JsonProcessingException {
        JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        ObjectReader objectReader = objectMapper.readerFor(javaType);
        try {
            return objectReader.readValue(input);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    public static <T> List<T> parseList(InputStream is, Class<T> clazz) throws JsonProcessingException {
        JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        ObjectReader objectReader = objectMapper.readerFor(javaType);
        try {
            return objectReader.readValue(is);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    public static <T> List<T> parseListFromJsonString(String input, Class<T> clazz) {
        try {
            return parseList(input, clazz);
        } catch (JsonProcessingException e) {
        }
        return new ArrayList<>(0);
    }

    public static <T> T parseObject(JsonNode input, Class<T> clazz) throws JsonParseException {
        try {
            return objectMapper.treeToValue(input, clazz);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    public static <T> List<T> parseList(JsonNode input, Class<T> clazz) throws JsonParseException {
        JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, clazz);
        ObjectReader objectReader = objectMapper.readerFor(javaType);
        try {
            return objectReader.readValue(input);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    //下面的mapper是特殊定制化的, 传入的

    public static <T> T parseObject(ObjectMapper mapper, String input, Class<T> clazz) throws JsonProcessingException {
        try {
            return mapper.readValue(input, clazz);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    //指定的mapper
    public static <T> List<T> parseList(ObjectMapper mapper, String input, Class<T> clazz)
            throws JsonProcessingException {
        JavaType javaType = mapper.getTypeFactory().constructCollectionType(List.class, clazz);
        ObjectReader objectReader = mapper.readerFor(javaType);
        try {
            return objectReader.readValue(input);
        } catch (IOException e) {
            throw new JsonParseException(null, "", e);
        }
    }

    /***
     * 转换为Map
     * @param jsonNode
     * @return
     */
    public static Map parseMap(JsonNode jsonNode) {
        return objectMapper.convertValue(jsonNode, Map.class);
    }
}
