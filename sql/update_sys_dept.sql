-- 修改sys_dept表，添加供应商接口所需的字段
ALTER TABLE sys_dept ADD COLUMN supplier_code varchar(10) COMMENT '供应商代码';
ALTER TABLE sys_dept ADD COLUMN supplier_type varchar(4) COMMENT '供应商类型';
ALTER TABLE sys_dept ADD COLUMN country varchar(3) COMMENT '国家';
ALTER TABLE sys_dept ADD COLUMN region varchar(3) COMMENT '地区';
ALTER TABLE sys_dept ADD COLUMN address varchar(60) COMMENT '地址';
ALTER TABLE sys_dept ADD COLUMN postcode varchar(10) COMMENT '邮编';
ALTER TABLE sys_dept ADD COLUMN fax varchar(31) COMMENT '传真';

-- 创建供应商代码唯一索引
CREATE UNIQUE INDEX idx_supplier_code ON sys_dept(supplier_code) WHERE supplier_code IS NOT NULL;

-- 更新现有数据，设置默认值
UPDATE sys_dept SET supplier_code = NULL, supplier_type = NULL, country = NULL, region = NULL, address = NULL, postcode = NULL, fax = NULL; 