package com.datalink.api.common;

import com.datalink.common.utils.StringUtils;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.TimeZone;

@ApiModel("通用请求信息")
public class CommonRequestEntity {
    @ApiModelProperty("查询模式")
    @NotNull(message = "Mode不能为空")
    @Pattern(regexp = "^TIME$|^CURSOR$", message = "mode格式不正确")
    private String mode;

    @ApiModelProperty("查询的起始位置")
    @NotNull(message = "起始点不能为空")
    private String startingPoint;

    @ApiModelProperty("是否包含起始位置")
    @Pattern(regexp = "^TRUE$|^FALSE$", message = "includingStartingPoint格式不正确")
    private String includingStartingPoint;

    @ApiModelProperty("一次请求的信息数量")
    @Digits(message = "offset格式不正确", integer = 3, fraction = 0)
    @DecimalMax(message = "offset不能超过100", value = "100")
    @DecimalMin(value = "1",message = "offset必须为正整数")
    private String offset;

    private Date startingDate;

    private int startingCursor;

    private boolean includingStart;

    private int offsetNumber = 50;

    private final DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

    public CommonRequestEntity(){
        this.dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));
    }

    public CommonRequestEntity(String mode, String startingPoint, String includingStartingPoint, String offset) {
        this.mode = mode;
        this.startingPoint = startingPoint;
        this.includingStartingPoint = includingStartingPoint;
        this.offset = offset;
        this.dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));
    }

    public ApiResult checkAndInit(){
//        if (StringUtils.isEmpty(mode)){
//            return ApiResult.emptyError("Mode is empty!");
//        }else if(StringUtils.isEmpty(startingPoint)){
//            return ApiResult.emptyError("Starting point is empty!");
//        }else if(!StringUtils.isEmpty(offset) && !StringUtils.isNumeric(offset)){
//            return ApiResult.formatError("Offset is not correct!");
//        }
        if (!StringUtils.isEmpty(offset)){
            offsetNumber = Integer.parseInt(offset);
            if(offsetNumber > 100){
                offsetNumber = 100;
            }
        }
        if (mode.equalsIgnoreCase(ApiConstants.CURSOR_MODE)){
            includingStart = false;
            if(!StringUtils.isEmpty(includingStartingPoint) && includingStartingPoint.equalsIgnoreCase(ApiConstants.TRUE)){
                includingStart = true;
            }
            if(!StringUtils.isNumeric(startingPoint)){
                return ApiResult.formatError("startPoint格式不正确");
            }

            startingCursor = Integer.parseInt(startingPoint);
        }else if(mode.equalsIgnoreCase(ApiConstants.TIME_MODE)){
            includingStart = true;
            if(!StringUtils.isEmpty(includingStartingPoint) && includingStartingPoint.equalsIgnoreCase(ApiConstants.FALSE)){
                includingStart = false;
            }
            try {
                startingDate = dateFormat.parse(startingPoint);
            } catch (ParseException e) {
                return ApiResult.formatError("startPoint格式不正确");
            }
        }

        return ApiResult.success();
    }

    public Map<String, Object> getParams(){
        Map<String, Object> params = Maps.newHashMap();
        if (mode.equalsIgnoreCase(ApiConstants.CURSOR_MODE)){
            if (includingStart){
                params.put("cursorInclude", startingCursor);
            }else{
                params.put("cursor", startingCursor);
            }
        }else if(ApiConstants.TIME_MODE.equalsIgnoreCase(mode)){
            if (includingStart){
                params.put("timeInclude", startingDate);
            }else{
                params.put("time", startingDate);
            }
        }

        params.put("limit", offsetNumber);
        return params;
    }

    public String getSearchValue(String cursorCol, String timeCol){
        StringBuilder sb = new StringBuilder();
        if (mode.equalsIgnoreCase(ApiConstants.CURSOR_MODE)){
            sb.append(cursorCol);
            sb.append(">");
            if (includingStart){
                sb.append("=");
            }
            sb.append(startingCursor);
            sb.append(" order by ");
            sb.append(cursorCol);
            sb.append(" asc");
        }else if(ApiConstants.TIME_MODE.equalsIgnoreCase(mode)){
            sb.append(timeCol);
            sb.append(">");
            if (includingStart){
                sb.append("=");
            }
            sb.append("str_to_date('");
            sb.append(startingPoint);
            sb.append("', '%Y%m%d%H%i%s')");
            sb.append(" order by ");
            sb.append(timeCol);
            sb.append(" asc");
        }
        sb.append(" limit ");
        sb.append(offsetNumber);
        return sb.toString();
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getStartingPoint() {
        return startingPoint;
    }

    public void setStartingPoint(String startingPoint) {
        this.startingPoint = startingPoint;
    }

    public String getIncludingStartingPoint() {
        return includingStartingPoint;
    }

    public void setIncludingStartingPoint(String includingStartingPoint) {
        this.includingStartingPoint = includingStartingPoint;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }
}
