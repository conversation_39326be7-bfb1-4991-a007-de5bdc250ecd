package com.datalink.json.handler;

import com.datalink.common.utils.StringUtils;
import com.datalink.json.JacksonUtil;
import com.fasterxml.jackson.databind.node.ArrayNode;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class JsonArrayNodeTypeHandler extends BaseTypeHandler<ArrayNode> {
    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, ArrayNode jsonNodes, JdbcType jdbcType)
            throws SQLException {
        if (jsonNodes == null) {
            preparedStatement.setString(i, null);
        } else {
            preparedStatement.setString(i, jsonNodes.toString());
        }
    }

    @Override
    public ArrayNode getNullableResult(ResultSet resultSet, String s) throws SQLException {
        String resultStr = resultSet.getString(s);
        if (StringUtils.isEmpty(resultStr)) {
            return null;
        }
        return (ArrayNode) JacksonUtil.readTree(resultStr);
    }

    @Override
    public ArrayNode getNullableResult(ResultSet resultSet, int i) throws SQLException {
        String resultStr = resultSet.getString(i);
        if (StringUtils.isEmpty(resultStr)) {
            return null;
        }
        return (ArrayNode) JacksonUtil.readTree(resultStr);
    }

    @Override
    public ArrayNode getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        String resultStr = callableStatement.getString(i);
        if (StringUtils.isEmpty(resultStr)) {
            return null;
        }
        return (ArrayNode) JacksonUtil.readTree(resultStr);
    }
}
