<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PartLabelReport" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="812" leftMargin="10" rightMargin="20" topMargin="5" bottomMargin="1" uuid="1a0aa80b-dc23-4b7d-85a7-2d94b9b90b62">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<field name="articleNo" class="java.lang.String"/>
	<field name="capacity" class="java.math.BigDecimal"/>
	<field name="capacityBarcode" class="java.lang.String"/>
	<field name="unit" class="java.lang.String"/>
	<field name="articleName" class="java.lang.String"/>
	<field name="unloadingName" class="java.lang.String"/>
	<field name="quantity" class="java.math.BigDecimal"/>
	<field name="suppName" class="java.lang.String"/>
	<field name="suppCode" class="java.lang.String"/>
	<field name="orderCode" class="java.lang.String"/>
	<field name="packNo" class="java.lang.Integer"/>
	<field name="packNoStr" class="java.lang.String"/>
	<field name="packCount" class="java.lang.Integer"/>
	<field name="qrCodeData" class="java.lang.String"/>
	<field name="deliveryDate" class="java.lang.String"/>
	<field name="plantCode" class="java.lang.String"/>
	<field name="unloadingNo" class="java.lang.String"/>
	<field name="stockLoc" class="java.lang.String"/>
	<field name="locAdd" class="java.lang.String"/>
	<field name="remark" class="java.lang.String"/>
	<field name="issueNo" class="java.lang.String"/>
	<field name="compName" class="java.lang.String"/>
	<field name="rcvName" class="java.lang.String"/>
	<field name="productType" class="java.lang.String"/>
	<field name="receivingLocation" class="java.lang.String"/>
	<field name="customerArticleNo" class="java.lang.String"/>
	<field name="customerPoNo" class="java.lang.String"/>
	<field name="rankNo" class="java.lang.String"/>
	<field name="createTime" class="java.lang.String"/>
	<field name="customerCode" class="java.lang.String"/>
	<field name="soOrderQuantity" class="java.math.BigDecimal"/>
	<field name="customerDeliveryDate" class="java.lang.String"/>
	<field name="shipmentPlant02" class="java.lang.String"/>
	<field name="qtyPerPack" class="java.math.BigDecimal"/>
	<field name="supplierCode04" class="java.lang.String"/>
	<field name="orderType" class="java.lang.String"/>
	<field name="qrCodeData2" class="java.lang.String"/>
	<field name="productClass" class="java.lang.String"/>
	<detail>
		<band height="193" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="610" y="22" width="160" height="21" uuid="07f0c4c0-0b7f-4a0c-9b3f-a8c9e7b12d76">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{articleNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="148" width="116" height="21" uuid="77f0c4c0-0b7f-4a0c-9b3f-a8c9e7b12d83">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderCode}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="235" y="109" width="65" height="65" uuid="97f0c4c0-0b7f-4a0c-9b3f-a8c9e7b12d85">
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:QRCode xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<jr:codeExpression><![CDATA[$F{qrCodeData}]]></jr:codeExpression>
				</jr:QRCode>
			</componentElement>
			<componentElement>
				<reportElement x="560" y="43" width="212" height="21" uuid="268b2715-28f3-4efd-a90f-bd5342f1b3bf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{articleNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="401" y="22" width="140" height="11" uuid="d8ce158f-1f77-48eb-9f96-e0649fc7b46a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[得意先部品番号]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="64" width="24" height="15" uuid="8f6a8e3a-2979-4e08-901f-5107f28c4659">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[工場]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="64" width="73" height="15" uuid="db984b9f-81dd-4685-8779-e62ece4e4f4e">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{plantCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="79" width="73" height="27" uuid="37c5d5cf-b75c-4506-8608-0fb65ecd6aee">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="401" y="79" width="24" height="27" uuid="fa58ec46-8854-4ca8-bb07-f00f89434ba3">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入
場所]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="106" width="24" height="21" uuid="dc8dfc87-ae54-442f-a8dd-efb58c3987e3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[From保管]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="127" width="24" height="21" uuid="f7cdfe8e-743c-4f49-bcb6-58030df2bcc3">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[To
保管]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="106" width="73" height="21" uuid="1100ad29-fc6b-42b0-b627-5593b8afdeb2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{stockLoc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="127" width="73" height="21" uuid="7a7fa8af-553b-4bde-a6b0-ed84d43c1f0c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="401" y="148" width="39" height="21" uuid="9d0af2e2-52dc-4f1f-a10a-2e6de52b5baf">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Location]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="440" y="148" width="120" height="21" uuid="9bab92b3-788a-48a6-bed7-86d174725244">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{locAdd}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="498" y="64" width="62" height="21" uuid="2a67ca9b-18c5-4a5d-a8a5-e43cb231251c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[特印]]></text>
			</staticText>
			<staticText>
				<reportElement x="498" y="106" width="12" height="21" uuid="e9350096-375f-4fcf-ae86-56b8fdc7dace">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[在
担]]></text>
			</staticText>
			<staticText>
				<reportElement x="498" y="127" width="12" height="21" uuid="726ef0ab-4c2b-475b-acd6-4c436944509c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[方
式]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="510" y="106" width="50" height="21" uuid="099610b5-8342-477c-8791-a438fe68913c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rcvName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="510" y="127" width="50" height="21" uuid="da6f2538-2df7-485e-8612-5e0ff0714f35">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{productType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="64" width="45" height="21" uuid="f5e0c7ca-7e81-44a9-a280-29983676aefd">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入先]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="64" width="76" height="21" uuid="a8305f84-271a-4a2b-9545-511222e24e42">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="85" width="45" height="21" uuid="8f9d082b-9516-46c7-a040-ce066747b47e">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入指示日]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="85" width="75" height="21" uuid="2b50e24c-b225-465f-aae0-25360a62ac53">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deliveryDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="127" width="45" height="10" uuid="1c840ae0-add2-4d61-bd9c-b4f0b12605e3">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[収容数]]></text>
			</staticText>
			<staticText>
				<reportElement x="560" y="106" width="45" height="10" uuid="849d776c-84b8-49aa-92cc-3fccbb31f064">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[指示数]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="106" width="100" height="21" uuid="b29071a4-74a7-4429-a5ea-a33859dfdff6">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="127" width="100" height="21" uuid="0b969049-23cb-4d4c-8956-d285c76c9998">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{capacity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="705" y="64" width="73" height="21" uuid="6f061d22-0b84-4ec6-aa9d-fc631ee484a2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{plantCode}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="681" y="64" width="24" height="21" uuid="3dd3a165-fc3c-4d2b-9295-c8d9f41e9134">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[工場]]></text>
			</staticText>
			<staticText>
				<reportElement x="681" y="85" width="24" height="21" uuid="c80b10fc-fdcf-41c6-92f2-45d2ee0fa80e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[時刻]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="705" y="85" width="73" height="21" uuid="ad27f5ca-e005-4066-84d5-2f5170f15761">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="705" y="106" width="73" height="21" uuid="3d7ca6c2-fd1c-4e90-85fa-93922109ee01">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入場所]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="705" y="127" width="73" height="21" uuid="8791b799-d292-4015-8506-f644bf810cbb">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="148" width="45" height="21" uuid="9c8711a3-ef6f-4b19-a5f8-2836d8a10124">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[発行番号]]></text>
			</staticText>
			<componentElement>
				<reportElement x="605" y="169" width="116" height="21" uuid="323da039-c684-470c-949b-d3bec3a86a09">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{orderCode} + $F{packNoStr}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="721" y="148" width="56" height="21" uuid="d9b307b0-a76f-4f47-8736-05fffa75bde4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{packNo} + "/" + $F{packCount}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="401" y="44" width="140" height="20" uuid="11aa3ce8-3b7c-4a20-9b81-53bc7d2121f0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{customerArticleNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="560" y="22" width="50" height="21" uuid="c4af23cc-f9d1-4ce2-b919-7c9257d55dc6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[部品番号]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="169" width="23" height="21" uuid="506444c3-f2a5-41c3-abfd-a1c128a568f7">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[備考]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="424" y="169" width="136" height="21" uuid="fffaead6-d4c1-4af6-998c-08cf0faa2bf5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="560" y="137" width="45" height="11" uuid="47f77fe9-fe96-41ed-aa87-e1f49eb46e1c">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="560" y="116" width="45" height="11" uuid="2a4740bb-df5b-4dfd-ac78-043d47169348">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="301" y="106" width="100" height="11" uuid="4b652935-7a5b-4b29-9404-360bc8dddd24">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入指示日時]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="301" y="117" width="100" height="21" uuid="2c13ac1a-6902-4836-b0d1-87fb47e04011">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deliveryDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="301" y="138" width="100" height="11" uuid="8e238d6c-8bb7-4af0-a698-c746fa9e01d9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[時刻]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="301" y="149" width="100" height="21" uuid="061e7db3-a302-4fde-a4b1-a87d744cb48a">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="301" y="79" width="39" height="11" uuid="ddceccbf-4249-49d0-ac80-7a83ca859cf0">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[指示数]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="301" y="90" width="100" height="16" uuid="45ebf338-45f4-4928-900a-bef740e42b84">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="340" y="79" width="29" height="11" uuid="06767c9b-064f-4e01-8469-54d9a3b9c0ff">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[単位]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="369" y="79" width="32" height="11" uuid="2d5a9471-8d5f-468e-9aa6-512f1e41bade">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="186" y="79" width="115" height="11" uuid="0e5817d9-667d-4a39-96c4-cc86703bd7ab">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box leftPadding="5">
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入先]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="186" y="90" width="115" height="16" uuid="a1d4335e-bf1f-4025-8425-8510fbc75c36">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="186" y="64" width="45" height="15" uuid="e8b30938-a3d2-499f-878e-50b10898f5fe">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box leftPadding="5">
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[部品名称]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="231" y="64" width="170" height="15" uuid="eb18543f-1d02-4120-a85d-710b8999fb0e">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{articleName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="148" width="45" height="21" uuid="b1c60162-d0a9-4083-8de2-cd53892e62f2">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[発行番号]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="46" y="148" width="130" height="21" uuid="e95e9e16-983e-4122-a1b9-1ae4833280fb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderCode}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="22" y="169" width="130" height="21" uuid="f56c2a0b-8826-4f75-9703-fc118d3db826">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{orderCode} + $F{packNoStr}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="176" y="148" width="56" height="41" uuid="a0c0e25c-cf57-4b84-b9a0-b64ae9665b5c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{packNo} + "/" + $F{packCount}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="64" width="99" height="21" uuid="a4071cc6-0d41-4cc6-97d1-901fdebfbce2">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[収容数]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="100" y="64" width="50" height="21" uuid="27d7f8cd-7222-409c-90f5-110f131ad3d1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{capacity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="22" y="85" width="130" height="21" uuid="b434725c-bdce-46a9-8846-88f8796a8666">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{capacityBarcode}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="150" y="64" width="35" height="10" uuid="0072c799-ca7d-4abd-b59a-767d8eabdd42">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[単位]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="150" y="74" width="35" height="11" uuid="609ae6ae-5166-4511-aeb3-22d3fb0eb634">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="106" width="30" height="21" uuid="43edc470-2e50-452a-a682-8fa8d037afb1">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[受注者]]></text>
			</staticText>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="31" y="106" width="201" height="21" uuid="088ac7f2-2d8b-4d61-a79c-bf3f60976afb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{suppName}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="22" y="127" width="130" height="21" uuid="5c60895b-e134-46a1-9ac0-4b75f24943cb">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{suppCode}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="152" y="127" width="80" height="21" uuid="5fbf1027-e037-40e5-a6f7-66f2bf726f90">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{suppCode}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="1" y="22" width="19" height="21" uuid="4bd8de9a-c3ae-49dd-849d-030322de3b82">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[部品番号]]></text>
			</staticText>
			<componentElement>
				<reportElement x="22" y="43" width="248" height="21" uuid="63faff37-d18d-4d80-9168-80df8598c938">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{articleNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="22" y="22" width="208" height="21" uuid="cef6ca7e-f9ab-4247-ad98-bc803c7d7edc">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="14" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{articleNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="270" y="43" width="30" height="10" uuid="69f795db-627f-4ad5-bf02-e6a9f21aa295">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[製区
]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="270" y="53" width="30" height="11" uuid="d1e8658a-16a5-43cf-96e0-478e7d201763">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{productClass}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="300" y="43" width="19" height="21" uuid="48c0b8b8-5fea-4473-9fb0-3d0b74012111">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[部品識別]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="319" y="43" width="82" height="21" uuid="fd02d6cf-4d0e-4247-a8b8-a53977bfabf5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{issueNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="280" y="22" width="121" height="21" uuid="598ebac7-19e8-4d31-880c-5e457e4207c1">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{compName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="410" y="33" width="131" height="11" uuid="c0065d9e-94a8-48e7-9380-e0124e74115c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[!"ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerArticleNo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="610" y="22" width="160" height="21" uuid="a37f9dd7-2fa4-49ce-aa4b-63d6b1de430d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[!"".equals($F{productClass}) ? $F{articleNo}.substring(0, $F{articleNo}.indexOf("+")) : $F{articleNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="148" width="116" height="21" uuid="8b17ded2-b82f-44d0-ab9b-95e54d001767">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderCode}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="354" y="65" width="46" height="46" uuid="aec4cbaa-1f4a-47ce-b8a7-d0697a3abc00">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:QRCode xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<jr:codeExpression><![CDATA[$F{qrCodeData2}]]></jr:codeExpression>
				</jr:QRCode>
			</componentElement>
			<componentElement>
				<reportElement x="560" y="43" width="212" height="21" uuid="e7ae3687-c164-43af-9d58-3dec7176b247">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[!"".equals($F{productClass}) ? $F{articleNo}.substring(0, $F{articleNo}.indexOf("+")) : $F{articleNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="401" y="64" width="24" height="15" uuid="619389bb-34bc-4858-94eb-819a663a6c39">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[工場]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="64" width="73" height="15" uuid="981595fa-8744-49a3-adfe-bdbd7d6d28d2">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{plantCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="79" width="73" height="27" uuid="2b555b39-3aac-4ad4-b6f3-72df8d8ab37f">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="401" y="79" width="24" height="27" uuid="7639a83c-5ecd-4a7f-aaca-3dff1122acf5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入
場所]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="106" width="24" height="21" uuid="be3185b2-dd15-4381-b588-bf2895147d04">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[From保管]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="127" width="24" height="21" uuid="af9730ba-5eb5-492b-955b-d004abb7e331">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[To
保管]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="106" width="73" height="21" uuid="83657455-41d4-46c5-a52a-701040db8c2f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{stockLoc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="127" width="73" height="21" uuid="461a464f-34a4-497f-8103-a01aa5d7f8d1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="401" y="148" width="24" height="21" uuid="ef4863fa-bb1a-4dad-b69a-d40fb3784cde">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[指示
日時]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="425" y="148" width="135" height="21" uuid="f309773a-f356-46d3-a856-785c074a3c49">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deliveryDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="498" y="64" width="62" height="21" uuid="ae7e2b44-0efd-4de7-ba37-e8da9e185d85">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[特印]]></text>
			</staticText>
			<staticText>
				<reportElement x="498" y="106" width="12" height="21" uuid="04fd9f43-5be4-4633-9fdb-58086c7d8c57">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[在
担]]></text>
			</staticText>
			<staticText>
				<reportElement x="498" y="127" width="12" height="21" uuid="f0b2aea6-3a73-420d-86cd-e14f95cbb5c9">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[方
式]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="510" y="106" width="50" height="21" uuid="fff75dcd-3251-4333-9c74-49fab35b3890">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rcvName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="510" y="127" width="50" height="21" uuid="c9e008eb-63a2-4ac8-b0b1-7628340411b7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{productType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="64" width="45" height="21" uuid="7a049fb3-c246-4d8f-b946-979f83baee5e">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入先]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="64" width="76" height="21" uuid="0d1ec969-25d6-450e-8eff-a360bc8ac405">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="85" width="45" height="21" uuid="abb44826-164f-408d-8adf-180a6489a40a">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入指示日]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="85" width="76" height="21" uuid="20a56e5e-1a1e-428a-b7f9-bca879303c75">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deliveryDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="127" width="45" height="10" uuid="3fb50981-bf9e-4f03-b390-956220ab3c74">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[収容数]]></text>
			</staticText>
			<staticText>
				<reportElement x="560" y="106" width="45" height="10" uuid="b0cda4f7-0f16-4b0c-8fda-f275a9ba5735">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[指示数]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="106" width="100" height="21" uuid="96a98366-570c-4a26-ac47-deca30a5a351">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="605" y="127" width="100" height="21" uuid="0dffbb96-3588-497e-8292-85be60ec0fbd">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{capacity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="705" y="64" width="73" height="21" uuid="7aa2a294-54f6-4e10-a7ab-f4a421f1e3cf">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{plantCode}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="681" y="64" width="24" height="21" uuid="2e92ec64-85d7-4532-8783-15a41785724b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" markup="none">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[工場]]></text>
			</staticText>
			<staticText>
				<reportElement x="681" y="85" width="24" height="21" uuid="1e78e804-d57e-4b21-ad50-5aef03731f6c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<leftPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[時刻]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="705" y="85" width="73" height="21" uuid="4575ba20-a0e7-4186-8187-a75c1083e128">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="705" y="106" width="73" height="21" uuid="6bafdfc8-d2bc-445a-a44d-6d42c798c3b1">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[納入場所]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="705" y="127" width="73" height="21" uuid="ddb68811-7849-4ca2-86b0-099d335536e5">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unloadingNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="148" width="45" height="21" uuid="e0916ab2-185b-4df4-a24a-889d3fd138dd">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[発行番号]]></text>
			</staticText>
			<componentElement>
				<reportElement x="605" y="169" width="116" height="21" uuid="cef567b2-40dd-4df7-b152-430909ee93c4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" moduleWidth="1.0" textPosition="none">
					<jr:codeExpression><![CDATA[$F{orderCode} + $F{packNoStr}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="721" y="148" width="56" height="21" uuid="1ec15563-e995-4b43-a33c-e14151890a5b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{packNo} + "/" + $F{packCount}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="560" y="22" width="50" height="21" uuid="13193c25-34c9-47eb-a22b-6ffa69ceabd7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[部品番号]]></text>
			</staticText>
			<staticText>
				<reportElement x="401" y="169" width="24" height="21" uuid="35405c52-870b-4b2d-90d6-4daf73a5c2b5">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField>
				<reportElement x="560" y="137" width="45" height="11" uuid="95257eba-ee2f-4cee-9c73-5e008eba8869">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="560" y="116" width="45" height="11" uuid="d8c66e7f-6e71-4b4a-90a9-ba85f7b96e49">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="268" y="38" width="17" height="13" uuid="017d7f34-21bc-4847-af6a-217fbeccb36e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[工場]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="291" y="51" width="45" height="13" uuid="710fb49f-5957-4fb8-b674-90e0604df703">
					<property name="com.jaspersoft.studio.unit.width" value="mm"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{soOrderQuantity}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="311" y="38" width="25" height="13" uuid="89cf8094-4df4-48b5-8837-958e069dcbe9">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[出荷日
]]></text>
			</staticText>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="336" y="38" width="65" height="13" uuid="662cd91a-99ff-4cb0-823d-5d7c8c6bf66b">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerDeliveryDate}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="309" y="22" width="92" height="16" uuid="77e34bb9-e166-4fdc-93a8-98e5c1cd4136">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{compName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="55" y="43" width="213" height="21" uuid="1d1894b8-6893-4f49-be3f-cb87a725f636">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerArticleNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="40" y="132" width="236" height="15" uuid="ffef03ca-8722-46e6-b8dc-9c21ad4100a1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{articleNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="145" y="158" width="50" height="11" uuid="4135e7e2-038f-41e4-a6b5-8ef0f37dc933">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{createTime}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="20" y="169" width="125" height="17" uuid="e70d00d9-29b2-4701-8a53-1c36c832851f">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderCode}.substring($F{orderCode}.length() - 8) + "-" + $F{packNoStr}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="1" y="22" width="267" height="21" uuid="efc62d46-a1e9-43ec-bc58-8ec64aba5341">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["P" + $F{customerArticleNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="1" y="43" width="54" height="21" uuid="854c6a79-9790-44d3-87d5-2ad0388f3b56">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[Customer
Part No.(P)]]></text>
			</staticText>
			<componentElement>
				<reportElement x="1" y="112" width="296" height="20" uuid="cdd41c98-8751-47a3-a69f-3f2a8a9fe2b0">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="mm"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["1P" + $F{articleNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="1" y="132" width="39" height="15" uuid="32580f65-70f4-4145-a976-13176e4b53c1">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="5"/>
				</textElement>
				<text><![CDATA[Supplier 
PART No.(1P) ]]></text>
			</staticText>
			<componentElement>
				<reportElement x="1" y="147" width="144" height="22" uuid="da3d794e-b555-4ddf-9691-e56c5c074dd9">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["S" + $F{orderCode}.substring($F{orderCode}.length() - 8) + $F{packNoStr}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="1" y="169" width="19" height="17" uuid="269b990e-4b81-455b-9aa3-5b5618128578">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[Serial 
No.(S) ]]></text>
			</staticText>
			<staticText>
				<reportElement x="145" y="147" width="50" height="11" uuid="ab10bc2a-7487-4c33-a992-b7c5eaecb45e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Issue Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="128" y="64" width="28" height="18" uuid="0322ea0e-a48f-4884-8194-099febc00c30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[RAN
(A)]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="156" y="64" width="100" height="18" uuid="a501ca20-ad79-4538-8682-d2130e5cbd1d">
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rankNo}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="128" y="82" width="128" height="30" uuid="1033218e-ba37-4c23-8acd-b16fe746f786">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["A" + $F{rankNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<componentElement>
				<reportElement x="256" y="64" width="97" height="30" uuid="46e66bcf-a8c8-478d-af66-a79c26e31eaa">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["Q" + $F{qtyPerPack}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="256" y="94" width="39" height="18" uuid="f2115dce-1717-4866-a1a7-fbbf7ccadf85">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<bottomPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[Quantity
(Q)]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="295" y="94" width="58" height="18" uuid="c53b8b98-8f91-44f8-991b-cbea06ac0846">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{qtyPerPack}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="277" y="112" width="38" height="15" uuid="acc964d7-f60f-44a9-b8e8-1ecfc5bc88fe">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box leftPadding="3">
					<topPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="5"/>
				</textElement>
				<text><![CDATA[Supplier
Code(V)]]></text>
			</staticText>
			<componentElement>
				<reportElement x="277" y="127" width="124" height="20" uuid="a08e3aa2-4706-4eb2-93a5-8f5fe984bec5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="mm"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["V" + $F{supplierCode04}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="315" y="112" width="86" height="15" uuid="1ca6e501-1c9e-4170-bf06-4db175ec148a">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supplierCode04}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="195" y="147" width="33" height="17" uuid="1cb95963-bbb1-474a-8d27-bfc18f49fc4e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[ P/O No. 
+RAN(K)]]></text>
			</staticText>
			<componentElement>
				<reportElement x="195" y="164" width="206" height="22" uuid="1dbc9ce1-744b-4c05-8f92-c9e0f8fc0694">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["K" + ($F{customerPoNo} == null ? "" : $F{customerPoNo}) + "-" + ($F{rankNo} == null ? "" : $F{rankNo})]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<textField isBlankWhenNull="true">
				<reportElement x="228" y="147" width="173" height="17" uuid="c0e64d0b-6ec4-4cef-b552-a12d7cec98a9">
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{customerPoNo} == null ? "" : $F{customerPoNo}) + "-" + ($F{rankNo} == null ? "" : $F{rankNo})]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="401" y="22" width="39" height="21" uuid="fb0af0c6-c19d-4259-8ca9-547944d834f6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box leftPadding="5">
					<topPen lineWidth="0.0"/>
					<leftPen lineWidth="0.5"/>
					<bottomPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[受注者]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="440" y="22" width="73" height="21" uuid="049b8dd6-70be-4ccc-8ead-66ccee768941">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{suppCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="401" y="43" width="159" height="21" uuid="d3d0b814-33b7-4cd4-9dbd-17f084cd3307">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{suppName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="336" y="51" width="65" height="13" uuid="d3eddedf-a89f-4c53-8749-5a542477c84f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0"/>
					<bottomPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{locAdd}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="268" y="51" width="23" height="13" uuid="d75b0fe8-87d7-41df-9a42-6395c4993dc6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement markup="none">
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<text><![CDATA[要求数]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="285" y="38" width="26" height="13" uuid="24f6bd67-460d-43e6-8a84-dab2d770fe3e">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipmentPlant02}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="268" y="22" width="41" height="16" uuid="aca48263-d083-49a2-979f-0c2076f1356c">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Bottom">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerCode}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="251" y="64" width="5" height="48" uuid="6c961d96-f26a-4864-bcf7-7e09b5d41a29">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<componentElement>
				<reportElement x="1" y="64" width="127" height="30" uuid="491c5ff6-dc6d-41ac-972a-c08359510574">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="none">
					<jr:codeExpression><![CDATA["K" + $F{customerPoNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<staticText>
				<reportElement x="1" y="94" width="39" height="18" uuid="16a9f3cd-5bb9-418b-a428-af761d96fc42">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[P/O No. 
(K)]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="40" y="94" width="88" height="18" uuid="b5baec50-5fad-420c-8ea0-0f6a0fdf1bac">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{customerPoNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="123" y="64" width="5" height="48" uuid="4eb33551-afc1-4fd1-8d18-aeae6cc02d07">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="6"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="145" y="169" width="50" height="17" uuid="24578894-861f-485b-bee1-8e917f44799b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<rightPen lineWidth="0.5"/>
				</box>
				<textElement>
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{packNo} + "/" + $F{packCount}]]></textFieldExpression>
			</textField>
			<frame>
				<reportElement x="1" y="112" width="275" height="20" uuid="2b0db9b4-315d-4753-9f2a-bb7b9dde6c70">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA["ZOR".equals($F{orderType})]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.5"/>
					<rightPen lineWidth="0.5"/>
				</box>
			</frame>
		</band>
	</detail>
</jasperReport>
