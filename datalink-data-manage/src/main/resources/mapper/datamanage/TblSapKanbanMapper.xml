<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblSapKanbanMapper">
    
    <resultMap type="TblSapKanbanKd" id="TblSapKanbanKdResult">
        <result property="fileNo"    column="FILE_NO"    />
        <result property="reserve1"    column="RESERVE1"    />
        <result property="demandCode"    column="DEMAND_CODE"    />
        <result property="issueNo"    column="ISSUE_NO"    />
        <result property="deliveryInstructionDate"    column="DELIVERY_INSTRUCTION_DATE"    />
        <result property="deliveryInstructionTime"    column="DELIVERY_INSTRUCTION_TIME"    />
        <result property="maker"    column="MAKER"    />
        <result property="depot"    column="DEPOT"    />
        <result property="classification"    column="CLASSIFICATION"    />
        <result property="deliveryLocation"    column="DELIVERY_LOCATION"    />
        <result property="unloadingUnit"    column="UNLOADING_UNIT"    />
        <result property="customerPartsNo"    column="CUSTOMER_PARTS_NO"    />
        <result property="currentProcessClass"    column="CURRENT_PROCESS_CLASS"    />
        <result property="partsName"    column="PARTS_NAME"    />
        <result property="deliveryInstructionNumber"    column="DELIVERY_INSTRUCTION_NUMBER"    />
        <result property="supplyLocation"    column="SUPPLY_LOCATION"    />
        <result property="snep"    column="SNEP"    />
        <result property="safetyFlag"    column="SAFETY_FLAG"    />
        <result property="labelCount"    column="LABEL_COUNT"    />
        <result property="managementPic"    column="MANAGEMENT_PIC"    />
        <result property="issueReason"    column="ISSUE_REASON"    />
        <result property="vipFlag"    column="VIP_FLAG"    />
        <result property="packageCode"    column="PACKAGE_CODE"    />
        <result property="internalExternalClass"    column="INTERNAL_EXTERNAL_CLASS"    />
        <result property="deliveryInstructionCount"    column="DELIVERY_INSTRUCTION_COUNT"    />
        <result property="deliveryMethod"    column="DELIVERY_METHOD"    />
        <result property="orderClass"    column="ORDER_CLASS"    />
        <result property="labelId"    column="LABEL_ID"    />
        <result property="delayPreventionMakerSymbol"    column="DELAY_PREVENTION_MAKER_SYMBOL"    />
        <result property="shiptoClass"    column="SHIPTO_CLASS"    />
        <result property="shiptoCode"    column="SHIPTO_CODE"    />
        <result property="labelOutput"    column="LABEL_OUTPUT"    />
        <result property="reserve2"    column="RESERVE2"    />
        <result property="labelNoChangeMark"    column="LABEL_NO_CHANGE_MARK"    />
        <result property="selfProcurementLine"    column="SELF_PROCUREMENT_LINE"    />
        <result property="labelSpecification"    column="LABEL_SPECIFICATION"    />
        <result property="selfProcurementKd"    column="SELF_PROCUREMENT_KD"    />
        <result property="reserve3"    column="RESERVE3"    />
        <result property="kdElements"    column="KD_ELEMENTS"    />
        <result property="transitCode"    column="TRANSIT_CODE"    />
        <result property="transitDeliveryDate"    column="TRANSIT_DELIVERY_DATE"    />
        <result property="partsId"    column="PARTS_ID"    />
        <result property="deliveryTicketNo"    column="DELIVERY_TICKET_NO"    />
        <result property="withdrawalClass"    column="WITHDRAWAL_CLASS"    />
        <result property="partPrice"    column="PART_PRICE"    />
        <result property="prototypeSymbol"    column="PROTOTYPE_SYMBOL"    />
        <result property="resendSymbol"    column="RESEND_SYMBOL"    />
        <result property="orderDate"    column="ORDER_DATE"    />
        <result property="labelSize"    column="LABEL_SIZE"    />
        <result property="reserve4"    column="RESERVE4"    />
        <result property="reserve5"    column="RESERVE5"    />
        <result property="makerPic"    column="MAKER_PIC"    />
        <result property="makerSnp"    column="MAKER_SNP"    />
        <result property="resendDepot"    column="RESEND_DEPOT"    />
        <result property="reserve6"    column="RESERVE6"    />
        <result property="reserve7"    column="RESERVE7"    />
        <result property="labelTerminalNo"    column="LABEL_TERMINAL_NO"    />
        <result property="listTerminalNo"    column="LIST_TERMINAL_NO"    />
        <result property="partsNo"    column="PARTS_NO"    />
        <result property="productClass"    column="PRODUCT_CLASS"    />
        <result property="productionMethod"    column="PRODUCTION_METHOD"    />
        <result property="deliveryMethod2"    column="DELIVERY_METHOD2"    />
        <result property="fromProcess"    column="FROM_PROCESS"    />
        <result property="fromWorkArea"    column="FROM_WORK_AREA"    />
        <result property="shippingPic"    column="SHIPPING_PIC"    />
        <result property="partsNoIdCode"    column="PARTS_NO_ID_CODE"    />
        <result property="instructionDate"    column="INSTRUCTION_DATE"    />
        <result property="instructionTime"    column="INSTRUCTION_TIME"    />
        <result property="instructionNo"    column="INSTRUCTION_NO"    />
        <result property="productionHandlingMethod"    column="PRODUCTION_HANDLING_METHOD"    />
        <result property="plantCode"    column="PLANT_CODE"    />
        <result property="shippingLocation"    column="SHIPPING_LOCATION"    />
        <result property="shippingPort"    column="SHIPPING_PORT"    />
        <result property="labelIssueAssignment"    column="LABEL_ISSUE_ASSIGNMENT"    />
        <result property="divisionDepot"    column="DIVISION_DEPOT"    />
        <result property="customerCode"    column="CUSTOMER_CODE"    />
        <result property="demandCode2"    column="DEMAND_CODE2"    />
        <result property="shippingLocationCode2"    column="SHIPPING_LOCATION_CODE2"    />
        <result property="movementLeadTime"    column="MOVEMENT_LEAD_TIME"    />
        <result property="rehandlingFlag"    column="REHANDLING_FLAG"    />
        <result property="noMasterFlag"    column="NO_MASTER_FLAG"    />
        <result property="divisionDepot2"    column="DIVISION_DEPOT2"    />
    </resultMap>

    <resultMap type="TblSapKanbanSv" id="TblSapKanbanSvResult">
        <result property="id"    column="id"    />
        <result property="fileNo"    column="FILE_NO"    />
        <result property="reserve1"    column="RESERVE1"    />
        <result property="demandCode"    column="DEMAND_CODE"    />
        <result property="issueNoId"    column="ISSUE_NO_ID"    />
        <result property="deliveryInstructionYy"    column="DELIVERY_INSTRUCTION_YY"    />
        <result property="maker"    column="MAKER"    />
        <result property="depot"    column="DEPOT"    />
        <result property="classification"    column="CLASSIFICATION"    />
        <result property="deliveryLocation"    column="DELIVERY_LOCATION"    />
        <result property="customerPartsNo"    column="CUSTOMER_PARTS_NO"    />
        <result property="currentProcessClass"    column="CURRENT_PROCESS_CLASS"    />
        <result property="partsName"    column="PARTS_NAME"    />
        <result property="deliveryInstructionNumber"    column="DELIVERY_INSTRUCTION_NUMBER"    />
        <result property="supplyLocation"    column="SUPPLY_LOCATION"    />
        <result property="snep"    column="SNEP"    />
        <result property="safetyFlag"    column="SAFETY_FLAG"    />
        <result property="labelCount"    column="LABEL_COUNT"    />
        <result property="managementPic"    column="MANAGEMENT_PIC"    />
        <result property="issueReason"    column="ISSUE_REASON"    />
        <result property="vipFlag"    column="VIP_FLAG"    />
        <result property="packageCode"    column="PACKAGE_CODE"    />
        <result property="internalExternalClass"    column="INTERNAL_EXTERNAL_CLASS"    />
        <result property="deliveryInstructionCount"    column="DELIVERY_INSTRUCTION_COUNT"    />
        <result property="deliveryMethod"    column="DELIVERY_METHOD"    />
        <result property="orderClass"    column="ORDER_CLASS"    />
        <result property="reserve2"    column="RESERVE2"    />
        <result property="delayPreventionMakerSymbol"    column="DELAY_PREVENTION_MAKER_SYMBOL"    />
        <result property="reserve3"    column="RESERVE3"    />
        <result property="reserve4"    column="RESERVE4"    />
        <result property="reserve5"    column="RESERVE5"    />
        <result property="labelSize"    column="LABEL_SIZE"    />
        <result property="labelSpecification"    column="LABEL_SPECIFICATION"    />
        <result property="orderType"    column="ORDER_TYPE"    />
        <result property="checkSpecification"    column="CHECK_SPECIFICATION"    />
        <result property="location"    column="LOCATION"    />
        <result property="packClass"    column="PACK_CLASS"    />
        <result property="tradeSpecification"    column="TRADE_SPECIFICATION"    />
        <result property="outpackMaterialCode"    column="OUTPACK_MATERIAL_CODE"    />
        <result property="outpackMaterialProcClass"    column="OUTPACK_MATERIAL_PROC_CLASS"    />
        <result property="bindPackCode"    column="BIND_PACK_CODE"    />
        <result property="bindUnit"    column="BIND_UNIT"    />
        <result property="packUnit"    column="PACK_UNIT"    />
        <result property="consistPackClass"    column="CONSIST_PACK_CLASS"    />
        <result property="elementsFinishClass"    column="ELEMENTS_FINISH_CLASS"    />
        <result property="inpackCode1"    column="INPACK_CODE1"    />
        <result property="inpackProcClass1"    column="INPACK_PROC_CLASS1"    />
        <result property="inpackNecessaryNumber1"    column="INPACK_NECESSARY_NUMBER1"    />
        <result property="inpackCode2"    column="INPACK_CODE2"    />
        <result property="inpackProcClass2"    column="INPACK_PROC_CLASS2"    />
        <result property="inpackNecessaryNumber2"    column="INPACK_NECESSARY_NUMBER2"    />
        <result property="inpackCode3"    column="INPACK_CODE3"    />
        <result property="inpackProcClass3"    column="INPACK_PROC_CLASS3"    />
        <result property="inpackNecessaryNumber3"    column="INPACK_NECESSARY_NUMBER3"    />
        <result property="inpackCode4"    column="INPACK_CODE4"    />
        <result property="inpackProcClass4"    column="INPACK_PROC_CLASS4"    />
        <result property="inpackNecessaryNumber4"    column="INPACK_NECESSARY_NUMBER4"    />
        <result property="inpackCode5"    column="INPACK_CODE5"    />
        <result property="inpackProcClass5"    column="INPACK_PROC_CLASS5"    />
        <result property="inpackNecessaryNumber5"    column="INPACK_NECESSARY_NUMBER5"    />
        <result property="inpackCode6"    column="INPACK_CODE6"    />
        <result property="inpackProcClass6"    column="INPACK_PROC_CLASS6"    />
        <result property="inpackNecessaryNumber6"    column="INPACK_NECESSARY_NUMBER6"    />
        <result property="inpackCode7"    column="INPACK_CODE7"    />
        <result property="inpackProcClass7"    column="INPACK_PROC_CLASS7"    />
        <result property="inpackNecessaryNumber7"    column="INPACK_NECESSARY_NUMBER7"    />
        <result property="inpackCode8"    column="INPACK_CODE8"    />
        <result property="inpackProcClass8"    column="INPACK_PROC_CLASS8"    />
        <result property="inpackNecessaryNumber8"    column="INPACK_NECESSARY_NUMBER8"    />
        <result property="inpackCode9"    column="INPACK_CODE9"    />
        <result property="inpackProcClass9"    column="INPACK_PROC_CLASS9"    />
        <result property="inpackNecessaryNumber9"    column="INPACK_NECESSARY_NUMBER9"    />
        <result property="moduleMaterial"    column="MODULE_MATERIAL"    />
        <result property="moduleUnit"    column="MODULE_UNIT"    />
        <result property="moduleClass"    column="MODULE_CLASS"    />
        <result property="originalPartNo"    column="ORIGINAL_PART_NO"    />
        <result property="partManageClass"    column="PART_MANAGE_CLASS"    />
        <result property="newcarProgramCode"    column="NEWCAR_PROGRAM_CODE"    />
        <result property="originalIssueNo"    column="ORIGINAL_ISSUE_NO"    />
        <result property="designChangeNo"    column="DESIGN_CHANGE_NO"    />
        <result property="normalContainerCode"    column="NORMAL_CONTAINER_CODE"    />
        <result property="boForecastDate"    column="BO_FORECAST_DATE"    />
        <result property="boSymbol"    column="BO_SYMBOL"    />
        <result property="trusteeId"    column="TRUSTEE_ID"    />
        <result property="deliveryDocType"    column="DELIVERY_DOC_TYPE"    />
        <result property="packageLabelType"    column="PACKAGE_LABEL_TYPE"    />
        <result property="labelIssueOrg"    column="LABEL_ISSUE_ORG"    />
        <result property="deliveryContainer"    column="DELIVERY_CONTAINER"    />
        <result property="ulContainerCount"    column="UL_CONTAINER_COUNT"    />
        <result property="itemSubno1"    column="ITEM_SUBNO1"    />
        <result property="itemSubno2"    column="ITEM_SUBNO2"    />
        <result property="itemSubno3"    column="ITEM_SUBNO3"    />
        <result property="itemSubno4"    column="ITEM_SUBNO4"    />
        <result property="itemSubno5"    column="ITEM_SUBNO5"    />
        <result property="itemSubno6"    column="ITEM_SUBNO6"    />
        <result property="itemSubno7"    column="ITEM_SUBNO7"    />
        <result property="itemSubno8"    column="ITEM_SUBNO8"    />
        <result property="itemSubno9"    column="ITEM_SUBNO9"    />
        <result property="itemSubno10"    column="ITEM_SUBNO10"    />
        <result property="itemSubno11"    column="ITEM_SUBNO11"    />
        <result property="itemSubno12"    column="ITEM_SUBNO12"    />
        <result property="itemSubno13"    column="ITEM_SUBNO13"    />
        <result property="itemSubno14"    column="ITEM_SUBNO14"    />
        <result property="itemSubno15"    column="ITEM_SUBNO15"    />
        <result property="itemSubno16"    column="ITEM_SUBNO16"    />
        <result property="itemSubno17"    column="ITEM_SUBNO17"    />
        <result property="itemSubno18"    column="ITEM_SUBNO18"    />
        <result property="itemSubno19"    column="ITEM_SUBNO19"    />
        <result property="itemSubno20"    column="ITEM_SUBNO20"    />
        <result property="barcodeInfo"    column="BARCODE_INFO"    />
        <result property="freeColumn1"    column="FREE_COLUMN1"    />
        <result property="freeColumn2"    column="FREE_COLUMN2"    />
        <result property="freeColumn3"    column="FREE_COLUMN3"    />
        <result property="freeColumn4"    column="FREE_COLUMN4"    />
        <result property="yard"    column="YARD"    />
        <result property="enlargeMark1"    column="ENLARGE_MARK1"    />
        <result property="enlargeMark2"    column="ENLARGE_MARK2"    />
        <result property="noDiffDeliveryClass"    column="NO_DIFF_DELIVERY_CLASS"    />
        <result property="ulSnp"    column="UL_SNP"    />
        <result property="deliveryTicketNo"    column="DELIVERY_TICKET_NO"    />
        <result property="withdrawalClass"    column="WITHDRAWAL_CLASS"    />
        <result property="partPrice"    column="PART_PRICE"    />
        <result property="prototypeSymbol"    column="PROTOTYPE_SYMBOL"    />
        <result property="resendSymbol"    column="RESEND_SYMBOL"    />
        <result property="orderDate"    column="ORDER_DATE"    />
        <result property="reserve6"    column="RESERVE6"    />
        <result property="reserve7"    column="RESERVE7"    />
        <result property="labelTerminalNo"    column="LABEL_TERMINAL_NO"    />
        <result property="listTerminalNo"    column="LIST_TERMINAL_NO"    />
        <result property="makerPic"    column="MAKER_PIC"    />
        <result property="makerSnp"    column="MAKER_SNP"    />
        <result property="resendDepot"    column="RESEND_DEPOT"    />
        <result property="partsNo"    column="PARTS_NO"    />
        <result property="productClass"    column="PRODUCT_CLASS"    />
        <result property="productionMethod"    column="PRODUCTION_METHOD"    />
        <result property="deliveryMethod2"    column="DELIVERY_METHOD2"    />
        <result property="fromProcess"    column="FROM_PROCESS"    />
        <result property="fromWorkArea"    column="FROM_WORK_AREA"    />
        <result property="shippingPic"    column="SHIPPING_PIC"    />
        <result property="partsNoIdCode"    column="PARTS_NO_ID_CODE"    />
        <result property="instructionDate"    column="INSTRUCTION_DATE"    />
        <result property="instructionTime"    column="INSTRUCTION_TIME"    />
        <result property="instructionNo"    column="INSTRUCTION_NO"    />
        <result property="productionHandlingMethod"    column="PRODUCTION_HANDLING_METHOD"    />
        <result property="plantCode"    column="PLANT_CODE"    />
        <result property="shippingLocation"    column="SHIPPING_LOCATION"    />
        <result property="shippingPort"    column="SHIPPING_PORT"    />
        <result property="reserve8"    column="RESERVE8"    />
        <result property="labelIssueAssignment"    column="LABEL_ISSUE_ASSIGNMENT"    />
        <result property="divisionDepot"    column="DIVISION_DEPOT"    />
        <result property="customerCode"    column="CUSTOMER_CODE"    />
        <result property="demandCode2"    column="DEMAND_CODE2"    />
        <result property="shippingLocationCode2"    column="SHIPPING_LOCATION_CODE2"    />
        <result property="movementLeadTime"    column="MOVEMENT_LEAD_TIME"    />
        <result property="rehandlingFlag"    column="REHANDLING_FLAG"    />
        <result property="noMasterFlag"    column="NO_MASTER_FLAG"    />
        <result property="reserve9"    column="RESERVE9"    />
        <result property="divisionDepot2"    column="DIVISION_DEPOT2"    />
    </resultMap>

    <sql id="selectTblSapKanbanKdVo">
        select id, FILE_NO, RESERVE1, DEMAND_CODE, ISSUE_NO, DELIVERY_INSTRUCTION_DATE, DELIVERY_INSTRUCTION_TIME, 
            MAKER, DEPOT, CLASSIFICATION, DELIVERY_LOCATION, UNLOADING_UNIT, CUSTOMER_PARTS_NO, CURRENT_PROCESS_CLASS,
            PARTS_NAME, DELIVERY_INSTRUCTION_NUMBER, SUPPLY_LOCATION, SNEP, SAFETY_FLAG, LABEL_COUNT, MANAGEMENT_PIC,
            ISSUE_REASON, VIP_FLAG, PACKAGE_CODE, INTERNAL_EXTERNAL_CLASS, DELIVERY_INSTRUCTION_COUNT, DELIVERY_METHOD,
            ORDER_CLASS, LABEL_ID, DELAY_PREVENTION_MAKER_SYMBOL, SHIPTO_CLASS, SHIPTO_CODE, LABEL_OUTPUT, RESERVE2,
            LABEL_NO_CHANGE_MARK, SELF_PROCUREMENT_LINE, LABEL_SPECIFICATION, SELF_PROCUREMENT_KD, RESERVE3, KD_ELEMENTS,
            TRANSIT_CODE, TRANSIT_DELIVERY_DATE, PARTS_ID, DELIVERY_TICKET_NO, WITHDRAWAL_CLASS, PART_PRICE,
            PROTOTYPE_SYMBOL, RESEND_SYMBOL, ORDER_DATE, LABEL_SIZE, RESERVE4, RESERVE5, UPDATE_TIME, LABEL_TERMINAL_NO,
            LIST_TERMINAL_NO, MAKER_PIC, MAKER_SNP, RESEND_DEPOT, PARTS_NO, PRODUCT_CLASS, PRODUCTION_METHOD,
            DELIVERY_METHOD2, FROM_PROCESS, FROM_WORK_AREA, SHIPPING_PIC, PARTS_NO_ID_CODE, INSTRUCTION_DATE,
            INSTRUCTION_TIME, INSTRUCTION_NO, PRODUCTION_HANDLING_METHOD, PLANT_CODE, SHIPPING_LOCATION, SHIPPING_PORT,
            SHIPPING_CHECK_FLAG, RESERVE6, LABEL_ISSUE_ASSIGNMENT, DIVISION_DEPOT, CUSTOMER_CODE, DEMAND_CODE2,
            SHIPPING_LOCATION_CODE2, MOVEMENT_LEAD_TIME, REHANDLING_FLAG, NO_MASTER_FLAG, RESERVE7, DIVISION_DEPOT2
        from tbl_sap_kanban_kd a
    </sql>

    <sql id="selectTblSapKanbanLineVo">
        select id, FILE_NO, RESERVE1, DEMAND_CODE, ISSUE_NO, DELIVERY_INSTRUCTION_DATE, DELIVERY_INSTRUCTION_TIME,
            MAKER, DEPOT, CLASSIFICATION, DELIVERY_LOCATION, UNLOADING_UNIT, CUSTOMER_PARTS_NO, CURRENT_PROCESS_CLASS,
            PARTS_NAME, DELIVERY_INSTRUCTION_NUMBER, SUPPLY_LOCATION, SNEP, SAFETY_FLAG, LABEL_COUNT, MANAGEMENT_PIC,
            ISSUE_REASON, VIP_FLAG, PACKAGE_CODE, INTERNAL_EXTERNAL_CLASS, DELIVERY_INSTRUCTION_COUNT, DELIVERY_METHOD,
            ORDER_CLASS, LABEL_ID, DELAY_PREVENTION_MAKER_SYMBOL, SHIPTO_CLASS, SHIPTO_CODE, LABEL_OUTPUT, RESERVE2,
            LABEL_NO_CHANGE_MARK, SELF_PROCUREMENT_LINE, LABEL_SPECIFICATION, SELF_PROCUREMENT_KD, RESERVE3, KD_ELEMENTS,
            TRANSIT_CODE, TRANSIT_DELIVERY_DATE, PARTS_ID, DELIVERY_TICKET_NO, WITHDRAWAL_CLASS, PART_PRICE,
            PROTOTYPE_SYMBOL, RESEND_SYMBOL, ORDER_DATE, LABEL_SIZE, RESERVE4, RESERVE5, UPDATE_TIME, LABEL_TERMINAL_NO,
            LIST_TERMINAL_NO, MAKER_PIC, MAKER_SNP, RESEND_DEPOT, PARTS_NO, PRODUCT_CLASS, PRODUCTION_METHOD,
            DELIVERY_METHOD2, FROM_PROCESS, FROM_WORK_AREA, SHIPPING_PIC, PARTS_NO_ID_CODE, INSTRUCTION_DATE,
            INSTRUCTION_TIME, INSTRUCTION_NO, PRODUCTION_HANDLING_METHOD, PLANT_CODE, SHIPPING_LOCATION, SHIPPING_PORT,
            SHIPPING_CHECK_FLAG, RESERVE6, LABEL_ISSUE_ASSIGNMENT, DIVISION_DEPOT, CUSTOMER_CODE, DEMAND_CODE2,
            SHIPPING_LOCATION_CODE2, MOVEMENT_LEAD_TIME, REHANDLING_FLAG, NO_MASTER_FLAG, RESERVE7, DIVISION_DEPOT2
        from tbl_sap_kanban_line a
    </sql>

    <select id="selectTblSapKanbanKdList" parameterType="TblSapKanbanKd" resultMap="TblSapKanbanKdResult">
        <include refid="selectTblSapKanbanKdVo"/>
        <where>
            <if test="fileNo != null and fileNo != ''"> and FILE_NO = #{fileNo}</if>
            <if test="reserve1 != null and reserve1 != ''"> and RESERVE1 = #{reserve1}</if>
            <if test="demandCode != null and demandCode != ''"> and DEMAND_CODE = #{demandCode}</if>
            <if test="issueNo != null and issueNo != ''"> and ISSUE_NO = #{issueNo}</if>
            <if test="deliveryInstructionDate != null and deliveryInstructionDate != ''"> and DELIVERY_INSTRUCTION_DATE = #{deliveryInstructionDate}</if>
            <if test="deliveryInstructionTime != null and deliveryInstructionTime != ''"> and DELIVERY_INSTRUCTION_TIME = #{deliveryInstructionTime}</if>
            <if test="maker != null and maker != ''"> and MAKER = #{maker}</if>
            <if test="depot != null and depot != ''"> and DEPOT = #{depot}</if>
            <if test="classification != null and classification != ''"> and CLASSIFICATION = #{classification}</if>
            <if test="deliveryLocation != null and deliveryLocation != ''"> and DELIVERY_LOCATION = #{deliveryLocation}</if>
            <if test="unloadingUnit != null and unloadingUnit != ''"> and UNLOADING_UNIT = #{unloadingUnit}</if>
            <if test="customerPartsNo != null and customerPartsNo != ''"> and CUSTOMER_PARTS_NO = #{customerPartsNo}</if>
            <if test="currentProcessClass != null and currentProcessClass != ''"> and CURRENT_PROCESS_CLASS = #{currentProcessClass}</if>
            <if test="partsName != null and partsName != ''"> and PARTS_NAME = #{partsName}</if>
            <if test="deliveryInstructionNumber != null and deliveryInstructionNumber != ''"> and DELIVERY_INSTRUCTION_NUMBER = #{deliveryInstructionNumber}</if>
            <if test="supplyLocation != null and supplyLocation != ''"> and SUPPLY_LOCATION = #{supplyLocation}</if>
            <if test="snep != null and snep != ''"> and SNEP = #{snep}</if>
            <if test="safetyFlag != null and safetyFlag != ''"> and SAFETY_FLAG = #{safetyFlag}</if>
            <if test="labelCount != null and labelCount != ''"> and LABEL_COUNT = #{labelCount}</if>
            <if test="managementPic != null and managementPic != ''"> and MANAGEMENT_PIC = #{managementPic}</if>
            <if test="issueReason != null and issueReason != ''"> and ISSUE_REASON = #{issueReason}</if>
            <if test="vipFlag != null and vipFlag != ''"> and VIP_FLAG = #{vipFlag}</if>
            <if test="packageCode != null and packageCode != ''"> and PACKAGE_CODE = #{packageCode}</if>
            <if test="internalExternalClass != null and internalExternalClass != ''"> and INTERNAL_EXTERNAL_CLASS = #{internalExternalClass}</if>
            <if test="deliveryInstructionCount != null and deliveryInstructionCount != ''"> and DELIVERY_INSTRUCTION_COUNT = #{deliveryInstructionCount}</if>
            <if test="deliveryMethod != null and deliveryMethod != ''"> and DELIVERY_METHOD = #{deliveryMethod}</if>
            <if test="orderClass != null and orderClass != ''"> and ORDER_CLASS = #{orderClass}</if>
            <if test="labelId != null and labelId != ''"> and LABEL_ID = #{labelId}</if>
            <if test="delayPreventionMakerSymbol != null and delayPreventionMakerSymbol != ''"> and DELAY_PREVENTION_MAKER_SYMBOL = #{delayPreventionMakerSymbol}</if>
            <if test="shiptoClass != null and shiptoClass != ''"> and SHIPTO_CLASS = #{shiptoClass}</if>
            <if test="shiptoCode != null and shiptoCode != ''"> and SHIPTO_CODE = #{shiptoCode}</if>
            <if test="labelOutput != null and labelOutput != ''"> and LABEL_OUTPUT = #{labelOutput}</if>
            <if test="reserve2 != null and reserve2 != ''"> and RESERVE2 = #{reserve2}</if>
            <if test="labelNoChangeMark != null and labelNoChangeMark != ''"> and LABEL_NO_CHANGE_MARK = #{labelNoChangeMark}</if>
            <if test="selfProcurementLine != null and selfProcurementLine != ''"> and SELF_PROCUREMENT_LINE = #{selfProcurementLine}</if>
            <if test="labelSpecification != null and labelSpecification != ''"> and LABEL_SPECIFICATION = #{labelSpecification}</if>
            <if test="selfProcurementKd != null and selfProcurementKd != ''"> and SELF_PROCUREMENT_KD = #{selfProcurementKd}</if>
            <if test="reserve3 != null and reserve3 != ''"> and RESERVE3 = #{reserve3}</if>
            <if test="kdElements != null and kdElements != ''"> and KD_ELEMENTS = #{kdElements}</if>
            <if test="transitCode != null and transitCode != ''"> and TRANSIT_CODE = #{transitCode}</if>
            <if test="transitDeliveryDate != null and transitDeliveryDate != ''"> and TRANSIT_DELIVERY_DATE = #{transitDeliveryDate}</if>
            <if test="partsId != null and partsId != ''"> and PARTS_ID = #{partsId}</if>
            <if test="deliveryTicketNo != null and deliveryTicketNo != ''"> and DELIVERY_TICKET_NO = #{deliveryTicketNo}</if>
            <if test="withdrawalClass != null and withdrawalClass != ''"> and WITHDRAWAL_CLASS = #{withdrawalClass}</if>
            <if test="partPrice != null and partPrice != ''"> and PART_PRICE = #{partPrice}</if>
            <if test="prototypeSymbol != null and prototypeSymbol != ''"> and PROTOTYPE_SYMBOL = #{prototypeSymbol}</if>
            <if test="resendSymbol != null and resendSymbol != ''"> and RESEND_SYMBOL = #{resendSymbol}</if>
            <if test="orderDate != null and orderDate != ''"> and ORDER_DATE = #{orderDate}</if>
            <if test="labelSize != null and labelSize != ''"> and LABEL_SIZE = #{labelSize}</if>
            <if test="reserve4 != null and reserve4 != ''"> and RESERVE4 = #{reserve4}</if>
            <if test="reserve5 != null and reserve5 != ''"> and RESERVE5 = #{reserve5}</if>
            <if test="labelTerminalNo != null and labelTerminalNo != ''"> and LABEL_TERMINAL_NO = #{labelTerminalNo}</if>
            <if test="listTerminalNo != null and listTerminalNo != ''"> and LIST_TERMINAL_NO = #{listTerminalNo}</if>
            <if test="makerPic != null and makerPic != ''"> and MAKER_PIC = #{makerPic}</if>
            <if test="makerSnp != null and makerSnp != ''"> and MAKER_SNP = #{makerSnp}</if>
            <if test="resendDepot != null and resendDepot != ''"> and RESEND_DEPOT = #{resendDepot}</if>
            <if test="partsNo != null and partsNo != ''"> and PARTS_NO = #{partsNo}</if>
            <if test="productClass != null and productClass != ''"> and PRODUCT_CLASS = #{productClass}</if>
            <if test="productionMethod != null and productionMethod != ''"> and PRODUCTION_METHOD = #{productionMethod}</if>
            <if test="deliveryMethod2 != null and deliveryMethod2 != ''"> and DELIVERY_METHOD2 = #{deliveryMethod2}</if>
            <if test="fromProcess != null and fromProcess != ''"> and FROM_PROCESS = #{fromProcess}</if>
            <if test="fromWorkArea != null and fromWorkArea != ''"> and FROM_WORK_AREA = #{fromWorkArea}</if>
            <if test="shippingPic != null and shippingPic != ''"> and SHIPPING_PIC = #{shippingPic}</if>
            <if test="partsNoIdCode != null and partsNoIdCode != ''"> and PARTS_NO_ID_CODE = #{partsNoIdCode}</if>
            <if test="instructionDate != null and instructionDate != ''"> and INSTRUCTION_DATE = #{instructionDate}</if>
            <if test="instructionTime != null and instructionTime != ''"> and INSTRUCTION_TIME = #{instructionTime}</if>
            <if test="instructionNo != null and instructionNo != ''"> and INSTRUCTION_NO = #{instructionNo}</if>
            <if test="productionHandlingMethod != null and productionHandlingMethod != ''"> and PRODUCTION_HANDLING_METHOD = #{productionHandlingMethod}</if>
            <if test="plantCode != null and plantCode != ''"> and PLANT_CODE = #{plantCode}</if>
            <if test="shippingLocation != null and shippingLocation != ''"> and SHIPPING_LOCATION = #{shippingLocation}</if>
            <if test="shippingPort != null and shippingPort != ''"> and SHIPPING_PORT = #{shippingPort}</if>
            <if test="shippingCheckFlag != null and shippingCheckFlag != ''"> and SHIPPING_CHECK_FLAG = #{shippingCheckFlag}</if>
            <if test="reserve6 != null and reserve6 != ''"> and RESERVE6 = #{reserve6}</if>
            <if test="labelIssueAssignment != null and labelIssueAssignment != ''"> and LABEL_ISSUE_ASSIGNMENT = #{labelIssueAssignment}</if>
            <if test="divisionDepot != null and divisionDepot != ''"> and DIVISION_DEPOT = #{divisionDepot}</if>
            <if test="customerCode != null and customerCode != ''"> and CUSTOMER_CODE = #{customerCode}</if>
            <if test="demandCode2 != null and demandCode2 != ''"> and DEMAND_CODE2 = #{demandCode2}</if>
            <if test="shippingLocationCode2 != null and shippingLocationCode2 != ''"> and SHIPPING_LOCATION_CODE2 = #{shippingLocationCode2}</if>
            <if test="movementLeadTime != null and movementLeadTime != ''"> and MOVEMENT_LEAD_TIME = #{movementLeadTime}</if>
            <if test="rehandlingFlag != null and rehandlingFlag != ''"> and REHANDLING_FLAG = #{rehandlingFlag}</if>
            <if test="noMasterFlag != null and noMasterFlag != ''"> and NO_MASTER_FLAG = #{noMasterFlag}</if>
            <if test="reserve7 != null and reserve7 != ''"> and RESERVE7 = #{reserve7}</if>
            <if test="divisionDepot2 != null and divisionDepot2 != ''"> and DIVISION_DEPOT2 = #{divisionDepot2}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by DELIVERY_INSTRUCTION_DATE desc, DELIVERY_INSTRUCTION_TIME desc, id desc
    </select>
    
    <select id="selectTblSapKanbanKdById" parameterType="Long" resultMap="TblSapKanbanKdResult">
        <include refid="selectTblSapKanbanKdVo"/>
        where id = #{id}
    </select>

    <select id="selectTblSapKanbanLineById" parameterType="Long" resultMap="TblSapKanbanKdResult">
        <include refid="selectTblSapKanbanLineVo"/>
        where id = #{id}
    </select>

    <insert id="insertTblSapKanbanKd" parameterType="TblSapKanbanKd">
        insert into tbl_sap_kanban_kd
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileNo != null">FILE_NO,</if>
            <if test="reserve1 != null">RESERVE1,</if>
            <if test="demandCode != null">DEMAND_CODE,</if>
            <if test="issueNo != null">ISSUE_NO,</if>
            <if test="deliveryInstructionDate != null">DELIVERY_INSTRUCTION_DATE,</if>
            <if test="deliveryInstructionTime != null">DELIVERY_INSTRUCTION_TIME,</if>
            <if test="maker != null">MAKER,</if>
            <if test="depot != null">DEPOT,</if>
            <if test="classification != null">CLASSIFICATION,</if>
            <if test="deliveryLocation != null">DELIVERY_LOCATION,</if>
            <if test="unloadingUnit != null">UNLOADING_UNIT,</if>
            <if test="customerPartsNo != null">CUSTOMER_PARTS_NO,</if>
            <if test="currentProcessClass != null">CURRENT_PROCESS_CLASS,</if>
            <if test="partsName != null">PARTS_NAME,</if>
            <if test="deliveryInstructionNumber != null">DELIVERY_INSTRUCTION_NUMBER,</if>
            <if test="supplyLocation != null">SUPPLY_LOCATION,</if>
            <if test="snep != null">SNEP,</if>
            <if test="safetyFlag != null">SAFETY_FLAG,</if>
            <if test="labelCount != null">LABEL_COUNT,</if>
            <if test="managementPic != null">MANAGEMENT_PIC,</if>
            <if test="issueReason != null">ISSUE_REASON,</if>
            <if test="vipFlag != null">VIP_FLAG,</if>
            <if test="packageCode != null">PACKAGE_CODE,</if>
            <if test="internalExternalClass != null">INTERNAL_EXTERNAL_CLASS,</if>
            <if test="deliveryInstructionCount != null">DELIVERY_INSTRUCTION_COUNT,</if>
            <if test="deliveryMethod != null">DELIVERY_METHOD,</if>
            <if test="orderClass != null">ORDER_CLASS,</if>
            <if test="labelId != null">LABEL_ID,</if>
            <if test="delayPreventionMakerSymbol != null">DELAY_PREVENTION_MAKER_SYMBOL,</if>
            <if test="shiptoClass != null">SHIPTO_CLASS,</if>
            <if test="shiptoCode != null">SHIPTO_CODE,</if>
            <if test="labelOutput != null">LABEL_OUTPUT,</if>
            <if test="reserve2 != null">RESERVE2,</if>
            <if test="labelNoChangeMark != null">LABEL_NO_CHANGE_MARK,</if>
            <if test="selfProcurementLine != null">SELF_PROCUREMENT_LINE,</if>
            <if test="labelSpecification != null">LABEL_SPECIFICATION,</if>
            <if test="selfProcurementKd != null">SELF_PROCUREMENT_KD,</if>
            <if test="reserve3 != null">RESERVE3,</if>
            <if test="kdElements != null">KD_ELEMENTS,</if>
            <if test="transitCode != null">TRANSIT_CODE,</if>
            <if test="transitDeliveryDate != null">TRANSIT_DELIVERY_DATE,</if>
            <if test="partsId != null">PARTS_ID,</if>
            <if test="deliveryTicketNo != null">DELIVERY_TICKET_NO,</if>
            <if test="withdrawalClass != null">WITHDRAWAL_CLASS,</if>
            <if test="partPrice != null">PART_PRICE,</if>
            <if test="prototypeSymbol != null">PROTOTYPE_SYMBOL,</if>
            <if test="resendSymbol != null">RESEND_SYMBOL,</if>
            <if test="orderDate != null">ORDER_DATE,</if>
            <if test="labelSize != null">LABEL_SIZE,</if>
            <if test="reserve4 != null">RESERVE4,</if>
            <if test="reserve5 != null">RESERVE5,</if>
            UPDATE_TIME,
            <if test="labelTerminalNo != null">LABEL_TERMINAL_NO,</if>
            <if test="listTerminalNo != null">LIST_TERMINAL_NO,</if>
            <if test="makerPic != null">MAKER_PIC,</if>
            <if test="makerSnp != null">MAKER_SNP,</if>
            <if test="resendDepot != null">RESEND_DEPOT,</if>
            <if test="partsNo != null">PARTS_NO,</if>
            <if test="productClass != null">PRODUCT_CLASS,</if>
            <if test="productionMethod != null">PRODUCTION_METHOD,</if>
            <if test="deliveryMethod2 != null">DELIVERY_METHOD2,</if>
            <if test="fromProcess != null">FROM_PROCESS,</if>
            <if test="fromWorkArea != null">FROM_WORK_AREA,</if>
            <if test="shippingPic != null">SHIPPING_PIC,</if>
            <if test="partsNoIdCode != null">PARTS_NO_ID_CODE,</if>
            <if test="instructionDate != null">INSTRUCTION_DATE,</if>
            <if test="instructionTime != null">INSTRUCTION_TIME,</if>
            <if test="instructionNo != null">INSTRUCTION_NO,</if>
            <if test="productionHandlingMethod != null">PRODUCTION_HANDLING_METHOD,</if>
            <if test="plantCode != null">PLANT_CODE,</if>
            <if test="shippingLocation != null">SHIPPING_LOCATION,</if>
            <if test="shippingPort != null">SHIPPING_PORT,</if>
            <if test="shippingCheckFlag != null">SHIPPING_CHECK_FLAG,</if>
            <if test="reserve6 != null">RESERVE6,</if>
            <if test="labelIssueAssignment != null">LABEL_ISSUE_ASSIGNMENT,</if>
            <if test="divisionDepot != null">DIVISION_DEPOT,</if>
            <if test="customerCode != null">CUSTOMER_CODE,</if>
            <if test="demandCode2 != null">DEMAND_CODE2,</if>
            <if test="shippingLocationCode2 != null">SHIPPING_LOCATION_CODE2,</if>
            <if test="movementLeadTime != null">MOVEMENT_LEAD_TIME,</if>
            <if test="rehandlingFlag != null">REHANDLING_FLAG,</if>
            <if test="noMasterFlag != null">NO_MASTER_FLAG,</if>
            <if test="reserve7 != null">RESERVE7,</if>
            <if test="divisionDepot2 != null">DIVISION_DEPOT2,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileNo != null">#{fileNo},</if>
            <if test="reserve1 != null">#{reserve1},</if>
            <if test="demandCode != null">#{demandCode},</if>
            <if test="issueNo != null">#{issueNo},</if>
            <if test="deliveryInstructionDate != null">#{deliveryInstructionDate},</if>
            <if test="deliveryInstructionTime != null">#{deliveryInstructionTime},</if>
            <if test="maker != null">#{maker},</if>
            <if test="depot != null">#{depot},</if>
            <if test="classification != null">#{classification},</if>
            <if test="deliveryLocation != null">#{deliveryLocation},</if>
            <if test="unloadingUnit != null">#{unloadingUnit},</if>
            <if test="customerPartsNo != null">#{customerPartsNo},</if>
            <if test="currentProcessClass != null">#{currentProcessClass},</if>
            <if test="partsName != null">#{partsName},</if>
            <if test="deliveryInstructionNumber != null">#{deliveryInstructionNumber},</if>
            <if test="supplyLocation != null">#{supplyLocation},</if>
            <if test="snep != null">#{snep},</if>
            <if test="safetyFlag != null">#{safetyFlag},</if>
            <if test="labelCount != null">#{labelCount},</if>
            <if test="managementPic != null">#{managementPic},</if>
            <if test="issueReason != null">#{issueReason},</if>
            <if test="vipFlag != null">#{vipFlag},</if>
            <if test="packageCode != null">#{packageCode},</if>
            <if test="internalExternalClass != null">#{internalExternalClass},</if>
            <if test="deliveryInstructionCount != null">#{deliveryInstructionCount},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="orderClass != null">#{orderClass},</if>
            <if test="labelId != null">#{labelId},</if>
            <if test="delayPreventionMakerSymbol != null">#{delayPreventionMakerSymbol},</if>
            <if test="shiptoClass != null">#{shiptoClass},</if>
            <if test="shiptoCode != null">#{shiptoCode},</if>
            <if test="labelOutput != null">#{labelOutput},</if>
            <if test="reserve2 != null">#{reserve2},</if>
            <if test="labelNoChangeMark != null">#{labelNoChangeMark},</if>
            <if test="selfProcurementLine != null">#{selfProcurementLine},</if>
            <if test="labelSpecification != null">#{labelSpecification},</if>
            <if test="selfProcurementKd != null">#{selfProcurementKd},</if>
            <if test="reserve3 != null">#{reserve3},</if>
            <if test="kdElements != null">#{kdElements},</if>
            <if test="transitCode != null">#{transitCode},</if>
            <if test="transitDeliveryDate != null">#{transitDeliveryDate},</if>
            <if test="partsId != null">#{partsId},</if>
            <if test="deliveryTicketNo != null">#{deliveryTicketNo},</if>
            <if test="withdrawalClass != null">#{withdrawalClass},</if>
            <if test="partPrice != null">#{partPrice},</if>
            <if test="prototypeSymbol != null">#{prototypeSymbol},</if>
            <if test="resendSymbol != null">#{resendSymbol},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="labelSize != null">#{labelSize},</if>
            <if test="reserve4 != null">#{reserve4},</if>
            <if test="reserve5 != null">#{reserve5},</if>
            sysdate(),
            <if test="labelTerminalNo != null">#{labelTerminalNo},</if>
            <if test="listTerminalNo != null">#{listTerminalNo},</if>
            <if test="makerPic != null">#{makerPic},</if>
            <if test="makerSnp != null">#{makerSnp},</if>
            <if test="resendDepot != null">#{resendDepot},</if>
            <if test="partsNo != null">#{partsNo},</if>
            <if test="productClass != null">#{productClass},</if>
            <if test="productionMethod != null">#{productionMethod},</if>
            <if test="deliveryMethod2 != null">#{deliveryMethod2},</if>
            <if test="fromProcess != null">#{fromProcess},</if>
            <if test="fromWorkArea != null">#{fromWorkArea},</if>
            <if test="shippingPic != null">#{shippingPic},</if>
            <if test="partsNoIdCode != null">#{partsNoIdCode},</if>
            <if test="instructionDate != null">#{instructionDate},</if>
            <if test="instructionTime != null">#{instructionTime},</if>
            <if test="instructionNo != null">#{instructionNo},</if>
            <if test="productionHandlingMethod != null">#{productionHandlingMethod},</if>
            <if test="plantCode != null">#{plantCode},</if>
            <if test="shippingLocation != null">#{shippingLocation},</if>
            <if test="shippingPort != null">#{shippingPort},</if>
            <if test="shippingCheckFlag != null">#{shippingCheckFlag},</if>
            <if test="reserve6 != null">#{reserve6},</if>
            <if test="labelIssueAssignment != null">#{labelIssueAssignment},</if>
            <if test="divisionDepot != null">#{divisionDepot},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="demandCode2 != null">#{demandCode2},</if>
            <if test="shippingLocationCode2 != null">#{shippingLocationCode2},</if>
            <if test="movementLeadTime != null">#{movementLeadTime},</if>
            <if test="rehandlingFlag != null">#{rehandlingFlag},</if>
            <if test="noMasterFlag != null">#{noMasterFlag},</if>
            <if test="reserve7 != null">#{reserve7},</if>
            <if test="divisionDepot2 != null">#{divisionDepot2},</if>
        </trim>
    </insert>

    <update id="updateTblSapKanbanKd" parameterType="TblSapKanbanKd">
        update tbl_sap_kanban_kd
        set 
            FILE_NO = #{fileNo},
            RESERVE1 = #{reserve1},
            DEMAND_CODE = #{demandCode},
            ISSUE_NO = #{issueNo},
            DELIVERY_INSTRUCTION_DATE = #{deliveryInstructionDate},
            DELIVERY_INSTRUCTION_TIME = #{deliveryInstructionTime},
            MAKER = #{maker},
            DEPOT = #{depot},
            CLASSIFICATION = #{classification},
            DELIVERY_LOCATION = #{deliveryLocation},
            UNLOADING_UNIT = #{unloadingUnit},
            CUSTOMER_PARTS_NO = #{customerPartsNo},
            CURRENT_PROCESS_CLASS = #{currentProcessClass},
            PARTS_NAME = #{partsName},
            DELIVERY_INSTRUCTION_NUMBER = #{deliveryInstructionNumber},
            SUPPLY_LOCATION = #{supplyLocation},
            SNEP = #{snep},
            SAFETY_FLAG = #{safetyFlag},
            LABEL_COUNT = #{labelCount},
            MANAGEMENT_PIC = #{managementPic},
            ISSUE_REASON = #{issueReason},
            VIP_FLAG = #{vipFlag},
            PACKAGE_CODE = #{packageCode},
            INTERNAL_EXTERNAL_CLASS = #{internalExternalClass},
            DELIVERY_INSTRUCTION_COUNT = #{deliveryInstructionCount},
            DELIVERY_METHOD = #{deliveryMethod},
            ORDER_CLASS = #{orderClass},
            LABEL_ID = #{labelId},
            DELAY_PREVENTION_MAKER_SYMBOL = #{delayPreventionMakerSymbol},
            SHIPTO_CLASS = #{shiptoClass},
            SHIPTO_CODE = #{shiptoCode},
            LABEL_OUTPUT = #{labelOutput},
            RESERVE2 = #{reserve2},
            LABEL_NO_CHANGE_MARK = #{labelNoChangeMark},
            SELF_PROCUREMENT_LINE = #{selfProcurementLine},
            LABEL_SPECIFICATION = #{labelSpecification},
            SELF_PROCUREMENT_KD = #{selfProcurementKd},
            RESERVE3 = #{reserve3},
            KD_ELEMENTS = #{kdElements},
            TRANSIT_CODE = #{transitCode},
            TRANSIT_DELIVERY_DATE = #{transitDeliveryDate},
            PARTS_ID = #{partsId},
            DELIVERY_TICKET_NO = #{deliveryTicketNo},
            WITHDRAWAL_CLASS = #{withdrawalClass},
            PART_PRICE = #{partPrice},
            PROTOTYPE_SYMBOL = #{prototypeSymbol},
            RESEND_SYMBOL = #{resendSymbol},
            ORDER_DATE = #{orderDate},
            LABEL_SIZE = #{labelSize},
            RESERVE4 = #{reserve4},
            RESERVE5 = #{reserve5},
            LABEL_TERMINAL_NO = #{labelTerminalNo},
            LIST_TERMINAL_NO = #{listTerminalNo},
            MAKER_PIC = #{makerPic},
            MAKER_SNP = #{makerSnp},
            RESEND_DEPOT = #{resendDepot},
            PARTS_NO = #{partsNo},
            PRODUCT_CLASS = #{productClass},
            PRODUCTION_METHOD = #{productionMethod},
            DELIVERY_METHOD2 = #{deliveryMethod2},
            FROM_PROCESS = #{fromProcess},
            FROM_WORK_AREA = #{fromWorkArea},
            SHIPPING_PIC = #{shippingPic},
            PARTS_NO_ID_CODE = #{partsNoIdCode},
            INSTRUCTION_DATE = #{instructionDate},
            INSTRUCTION_TIME = #{instructionTime},
            INSTRUCTION_NO = #{instructionNo},
            PRODUCTION_HANDLING_METHOD = #{productionHandlingMethod},
            PLANT_CODE = #{plantCode},
            SHIPPING_LOCATION = #{shippingLocation},
            SHIPPING_PORT = #{shippingPort},
            SHIPPING_CHECK_FLAG = #{shippingCheckFlag},
            RESERVE6 = #{reserve6},
            LABEL_ISSUE_ASSIGNMENT = #{labelIssueAssignment},
            DIVISION_DEPOT = #{divisionDepot},
            CUSTOMER_CODE = #{customerCode},
            DEMAND_CODE2 = #{demandCode2},
            SHIPPING_LOCATION_CODE2 = #{shippingLocationCode2},
            MOVEMENT_LEAD_TIME = #{movementLeadTime},
            REHANDLING_FLAG = #{rehandlingFlag},
            NO_MASTER_FLAG = #{noMasterFlag},
            RESERVE7 = #{reserve7},
            DIVISION_DEPOT2 = #{divisionDepot2},
            SUPP_CODE = left(#{fromWorkArea}, 5),
            UPDATE_TIME = sysdate()
        where id = #{id}
    </update>

    <delete id="deleteTblSapKanbanKdById" parameterType="String">
        delete from tbl_sap_kanban_kd where FILE_NO = #{fileNo}
    </delete>

    <delete id="deleteTblSapKanbanKdByIds" parameterType="String">
        delete from tbl_sap_kanban_kd where FILE_NO in 
        <foreach item="fileNo" collection="array" open="(" separator="," close=")">
            #{fileNo}
        </foreach>
    </delete>

    <insert id="insertTblSapKanbanLine" parameterType="TblSapKanbanKd">
        insert into tbl_sap_kanban_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileNo != null">FILE_NO,</if>
            <if test="reserve1 != null">RESERVE1,</if>
            <if test="demandCode != null">DEMAND_CODE,</if>
            <if test="issueNo != null">ISSUE_NO,</if>
            <if test="deliveryInstructionDate != null">DELIVERY_INSTRUCTION_DATE,</if>
            <if test="deliveryInstructionTime != null">DELIVERY_INSTRUCTION_TIME,</if>
            <if test="maker != null">MAKER,</if>
            <if test="depot != null">DEPOT,</if>
            <if test="classification != null">CLASSIFICATION,</if>
            <if test="deliveryLocation != null">DELIVERY_LOCATION,</if>
            <if test="unloadingUnit != null">UNLOADING_UNIT,</if>
            <if test="customerPartsNo != null">CUSTOMER_PARTS_NO,</if>
            <if test="currentProcessClass != null">CURRENT_PROCESS_CLASS,</if>
            <if test="partsName != null">PARTS_NAME,</if>
            <if test="deliveryInstructionNumber != null">DELIVERY_INSTRUCTION_NUMBER,</if>
            <if test="supplyLocation != null">SUPPLY_LOCATION,</if>
            <if test="snep != null">SNEP,</if>
            <if test="safetyFlag != null">SAFETY_FLAG,</if>
            <if test="labelCount != null">LABEL_COUNT,</if>
            <if test="managementPic != null">MANAGEMENT_PIC,</if>
            <if test="issueReason != null">ISSUE_REASON,</if>
            <if test="vipFlag != null">VIP_FLAG,</if>
            <if test="packageCode != null">PACKAGE_CODE,</if>
            <if test="internalExternalClass != null">INTERNAL_EXTERNAL_CLASS,</if>
            <if test="deliveryInstructionCount != null">DELIVERY_INSTRUCTION_COUNT,</if>
            <if test="deliveryMethod != null">DELIVERY_METHOD,</if>
            <if test="orderClass != null">ORDER_CLASS,</if>
            <if test="labelId != null">LABEL_ID,</if>
            <if test="delayPreventionMakerSymbol != null">DELAY_PREVENTION_MAKER_SYMBOL,</if>
            <if test="shiptoClass != null">SHIPTO_CLASS,</if>
            <if test="shiptoCode != null">SHIPTO_CODE,</if>
            <if test="labelOutput != null">LABEL_OUTPUT,</if>
            <if test="reserve2 != null">RESERVE2,</if>
            <if test="labelNoChangeMark != null">LABEL_NO_CHANGE_MARK,</if>
            <if test="selfProcurementLine != null">SELF_PROCUREMENT_LINE,</if>
            <if test="labelSpecification != null">LABEL_SPECIFICATION,</if>
            <if test="selfProcurementKd != null">SELF_PROCUREMENT_KD,</if>
            <if test="reserve3 != null">RESERVE3,</if>
            <if test="kdElements != null">KD_ELEMENTS,</if>
            <if test="transitCode != null">TRANSIT_CODE,</if>
            <if test="transitDeliveryDate != null">TRANSIT_DELIVERY_DATE,</if>
            <if test="partsId != null">PARTS_ID,</if>
            <if test="deliveryTicketNo != null">DELIVERY_TICKET_NO,</if>
            <if test="withdrawalClass != null">WITHDRAWAL_CLASS,</if>
            <if test="partPrice != null">PART_PRICE,</if>
            <if test="prototypeSymbol != null">PROTOTYPE_SYMBOL,</if>
            <if test="resendSymbol != null">RESEND_SYMBOL,</if>
            <if test="orderDate != null">ORDER_DATE,</if>
            <if test="labelSize != null">LABEL_SIZE,</if>
            <if test="reserve4 != null">RESERVE4,</if>
            <if test="reserve5 != null">RESERVE5,</if>
            UPDATE_TIME,
            <if test="labelTerminalNo != null">LABEL_TERMINAL_NO,</if>
            <if test="listTerminalNo != null">LIST_TERMINAL_NO,</if>
            <if test="makerPic != null">MAKER_PIC,</if>
            <if test="makerSnp != null">MAKER_SNP,</if>
            <if test="resendDepot != null">RESEND_DEPOT,</if>
            <if test="partsNo != null">PARTS_NO,</if>
            <if test="productClass != null">PRODUCT_CLASS,</if>
            <if test="productionMethod != null">PRODUCTION_METHOD,</if>
            <if test="deliveryMethod2 != null">DELIVERY_METHOD2,</if>
            <if test="fromProcess != null">FROM_PROCESS,</if>
            <if test="fromWorkArea != null">FROM_WORK_AREA,</if>
            <if test="shippingPic != null">SHIPPING_PIC,</if>
            <if test="partsNoIdCode != null">PARTS_NO_ID_CODE,</if>
            <if test="instructionDate != null">INSTRUCTION_DATE,</if>
            <if test="instructionTime != null">INSTRUCTION_TIME,</if>
            <if test="instructionNo != null">INSTRUCTION_NO,</if>
            <if test="productionHandlingMethod != null">PRODUCTION_HANDLING_METHOD,</if>
            <if test="plantCode != null">PLANT_CODE,</if>
            <if test="shippingLocation != null">SHIPPING_LOCATION,</if>
            <if test="shippingPort != null">SHIPPING_PORT,</if>
            <if test="shippingCheckFlag != null">SHIPPING_CHECK_FLAG,</if>
            <if test="reserve6 != null">RESERVE6,</if>
            <if test="labelIssueAssignment != null">LABEL_ISSUE_ASSIGNMENT,</if>
            <if test="divisionDepot != null">DIVISION_DEPOT,</if>
            <if test="customerCode != null">CUSTOMER_CODE,</if>
            <if test="demandCode2 != null">DEMAND_CODE2,</if>
            <if test="shippingLocationCode2 != null">SHIPPING_LOCATION_CODE2,</if>
            <if test="movementLeadTime != null">MOVEMENT_LEAD_TIME,</if>
            <if test="rehandlingFlag != null">REHANDLING_FLAG,</if>
            <if test="noMasterFlag != null">NO_MASTER_FLAG,</if>
            <if test="reserve7 != null">RESERVE7,</if>
            <if test="divisionDepot2 != null">DIVISION_DEPOT2,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileNo != null">#{fileNo},</if>
            <if test="reserve1 != null">#{reserve1},</if>
            <if test="demandCode != null">#{demandCode},</if>
            <if test="issueNo != null">#{issueNo},</if>
            <if test="deliveryInstructionDate != null">#{deliveryInstructionDate},</if>
            <if test="deliveryInstructionTime != null">#{deliveryInstructionTime},</if>
            <if test="maker != null">#{maker},</if>
            <if test="depot != null">#{depot},</if>
            <if test="classification != null">#{classification},</if>
            <if test="deliveryLocation != null">#{deliveryLocation},</if>
            <if test="unloadingUnit != null">#{unloadingUnit},</if>
            <if test="customerPartsNo != null">#{customerPartsNo},</if>
            <if test="currentProcessClass != null">#{currentProcessClass},</if>
            <if test="partsName != null">#{partsName},</if>
            <if test="deliveryInstructionNumber != null">#{deliveryInstructionNumber},</if>
            <if test="supplyLocation != null">#{supplyLocation},</if>
            <if test="snep != null">#{snep},</if>
            <if test="safetyFlag != null">#{safetyFlag},</if>
            <if test="labelCount != null">#{labelCount},</if>
            <if test="managementPic != null">#{managementPic},</if>
            <if test="issueReason != null">#{issueReason},</if>
            <if test="vipFlag != null">#{vipFlag},</if>
            <if test="packageCode != null">#{packageCode},</if>
            <if test="internalExternalClass != null">#{internalExternalClass},</if>
            <if test="deliveryInstructionCount != null">#{deliveryInstructionCount},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="orderClass != null">#{orderClass},</if>
            <if test="labelId != null">#{labelId},</if>
            <if test="delayPreventionMakerSymbol != null">#{delayPreventionMakerSymbol},</if>
            <if test="shiptoClass != null">#{shiptoClass},</if>
            <if test="shiptoCode != null">#{shiptoCode},</if>
            <if test="labelOutput != null">#{labelOutput},</if>
            <if test="reserve2 != null">#{reserve2},</if>
            <if test="labelNoChangeMark != null">#{labelNoChangeMark},</if>
            <if test="selfProcurementLine != null">#{selfProcurementLine},</if>
            <if test="labelSpecification != null">#{labelSpecification},</if>
            <if test="selfProcurementKd != null">#{selfProcurementKd},</if>
            <if test="reserve3 != null">#{reserve3},</if>
            <if test="kdElements != null">#{kdElements},</if>
            <if test="transitCode != null">#{transitCode},</if>
            <if test="transitDeliveryDate != null">#{transitDeliveryDate},</if>
            <if test="partsId != null">#{partsId},</if>
            <if test="deliveryTicketNo != null">#{deliveryTicketNo},</if>
            <if test="withdrawalClass != null">#{withdrawalClass},</if>
            <if test="partPrice != null">#{partPrice},</if>
            <if test="prototypeSymbol != null">#{prototypeSymbol},</if>
            <if test="resendSymbol != null">#{resendSymbol},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="labelSize != null">#{labelSize},</if>
            <if test="reserve4 != null">#{reserve4},</if>
            <if test="reserve5 != null">#{reserve5},</if>
            sysdate(),
            <if test="labelTerminalNo != null">#{labelTerminalNo},</if>
            <if test="listTerminalNo != null">#{listTerminalNo},</if>
            <if test="makerPic != null">#{makerPic},</if>
            <if test="makerSnp != null">#{makerSnp},</if>
            <if test="resendDepot != null">#{resendDepot},</if>
            <if test="partsNo != null">#{partsNo},</if>
            <if test="productClass != null">#{productClass},</if>
            <if test="productionMethod != null">#{productionMethod},</if>
            <if test="deliveryMethod2 != null">#{deliveryMethod2},</if>
            <if test="fromProcess != null">#{fromProcess},</if>
            <if test="fromWorkArea != null">#{fromWorkArea},</if>
            <if test="shippingPic != null">#{shippingPic},</if>
            <if test="partsNoIdCode != null">#{partsNoIdCode},</if>
            <if test="instructionDate != null">#{instructionDate},</if>
            <if test="instructionTime != null">#{instructionTime},</if>
            <if test="instructionNo != null">#{instructionNo},</if>
            <if test="productionHandlingMethod != null">#{productionHandlingMethod},</if>
            <if test="plantCode != null">#{plantCode},</if>
            <if test="shippingLocation != null">#{shippingLocation},</if>
            <if test="shippingPort != null">#{shippingPort},</if>
            <if test="shippingCheckFlag != null">#{shippingCheckFlag},</if>
            <if test="reserve6 != null">#{reserve6},</if>
            <if test="labelIssueAssignment != null">#{labelIssueAssignment},</if>
            <if test="divisionDepot != null">#{divisionDepot},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="demandCode2 != null">#{demandCode2},</if>
            <if test="shippingLocationCode2 != null">#{shippingLocationCode2},</if>
            <if test="movementLeadTime != null">#{movementLeadTime},</if>
            <if test="rehandlingFlag != null">#{rehandlingFlag},</if>
            <if test="noMasterFlag != null">#{noMasterFlag},</if>
            <if test="reserve7 != null">#{reserve7},</if>
            <if test="divisionDepot2 != null">#{divisionDepot2},</if>
        </trim>
    </insert>

    <insert id="insertTblSapKanbanSv" parameterType="TblSapKanbanSv">
        insert into tbl_sap_kanban_sv
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileNo != null">FILE_NO,</if>
            <if test="reserve1 != null">RESERVE1,</if>
            <if test="demandCode != null">DEMAND_CODE,</if>
            <if test="issueNoId != null">ISSUE_NO_ID,</if>
            <if test="deliveryInstructionYy != null">DELIVERY_INSTRUCTION_YY,</if>
            <if test="maker != null">MAKER,</if>
            <if test="depot != null">DEPOT,</if>
            <if test="classification != null">CLASSIFICATION,</if>
            <if test="deliveryLocation != null">DELIVERY_LOCATION,</if>
            <if test="customerPartsNo != null">CUSTOMER_PARTS_NO,</if>
            <if test="currentProcessClass != null">CURRENT_PROCESS_CLASS,</if>
            <if test="partsName != null">PARTS_NAME,</if>
            <if test="deliveryInstructionNumber != null">DELIVERY_INSTRUCTION_NUMBER,</if>
            <if test="supplyLocation != null">SUPPLY_LOCATION,</if>
            <if test="snep != null">SNEP,</if>
            <if test="safetyFlag != null">SAFETY_FLAG,</if>
            <if test="labelCount != null">LABEL_COUNT,</if>
            <if test="managementPic != null">MANAGEMENT_PIC,</if>
            <if test="issueReason != null">ISSUE_REASON,</if>
            <if test="vipFlag != null">VIP_FLAG,</if>
            <if test="packageCode != null">PACKAGE_CODE,</if>
            <if test="internalExternalClass != null">INTERNAL_EXTERNAL_CLASS,</if>
            <if test="deliveryInstructionCount != null">DELIVERY_INSTRUCTION_COUNT,</if>
            <if test="deliveryMethod != null">DELIVERY_METHOD,</if>
            <if test="orderClass != null">ORDER_CLASS,</if>
            <if test="reserve2 != null">RESERVE2,</if>
            <if test="delayPreventionMakerSymbol != null">DELAY_PREVENTION_MAKER_SYMBOL,</if>
            <if test="reserve3 != null">RESERVE3,</if>
            <if test="reserve4 != null">RESERVE4,</if>
            <if test="reserve5 != null">RESERVE5,</if>
            <if test="labelSize != null">LABEL_SIZE,</if>
            <if test="labelSpecification != null">LABEL_SPECIFICATION,</if>
            <if test="orderType != null">ORDER_TYPE,</if>
            <if test="checkSpecification != null">CHECK_SPECIFICATION,</if>
            <if test="location != null">LOCATION,</if>
            <if test="packClass != null">PACK_CLASS,</if>
            <if test="tradeSpecification != null">TRADE_SPECIFICATION,</if>
            <if test="outpackMaterialCode != null">OUTPACK_MATERIAL_CODE,</if>
            <if test="outpackMaterialProcClass != null">OUTPACK_MATERIAL_PROC_CLASS,</if>
            <if test="bindPackCode != null">BIND_PACK_CODE,</if>
            <if test="bindUnit != null">BIND_UNIT,</if>
            <if test="packUnit != null">PACK_UNIT,</if>
            <if test="consistPackClass != null">CONSIST_PACK_CLASS,</if>
            <if test="elementsFinishClass != null">ELEMENTS_FINISH_CLASS,</if>
            <if test="inpackCode1 != null">INPACK_CODE1,</if>
            <if test="inpackProcClass1 != null">INPACK_PROC_CLASS1,</if>
            <if test="inpackNecessaryNumber1 != null">INPACK_NECESSARY_NUMBER1,</if>
            <if test="inpackCode2 != null">INPACK_CODE2,</if>
            <if test="inpackProcClass2 != null">INPACK_PROC_CLASS2,</if>
            <if test="inpackNecessaryNumber2 != null">INPACK_NECESSARY_NUMBER2,</if>
            <if test="inpackCode3 != null">INPACK_CODE3,</if>
            <if test="inpackProcClass3 != null">INPACK_PROC_CLASS3,</if>
            <if test="inpackNecessaryNumber3 != null">INPACK_NECESSARY_NUMBER3,</if>
            <if test="inpackCode4 != null">INPACK_CODE4,</if>
            <if test="inpackProcClass4 != null">INPACK_PROC_CLASS4,</if>
            <if test="inpackNecessaryNumber4 != null">INPACK_NECESSARY_NUMBER4,</if>
            <if test="inpackCode5 != null">INPACK_CODE5,</if>
            <if test="inpackProcClass5 != null">INPACK_PROC_CLASS5,</if>
            <if test="inpackNecessaryNumber5 != null">INPACK_NECESSARY_NUMBER5,</if>
            <if test="inpackCode6 != null">INPACK_CODE6,</if>
            <if test="inpackProcClass6 != null">INPACK_PROC_CLASS6,</if>
            <if test="inpackNecessaryNumber6 != null">INPACK_NECESSARY_NUMBER6,</if>
            <if test="inpackCode7 != null">INPACK_CODE7,</if>
            <if test="inpackProcClass7 != null">INPACK_PROC_CLASS7,</if>
            <if test="inpackNecessaryNumber7 != null">INPACK_NECESSARY_NUMBER7,</if>
            <if test="inpackCode8 != null">INPACK_CODE8,</if>
            <if test="inpackProcClass8 != null">INPACK_PROC_CLASS8,</if>
            <if test="inpackNecessaryNumber8 != null">INPACK_NECESSARY_NUMBER8,</if>
            <if test="inpackCode9 != null">INPACK_CODE9,</if>
            <if test="inpackProcClass9 != null">INPACK_PROC_CLASS9,</if>
            <if test="inpackNecessaryNumber9 != null">INPACK_NECESSARY_NUMBER9,</if>
            <if test="moduleMaterial != null">MODULE_MATERIAL,</if>
            <if test="moduleUnit != null">MODULE_UNIT,</if>
            <if test="moduleClass != null">MODULE_CLASS,</if>
            <if test="originalPartNo != null">ORIGINAL_PART_NO,</if>
            <if test="partManageClass != null">PART_MANAGE_CLASS,</if>
            <if test="newcarProgramCode != null">NEWCAR_PROGRAM_CODE,</if>
            <if test="originalIssueNo != null">ORIGINAL_ISSUE_NO,</if>
            <if test="designChangeNo != null">DESIGN_CHANGE_NO,</if>
            <if test="normalContainerCode != null">NORMAL_CONTAINER_CODE,</if>
            <if test="boForecastDate != null">BO_FORECAST_DATE,</if>
            <if test="boSymbol != null">BO_SYMBOL,</if>
            <if test="trusteeId != null">TRUSTEE_ID,</if>
            <if test="deliveryDocType != null">DELIVERY_DOC_TYPE,</if>
            <if test="packageLabelType != null">PACKAGE_LABEL_TYPE,</if>
            <if test="labelIssueOrg != null">LABEL_ISSUE_ORG,</if>
            <if test="deliveryContainer != null">DELIVERY_CONTAINER,</if>
            <if test="ulContainerCount != null">UL_CONTAINER_COUNT,</if>
            <if test="itemSubno1 != null">ITEM_SUBNO1,</if>
            <if test="itemSubno2 != null">ITEM_SUBNO2,</if>
            <if test="itemSubno3 != null">ITEM_SUBNO3,</if>
            <if test="itemSubno4 != null">ITEM_SUBNO4,</if>
            <if test="itemSubno5 != null">ITEM_SUBNO5,</if>
            <if test="itemSubno6 != null">ITEM_SUBNO6,</if>
            <if test="itemSubno7 != null">ITEM_SUBNO7,</if>
            <if test="itemSubno8 != null">ITEM_SUBNO8,</if>
            <if test="itemSubno9 != null">ITEM_SUBNO9,</if>
            <if test="itemSubno10 != null">ITEM_SUBNO10,</if>
            <if test="itemSubno11 != null">ITEM_SUBNO11,</if>
            <if test="itemSubno12 != null">ITEM_SUBNO12,</if>
            <if test="itemSubno13 != null">ITEM_SUBNO13,</if>
            <if test="itemSubno14 != null">ITEM_SUBNO14,</if>
            <if test="itemSubno15 != null">ITEM_SUBNO15,</if>
            <if test="itemSubno16 != null">ITEM_SUBNO16,</if>
            <if test="itemSubno17 != null">ITEM_SUBNO17,</if>
            <if test="itemSubno18 != null">ITEM_SUBNO18,</if>
            <if test="itemSubno19 != null">ITEM_SUBNO19,</if>
            <if test="itemSubno20 != null">ITEM_SUBNO20,</if>
            <if test="barcodeInfo != null">BARCODE_INFO,</if>
            <if test="freeColumn1 != null">FREE_COLUMN1,</if>
            <if test="freeColumn2 != null">FREE_COLUMN2,</if>
            <if test="freeColumn3 != null">FREE_COLUMN3,</if>
            <if test="freeColumn4 != null">FREE_COLUMN4,</if>
            <if test="yard != null">YARD,</if>
            <if test="enlargeMark1 != null">ENLARGE_MARK1,</if>
            <if test="enlargeMark2 != null">ENLARGE_MARK2,</if>
            <if test="noDiffDeliveryClass != null">NO_DIFF_DELIVERY_CLASS,</if>
            <if test="ulSnp != null">UL_SNP,</if>
            <if test="deliveryTicketNo != null">DELIVERY_TICKET_NO,</if>
            <if test="withdrawalClass != null">WITHDRAWAL_CLASS,</if>
            <if test="partPrice != null">PART_PRICE,</if>
            <if test="prototypeSymbol != null">PROTOTYPE_SYMBOL,</if>
            <if test="resendSymbol != null">RESEND_SYMBOL,</if>
            <if test="orderDate != null">ORDER_DATE,</if>
            <if test="reserve6 != null">RESERVE6,</if>
            <if test="reserve7 != null">RESERVE7,</if>
            <if test="labelTerminalNo != null">LABEL_TERMINAL_NO,</if>
            <if test="listTerminalNo != null">LIST_TERMINAL_NO,</if>
            <if test="makerPic != null">MAKER_PIC,</if>
            <if test="makerSnp != null">MAKER_SNP,</if>
            <if test="resendDepot != null">RESEND_DEPOT,</if>
            <if test="partsNo != null">PARTS_NO,</if>
            <if test="productClass != null">PRODUCT_CLASS,</if>
            <if test="productionMethod != null">PRODUCTION_METHOD,</if>
            <if test="deliveryMethod2 != null">DELIVERY_METHOD2,</if>
            <if test="fromProcess != null">FROM_PROCESS,</if>
            <if test="fromWorkArea != null">FROM_WORK_AREA,</if>
            <if test="shippingPic != null">SHIPPING_PIC,</if>
            <if test="partsNoIdCode != null">PARTS_NO_ID_CODE,</if>
            <if test="instructionDate != null">INSTRUCTION_DATE,</if>
            <if test="instructionTime != null">INSTRUCTION_TIME,</if>
            <if test="instructionNo != null">INSTRUCTION_NO,</if>
            <if test="productionHandlingMethod != null">PRODUCTION_HANDLING_METHOD,</if>
            <if test="plantCode != null">PLANT_CODE,</if>
            <if test="shippingLocation != null">SHIPPING_LOCATION,</if>
            <if test="shippingPort != null">SHIPPING_PORT,</if>
            <if test="reserve8 != null">RESERVE8,</if>
            <if test="labelIssueAssignment != null">LABEL_ISSUE_ASSIGNMENT,</if>
            <if test="divisionDepot != null">DIVISION_DEPOT,</if>
            <if test="customerCode != null">CUSTOMER_CODE,</if>
            <if test="demandCode2 != null">DEMAND_CODE2,</if>
            <if test="shippingLocationCode2 != null">SHIPPING_LOCATION_CODE2,</if>
            <if test="movementLeadTime != null">MOVEMENT_LEAD_TIME,</if>
            <if test="rehandlingFlag != null">REHANDLING_FLAG,</if>
            <if test="noMasterFlag != null">NO_MASTER_FLAG,</if>
            <if test="reserve9 != null">RESERVE9,</if>
            <if test="divisionDepot2 != null">DIVISION_DEPOT2,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileNo != null">#{fileNo},</if>
            <if test="reserve1 != null">#{reserve1},</if>
            <if test="demandCode != null">#{demandCode},</if>
            <if test="issueNoId != null">#{issueNoId},</if>
            <if test="deliveryInstructionYy != null">#{deliveryInstructionYy},</if>
            <if test="maker != null">#{maker},</if>
            <if test="depot != null">#{depot},</if>
            <if test="classification != null">#{classification},</if>
            <if test="deliveryLocation != null">#{deliveryLocation},</if>
            <if test="customerPartsNo != null">#{customerPartsNo},</if>
            <if test="currentProcessClass != null">#{currentProcessClass},</if>
            <if test="partsName != null">#{partsName},</if>
            <if test="deliveryInstructionNumber != null">#{deliveryInstructionNumber},</if>
            <if test="supplyLocation != null">#{supplyLocation},</if>
            <if test="snep != null">#{snep},</if>
            <if test="safetyFlag != null">#{safetyFlag},</if>
            <if test="labelCount != null">#{labelCount},</if>
            <if test="managementPic != null">#{managementPic},</if>
            <if test="issueReason != null">#{issueReason},</if>
            <if test="vipFlag != null">#{vipFlag},</if>
            <if test="packageCode != null">#{packageCode},</if>
            <if test="internalExternalClass != null">#{internalExternalClass},</if>
            <if test="deliveryInstructionCount != null">#{deliveryInstructionCount},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="orderClass != null">#{orderClass},</if>
            <if test="reserve2 != null">#{reserve2},</if>
            <if test="delayPreventionMakerSymbol != null">#{delayPreventionMakerSymbol},</if>
            <if test="reserve3 != null">#{reserve3},</if>
            <if test="reserve4 != null">#{reserve4},</if>
            <if test="reserve5 != null">#{reserve5},</if>
            <if test="labelSize != null">#{labelSize},</if>
            <if test="labelSpecification != null">#{labelSpecification},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="checkSpecification != null">#{checkSpecification},</if>
            <if test="location != null">#{location},</if>
            <if test="packClass != null">#{packClass},</if>
            <if test="tradeSpecification != null">#{tradeSpecification},</if>
            <if test="outpackMaterialCode != null">#{outpackMaterialCode},</if>
            <if test="outpackMaterialProcClass != null">#{outpackMaterialProcClass},</if>
            <if test="bindPackCode != null">#{bindPackCode},</if>
            <if test="bindUnit != null">#{bindUnit},</if>
            <if test="packUnit != null">#{packUnit},</if>
            <if test="consistPackClass != null">#{consistPackClass},</if>
            <if test="elementsFinishClass != null">#{elementsFinishClass},</if>
            <if test="inpackCode1 != null">#{inpackCode1},</if>
            <if test="inpackProcClass1 != null">#{inpackProcClass1},</if>
            <if test="inpackNecessaryNumber1 != null">#{inpackNecessaryNumber1},</if>
            <if test="inpackCode2 != null">#{inpackCode2},</if>
            <if test="inpackProcClass2 != null">#{inpackProcClass2},</if>
            <if test="inpackNecessaryNumber2 != null">#{inpackNecessaryNumber2},</if>
            <if test="inpackCode3 != null">#{inpackCode3},</if>
            <if test="inpackProcClass3 != null">#{inpackProcClass3},</if>
            <if test="inpackNecessaryNumber3 != null">#{inpackNecessaryNumber3},</if>
            <if test="inpackCode4 != null">#{inpackCode4},</if>
            <if test="inpackProcClass4 != null">#{inpackProcClass4},</if>
            <if test="inpackNecessaryNumber4 != null">#{inpackNecessaryNumber4},</if>
            <if test="inpackCode5 != null">#{inpackCode5},</if>
            <if test="inpackProcClass5 != null">#{inpackProcClass5},</if>
            <if test="inpackNecessaryNumber5 != null">#{inpackNecessaryNumber5},</if>
            <if test="inpackCode6 != null">#{inpackCode6},</if>
            <if test="inpackProcClass6 != null">#{inpackProcClass6},</if>
            <if test="inpackNecessaryNumber6 != null">#{inpackNecessaryNumber6},</if>
            <if test="inpackCode7 != null">#{inpackCode7},</if>
            <if test="inpackProcClass7 != null">#{inpackProcClass7},</if>
            <if test="inpackNecessaryNumber7 != null">#{inpackNecessaryNumber7},</if>
            <if test="inpackCode8 != null">#{inpackCode8},</if>
            <if test="inpackProcClass8 != null">#{inpackProcClass8},</if>
            <if test="inpackNecessaryNumber8 != null">#{inpackNecessaryNumber8},</if>
            <if test="inpackCode9 != null">#{inpackCode9},</if>
            <if test="inpackProcClass9 != null">#{inpackProcClass9},</if>
            <if test="inpackNecessaryNumber9 != null">#{inpackNecessaryNumber9},</if>
            <if test="moduleMaterial != null">#{moduleMaterial},</if>
            <if test="moduleUnit != null">#{moduleUnit},</if>
            <if test="moduleClass != null">#{moduleClass},</if>
            <if test="originalPartNo != null">#{originalPartNo},</if>
            <if test="partManageClass != null">#{partManageClass},</if>
            <if test="newcarProgramCode != null">#{newcarProgramCode},</if>
            <if test="originalIssueNo != null">#{originalIssueNo},</if>
            <if test="designChangeNo != null">#{designChangeNo},</if>
            <if test="normalContainerCode != null">#{normalContainerCode},</if>
            <if test="boForecastDate != null">#{boForecastDate},</if>
            <if test="boSymbol != null">#{boSymbol},</if>
            <if test="trusteeId != null">#{trusteeId},</if>
            <if test="deliveryDocType != null">#{deliveryDocType},</if>
            <if test="packageLabelType != null">#{packageLabelType},</if>
            <if test="labelIssueOrg != null">#{labelIssueOrg},</if>
            <if test="deliveryContainer != null">#{deliveryContainer},</if>
            <if test="ulContainerCount != null">#{ulContainerCount},</if>
            <if test="itemSubno1 != null">#{itemSubno1},</if>
            <if test="itemSubno2 != null">#{itemSubno2},</if>
            <if test="itemSubno3 != null">#{itemSubno3},</if>
            <if test="itemSubno4 != null">#{itemSubno4},</if>
            <if test="itemSubno5 != null">#{itemSubno5},</if>
            <if test="itemSubno6 != null">#{itemSubno6},</if>
            <if test="itemSubno7 != null">#{itemSubno7},</if>
            <if test="itemSubno8 != null">#{itemSubno8},</if>
            <if test="itemSubno9 != null">#{itemSubno9},</if>
            <if test="itemSubno10 != null">#{itemSubno10},</if>
            <if test="itemSubno11 != null">#{itemSubno11},</if>
            <if test="itemSubno12 != null">#{itemSubno12},</if>
            <if test="itemSubno13 != null">#{itemSubno13},</if>
            <if test="itemSubno14 != null">#{itemSubno14},</if>
            <if test="itemSubno15 != null">#{itemSubno15},</if>
            <if test="itemSubno16 != null">#{itemSubno16},</if>
            <if test="itemSubno17 != null">#{itemSubno17},</if>
            <if test="itemSubno18 != null">#{itemSubno18},</if>
            <if test="itemSubno19 != null">#{itemSubno19},</if>
            <if test="itemSubno20 != null">#{itemSubno20},</if>
            <if test="barcodeInfo != null">#{barcodeInfo},</if>
            <if test="freeColumn1 != null">#{freeColumn1},</if>
            <if test="freeColumn2 != null">#{freeColumn2},</if>
            <if test="freeColumn3 != null">#{freeColumn3},</if>
            <if test="freeColumn4 != null">#{freeColumn4},</if>
            <if test="yard != null">#{yard},</if>
            <if test="enlargeMark1 != null">#{enlargeMark1},</if>
            <if test="enlargeMark2 != null">#{enlargeMark2},</if>
            <if test="noDiffDeliveryClass != null">#{noDiffDeliveryClass},</if>
            <if test="ulSnp != null">#{ulSnp},</if>
            <if test="deliveryTicketNo != null">#{deliveryTicketNo},</if>
            <if test="withdrawalClass != null">#{withdrawalClass},</if>
            <if test="partPrice != null">#{partPrice},</if>
            <if test="prototypeSymbol != null">#{prototypeSymbol},</if>
            <if test="resendSymbol != null">#{resendSymbol},</if>
            <if test="orderDate != null">#{orderDate},</if>
            <if test="reserve6 != null">#{reserve6},</if>
            <if test="reserve7 != null">#{reserve7},</if>
            <if test="labelTerminalNo != null">#{labelTerminalNo},</if>
            <if test="listTerminalNo != null">#{listTerminalNo},</if>
            <if test="makerPic != null">#{makerPic},</if>
            <if test="makerSnp != null">#{makerSnp},</if>
            <if test="resendDepot != null">#{resendDepot},</if>
            <if test="partsNo != null">#{partsNo},</if>
            <if test="productClass != null">#{productClass},</if>
            <if test="productionMethod != null">#{productionMethod},</if>
            <if test="deliveryMethod2 != null">#{deliveryMethod2},</if>
            <if test="fromProcess != null">#{fromProcess},</if>
            <if test="fromWorkArea != null">#{fromWorkArea},</if>
            <if test="shippingPic != null">#{shippingPic},</if>
            <if test="partsNoIdCode != null">#{partsNoIdCode},</if>
            <if test="instructionDate != null">#{instructionDate},</if>
            <if test="instructionTime != null">#{instructionTime},</if>
            <if test="instructionNo != null">#{instructionNo},</if>
            <if test="productionHandlingMethod != null">#{productionHandlingMethod},</if>
            <if test="plantCode != null">#{plantCode},</if>
            <if test="shippingLocation != null">#{shippingLocation},</if>
            <if test="shippingPort != null">#{shippingPort},</if>
            <if test="reserve8 != null">#{reserve8},</if>
            <if test="labelIssueAssignment != null">#{labelIssueAssignment},</if>
            <if test="divisionDepot != null">#{divisionDepot},</if>
            <if test="customerCode != null">#{customerCode},</if>
            <if test="demandCode2 != null">#{demandCode2},</if>
            <if test="shippingLocationCode2 != null">#{shippingLocationCode2},</if>
            <if test="movementLeadTime != null">#{movementLeadTime},</if>
            <if test="rehandlingFlag != null">#{rehandlingFlag},</if>
            <if test="noMasterFlag != null">#{noMasterFlag},</if>
            <if test="reserve9 != null">#{reserve9},</if>
            <if test="divisionDepot2 != null">#{divisionDepot2},</if>
        </trim>
    </insert>

    <!-- 批量新增看板KD -->
    <insert id="batchInsertTblSapKanbanKd" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_kanban_kd (
            file_no, reserve1, demand_code, issue_no, delivery_instruction_date, delivery_instruction_time,
            maker, depot, classification, delivery_location, unloading_unit, customer_parts_no,
            current_process_class, parts_name, delivery_instruction_number, supply_location, snep,
            safety_flag, label_count, management_pic, issue_reason, vip_flag, package_code,
            internal_external_class, delivery_instruction_count, delivery_method, order_class,
            label_id, delay_prevention_maker_symbol, shipto_class, shipto_code, label_output,
            reserve2, label_no_change_mark, self_procurement_line, label_specification,
            self_procurement_kd, reserve3, kd_elements, transit_code, transit_delivery_date,
            parts_id, delivery_ticket_no, withdrawal_class, part_price, prototype_symbol,
            resend_symbol, order_date, label_size, reserve4, reserve5, maker_pic, maker_snp,
            resend_depot, division_depot2, update_time,
            label_terminal_no, list_terminal_no, parts_no, product_class, production_method,
            delivery_method2, from_process, from_work_area, shipping_pic, parts_no_id_code,
            instruction_date, instruction_time, instruction_no, production_handling_method,
            plant_code, shipping_location, shipping_port, shipping_check_flag, reserve6,
            label_issue_assignment, division_depot, customer_code, demand_code2,
            shipping_location_code2, movement_lead_time, rehandling_flag, no_master_flag,
            reserve7, supp_code
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.fileNo}, #{item.reserve1}, #{item.demandCode}, #{item.issueNo}, 
            #{item.deliveryInstructionDate}, #{item.deliveryInstructionTime}, #{item.maker}, 
            #{item.depot}, #{item.classification}, #{item.deliveryLocation}, #{item.unloadingUnit}, 
            #{item.customerPartsNo}, #{item.currentProcessClass}, #{item.partsName}, 
            #{item.deliveryInstructionNumber}, #{item.supplyLocation}, #{item.snep}, 
            #{item.safetyFlag}, #{item.labelCount}, #{item.managementPic}, #{item.issueReason}, 
            #{item.vipFlag}, #{item.packageCode}, #{item.internalExternalClass}, 
            #{item.deliveryInstructionCount}, #{item.deliveryMethod}, #{item.orderClass}, 
            #{item.labelId}, #{item.delayPreventionMakerSymbol}, #{item.shiptoClass}, 
            #{item.shiptoCode}, #{item.labelOutput}, #{item.reserve2}, #{item.labelNoChangeMark}, 
            #{item.selfProcurementLine}, #{item.labelSpecification}, #{item.selfProcurementKd}, 
            #{item.reserve3}, #{item.kdElements}, #{item.transitCode}, #{item.transitDeliveryDate}, 
            #{item.partsId}, #{item.deliveryTicketNo}, #{item.withdrawalClass}, #{item.partPrice}, 
            #{item.prototypeSymbol}, #{item.resendSymbol}, #{item.orderDate}, #{item.labelSize}, 
            #{item.reserve4}, #{item.reserve5}, #{item.makerPic}, #{item.makerSnp}, 
            #{item.resendDepot}, #{item.divisionDepot2}, sysdate(),
            #{item.labelTerminalNo}, #{item.listTerminalNo}, #{item.partsNo}, #{item.productClass},
            #{item.productionMethod}, #{item.deliveryMethod2}, #{item.fromProcess}, #{item.fromWorkArea},
            #{item.shippingPic}, #{item.partsNoIdCode}, #{item.instructionDate}, #{item.instructionTime},
            #{item.instructionNo}, #{item.productionHandlingMethod}, #{item.plantCode}, #{item.shippingLocation},
            #{item.shippingPort}, #{item.shippingCheckFlag}, #{item.reserve6}, #{item.labelIssueAssignment},
            #{item.divisionDepot}, #{item.customerCode}, #{item.demandCode2}, #{item.shippingLocationCode2},
            #{item.movementLeadTime}, #{item.rehandlingFlag}, #{item.noMasterFlag}, #{item.reserve7}, left(#{item.fromWorkArea}, 5)
            )
        </foreach>
    </insert>

    <!-- 批量新增看板LINE -->
    <insert id="batchInsertTblSapKanbanLine" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_kanban_line (
            file_no, reserve1, demand_code, issue_no, delivery_instruction_date, delivery_instruction_time,
            maker, depot, classification, delivery_location, unloading_unit, customer_parts_no,
            current_process_class, parts_name, delivery_instruction_number, supply_location, snep,
            safety_flag, label_count, management_pic, issue_reason, vip_flag, package_code,
            internal_external_class, delivery_instruction_count, delivery_method, order_class,
            label_id, delay_prevention_maker_symbol, shipto_class, shipto_code, label_output,
            reserve2, label_no_change_mark, self_procurement_line, label_specification,
            self_procurement_kd, reserve3, kd_elements, transit_code, transit_delivery_date,
            parts_id, delivery_ticket_no, withdrawal_class, part_price, prototype_symbol,
            resend_symbol, order_date, label_size, reserve4, reserve5, maker_pic, maker_snp,
            resend_depot, division_depot2, update_time,
            label_terminal_no, list_terminal_no, parts_no, product_class, production_method,
            delivery_method2, from_process, from_work_area, shipping_pic, parts_no_id_code,
            instruction_date, instruction_time, instruction_no, production_handling_method,
            plant_code, shipping_location, shipping_port, shipping_check_flag, reserve6,
            label_issue_assignment, division_depot, customer_code, demand_code2,
            shipping_location_code2, movement_lead_time, rehandling_flag, no_master_flag,
            reserve7, supp_code
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.fileNo}, #{item.reserve1}, #{item.demandCode}, #{item.issueNo}, 
            #{item.deliveryInstructionDate}, #{item.deliveryInstructionTime}, #{item.maker}, 
            #{item.depot}, #{item.classification}, #{item.deliveryLocation}, #{item.unloadingUnit}, 
            #{item.customerPartsNo}, #{item.currentProcessClass}, #{item.partsName}, 
            #{item.deliveryInstructionNumber}, #{item.supplyLocation}, #{item.snep}, 
            #{item.safetyFlag}, #{item.labelCount}, #{item.managementPic}, #{item.issueReason}, 
            #{item.vipFlag}, #{item.packageCode}, #{item.internalExternalClass}, 
            #{item.deliveryInstructionCount}, #{item.deliveryMethod}, #{item.orderClass}, 
            #{item.labelId}, #{item.delayPreventionMakerSymbol}, #{item.shiptoClass}, 
            #{item.shiptoCode}, #{item.labelOutput}, #{item.reserve2}, #{item.labelNoChangeMark}, 
            #{item.selfProcurementLine}, #{item.labelSpecification}, #{item.selfProcurementKd}, 
            #{item.reserve3}, #{item.kdElements}, #{item.transitCode}, #{item.transitDeliveryDate}, 
            #{item.partsId}, #{item.deliveryTicketNo}, #{item.withdrawalClass}, #{item.partPrice}, 
            #{item.prototypeSymbol}, #{item.resendSymbol}, #{item.orderDate}, #{item.labelSize}, 
            #{item.reserve4}, #{item.reserve5}, #{item.makerPic}, #{item.makerSnp}, 
            #{item.resendDepot}, #{item.divisionDepot2}, sysdate(),
            #{item.labelTerminalNo}, #{item.listTerminalNo}, #{item.partsNo}, #{item.productClass},
            #{item.productionMethod}, #{item.deliveryMethod2}, #{item.fromProcess}, #{item.fromWorkArea},
            #{item.shippingPic}, #{item.partsNoIdCode}, #{item.instructionDate}, #{item.instructionTime},
            #{item.instructionNo}, #{item.productionHandlingMethod}, #{item.plantCode}, #{item.shippingLocation},
            #{item.shippingPort}, #{item.shippingCheckFlag}, #{item.reserve6}, #{item.labelIssueAssignment},
            #{item.divisionDepot}, #{item.customerCode}, #{item.demandCode2}, #{item.shippingLocationCode2},
            #{item.movementLeadTime}, #{item.rehandlingFlag}, #{item.noMasterFlag}, #{item.reserve7}, left(#{item.fromWorkArea}, 5)
            )
        </foreach>
    </insert>

    <!-- 批量新增看板SV -->
    <insert id="batchInsertTblSapKanbanSv" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_kanban_sv (
            file_no, reserve1, demand_code, issue_no_id, delivery_instruction_yy, maker, depot,
            classification, delivery_location, customer_parts_no, current_process_class, parts_name,
            delivery_instruction_number, supply_location, snep, safety_flag, label_count,
            management_pic, issue_reason, vip_flag, package_code, internal_external_class,
            delivery_instruction_count, delivery_method, order_class, reserve2,
            delay_prevention_maker_symbol, reserve3, reserve4, reserve5, label_size,
            label_specification, order_type, check_specification, location, pack_class,
            trade_specification, outpack_material_code, outpack_material_proc_class,
            bind_pack_code, bind_unit, pack_unit, consist_pack_class, elements_finish_class,
            inpack_code1, inpack_proc_class1, inpack_necessary_number1, inpack_code2,
            inpack_proc_class2, inpack_necessary_number2, inpack_code3, inpack_proc_class3,
            inpack_necessary_number3, inpack_code4, inpack_proc_class4, inpack_necessary_number4,
            inpack_code5, inpack_proc_class5, inpack_necessary_number5, inpack_code6,
            inpack_proc_class6, inpack_necessary_number6, inpack_code7, inpack_proc_class7,
            inpack_necessary_number7, inpack_code8, inpack_proc_class8, inpack_necessary_number8,
            inpack_code9, inpack_proc_class9, inpack_necessary_number9, module_material,
            module_unit, module_class, original_part_no, part_manage_class, newcar_program_code,
            original_issue_no, design_change_no, normal_container_code, bo_forecast_date,
            bo_symbol, trustee_id, delivery_doc_type, package_label_type, label_issue_org,
            delivery_container, ul_container_count, item_subno1, item_subno2, item_subno3,
            item_subno4, item_subno5, item_subno6, item_subno7, item_subno8, item_subno9,
            item_subno10, item_subno11, item_subno12, item_subno13, item_subno14, item_subno15,
            item_subno16, item_subno17, item_subno18, item_subno19, item_subno20, barcode_info,
            free_column1, free_column2, free_column3, free_column4, yard, enlarge_mark1,
            enlarge_mark2, no_diff_delivery_class, ul_snp, delivery_ticket_no, withdrawal_class,
            part_price, prototype_symbol, resend_symbol, order_date, reserve6, reserve7,
            label_terminal_no, list_terminal_no, maker_pic, maker_snp, resend_depot, parts_no,
            product_class, production_method, delivery_method2, from_process, from_work_area,
            shipping_pic, parts_no_id_code, instruction_date, instruction_time, instruction_no,
            production_handling_method, plant_code, shipping_location, shipping_port, reserve8,
            label_issue_assignment, division_depot, customer_code, demand_code2,
            shipping_location_code2, movement_lead_time, rehandling_flag, no_master_flag,
            reserve9, division_depot2, update_time, supp_code
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.fileNo}, #{item.reserve1}, #{item.demandCode}, #{item.issueNoId},
            #{item.deliveryInstructionYy}, #{item.maker}, #{item.depot}, #{item.classification},
            #{item.deliveryLocation}, #{item.customerPartsNo}, #{item.currentProcessClass},
            #{item.partsName}, #{item.deliveryInstructionNumber}, #{item.supplyLocation},
            #{item.snep}, #{item.safetyFlag}, #{item.labelCount}, #{item.managementPic},
            #{item.issueReason}, #{item.vipFlag}, #{item.packageCode},
            #{item.internalExternalClass}, #{item.deliveryInstructionCount},
            #{item.deliveryMethod}, #{item.orderClass}, #{item.reserve2},
            #{item.delayPreventionMakerSymbol}, #{item.reserve3}, #{item.reserve4},
            #{item.reserve5}, #{item.labelSize}, #{item.labelSpecification}, #{item.orderType},
            #{item.checkSpecification}, #{item.location}, #{item.packClass},
            #{item.tradeSpecification}, #{item.outpackMaterialCode},
            #{item.outpackMaterialProcClass}, #{item.bindPackCode}, #{item.bindUnit},
            #{item.packUnit}, #{item.consistPackClass}, #{item.elementsFinishClass},
            #{item.inpackCode1}, #{item.inpackProcClass1}, #{item.inpackNecessaryNumber1},
            #{item.inpackCode2}, #{item.inpackProcClass2}, #{item.inpackNecessaryNumber2},
            #{item.inpackCode3}, #{item.inpackProcClass3}, #{item.inpackNecessaryNumber3},
            #{item.inpackCode4}, #{item.inpackProcClass4}, #{item.inpackNecessaryNumber4},
            #{item.inpackCode5}, #{item.inpackProcClass5}, #{item.inpackNecessaryNumber5},
            #{item.inpackCode6}, #{item.inpackProcClass6}, #{item.inpackNecessaryNumber6},
            #{item.inpackCode7}, #{item.inpackProcClass7}, #{item.inpackNecessaryNumber7},
            #{item.inpackCode8}, #{item.inpackProcClass8}, #{item.inpackNecessaryNumber8},
            #{item.inpackCode9}, #{item.inpackProcClass9}, #{item.inpackNecessaryNumber9},
            #{item.moduleMaterial}, #{item.moduleUnit}, #{item.moduleClass},
            #{item.originalPartNo}, #{item.partManageClass}, #{item.newcarProgramCode},
            #{item.originalIssueNo}, #{item.designChangeNo}, #{item.normalContainerCode},
            #{item.boForecastDate}, #{item.boSymbol}, #{item.trusteeId},
            #{item.deliveryDocType}, #{item.packageLabelType}, #{item.labelIssueOrg},
            #{item.deliveryContainer}, #{item.ulContainerCount}, #{item.itemSubno1},
            #{item.itemSubno2}, #{item.itemSubno3}, #{item.itemSubno4}, #{item.itemSubno5},
            #{item.itemSubno6}, #{item.itemSubno7}, #{item.itemSubno8}, #{item.itemSubno9},
            #{item.itemSubno10}, #{item.itemSubno11}, #{item.itemSubno12}, #{item.itemSubno13},
            #{item.itemSubno14}, #{item.itemSubno15}, #{item.itemSubno16}, #{item.itemSubno17},
            #{item.itemSubno18}, #{item.itemSubno19}, #{item.itemSubno20}, #{item.barcodeInfo},
            #{item.freeColumn1}, #{item.freeColumn2}, #{item.freeColumn3}, #{item.freeColumn4},
            #{item.yard}, #{item.enlargeMark1}, #{item.enlargeMark2},
            #{item.noDiffDeliveryClass}, #{item.ulSnp}, #{item.deliveryTicketNo},
            #{item.withdrawalClass}, #{item.partPrice}, #{item.prototypeSymbol},
            #{item.resendSymbol}, #{item.orderDate}, #{item.reserve6}, #{item.reserve7},
            #{item.labelTerminalNo}, #{item.listTerminalNo}, #{item.makerPic}, #{item.makerSnp},
            #{item.resendDepot}, #{item.partsNo}, #{item.productClass}, #{item.productionMethod},
            #{item.deliveryMethod2}, #{item.fromProcess}, #{item.fromWorkArea},
            #{item.shippingPic}, #{item.partsNoIdCode}, #{item.instructionDate}, #{item.instructionTime},
            #{item.instructionNo}, #{item.productionHandlingMethod}, #{item.plantCode}, #{item.shippingLocation},
            #{item.shippingPort}, #{item.reserve8}, #{item.labelIssueAssignment}, #{item.divisionDepot},
            #{item.customerCode}, #{item.demandCode2}, #{item.shippingLocationCode2}, #{item.movementLeadTime},
            #{item.rehandlingFlag}, #{item.noMasterFlag}, #{item.reserve9}, #{item.divisionDepot2}, sysdate(), left(#{item.fromWorkArea}, 5)
            )
        </foreach>
    </insert>

    <!-- 在mapper中添加以下查询 -->
    <select id="selectTblSapKanbanKdByBizKey" resultMap="TblSapKanbanKdResult">
        <include refid="selectTblSapKanbanKdVo"/>
        where demand_code = #{demandCode} 
        and issue_no = #{issueNo}
        and customer_parts_no = #{customerPartsNo}
    </select>

    <select id="selectTblSapKanbanLineByBizKey" resultMap="TblSapKanbanKdResult">
        select * from tbl_sap_kanban_line
        where demand_code = #{demandCode}
        and issue_no = #{issueNo} 
        and customer_parts_no = #{customerPartsNo}
    </select>

    <select id="selectTblSapKanbanSvByBizKey" resultMap="TblSapKanbanSvResult">
        select * from tbl_sap_kanban_sv
        where demand_code = #{demandCode}
        and ISSUE_NO_ID = #{issueNoId}
        and customer_parts_no = #{customerPartsNo} 
    </select>

    <!-- 更新看板LINE -->
    <update id="updateTblSapKanbanLine" parameterType="TblSapKanbanKd">
        update tbl_sap_kanban_line
        set
            FILE_NO = #{fileNo},
            RESERVE1 = #{reserve1},
            DEMAND_CODE = #{demandCode},
            ISSUE_NO = #{issueNo},
            DELIVERY_INSTRUCTION_DATE = #{deliveryInstructionDate},
            DELIVERY_INSTRUCTION_TIME = #{deliveryInstructionTime},
            MAKER = #{maker},
            DEPOT = #{depot},
            CLASSIFICATION = #{classification},
            DELIVERY_LOCATION = #{deliveryLocation},
            UNLOADING_UNIT = #{unloadingUnit},
            CUSTOMER_PARTS_NO = #{customerPartsNo},
            CURRENT_PROCESS_CLASS = #{currentProcessClass},
            PARTS_NAME = #{partsName},
            DELIVERY_INSTRUCTION_NUMBER = #{deliveryInstructionNumber},
            SUPPLY_LOCATION = #{supplyLocation},
            SNEP = #{snep},
            SAFETY_FLAG = #{safetyFlag},
            LABEL_COUNT = #{labelCount},
            MANAGEMENT_PIC = #{managementPic},
            ISSUE_REASON = #{issueReason},
            VIP_FLAG = #{vipFlag},
            PACKAGE_CODE = #{packageCode},
            INTERNAL_EXTERNAL_CLASS = #{internalExternalClass},
            DELIVERY_INSTRUCTION_COUNT = #{deliveryInstructionCount},
            DELIVERY_METHOD = #{deliveryMethod},
            ORDER_CLASS = #{orderClass},
            LABEL_ID = #{labelId},
            DELAY_PREVENTION_MAKER_SYMBOL = #{delayPreventionMakerSymbol},
            SHIPTO_CLASS = #{shiptoClass},
            SHIPTO_CODE = #{shiptoCode},
            LABEL_OUTPUT = #{labelOutput},
            RESERVE2 = #{reserve2},
            LABEL_NO_CHANGE_MARK = #{labelNoChangeMark},
            SELF_PROCUREMENT_LINE = #{selfProcurementLine},
            LABEL_SPECIFICATION = #{labelSpecification},
            SELF_PROCUREMENT_KD = #{selfProcurementKd},
            RESERVE3 = #{reserve3},
            KD_ELEMENTS = #{kdElements},
            TRANSIT_CODE = #{transitCode},
            TRANSIT_DELIVERY_DATE = #{transitDeliveryDate},
            PARTS_ID = #{partsId},
            DELIVERY_TICKET_NO = #{deliveryTicketNo},
            WITHDRAWAL_CLASS = #{withdrawalClass},
            PART_PRICE = #{partPrice},
            PROTOTYPE_SYMBOL = #{prototypeSymbol},
            RESEND_SYMBOL = #{resendSymbol},
            ORDER_DATE = #{orderDate},
            LABEL_SIZE = #{labelSize},
            RESERVE4 = #{reserve4},
            RESERVE5 = #{reserve5},
            LABEL_TERMINAL_NO = #{labelTerminalNo},
            LIST_TERMINAL_NO = #{listTerminalNo},
            MAKER_PIC = #{makerPic},
            MAKER_SNP = #{makerSnp},
            RESEND_DEPOT = #{resendDepot},
            PARTS_NO = #{partsNo},
            PRODUCT_CLASS = #{productClass},
            PRODUCTION_METHOD = #{productionMethod},
            DELIVERY_METHOD2 = #{deliveryMethod2},
            FROM_PROCESS = #{fromProcess},
            FROM_WORK_AREA = #{fromWorkArea},
            SHIPPING_PIC = #{shippingPic},
            PARTS_NO_ID_CODE = #{partsNoIdCode},
            INSTRUCTION_DATE = #{instructionDate},
            INSTRUCTION_TIME = #{instructionTime},
            INSTRUCTION_NO = #{instructionNo},
            PRODUCTION_HANDLING_METHOD = #{productionHandlingMethod},
            PLANT_CODE = #{plantCode},
            SHIPPING_LOCATION = #{shippingLocation},
            SHIPPING_PORT = #{shippingPort},
            SHIPPING_CHECK_FLAG = #{shippingCheckFlag},
            RESERVE6 = #{reserve6},
            LABEL_ISSUE_ASSIGNMENT = #{labelIssueAssignment},
            DIVISION_DEPOT = #{divisionDepot},
            CUSTOMER_CODE = #{customerCode},
            DEMAND_CODE2 = #{demandCode2},
            SHIPPING_LOCATION_CODE2 = #{shippingLocationCode2},
            MOVEMENT_LEAD_TIME = #{movementLeadTime},
            REHANDLING_FLAG = #{rehandlingFlag},
            NO_MASTER_FLAG = #{noMasterFlag},
            RESERVE7 = #{reserve7},
            DIVISION_DEPOT2 = #{divisionDepot2},
            UPDATE_TIME = sysdate(),
            SUPP_CODE = left(#{fromWorkArea}, 5)
        where id = #{id}
    </update>

    <!-- 更新看板SV -->
    <update id="updateTblSapKanbanSv" parameterType="TblSapKanbanSv">
        update tbl_sap_kanban_sv
        set
            FILE_NO = #{fileNo},
            RESERVE1 = #{reserve1},
            DEMAND_CODE = #{demandCode},
            ISSUE_NO_ID = #{issueNoId},
            DELIVERY_INSTRUCTION_YY = #{deliveryInstructionYy},
            MAKER = #{maker},
            DEPOT = #{depot},
            CLASSIFICATION = #{classification},
            DELIVERY_LOCATION = #{deliveryLocation},
            CUSTOMER_PARTS_NO = #{customerPartsNo},
            CURRENT_PROCESS_CLASS = #{currentProcessClass},
            PARTS_NAME = #{partsName},
            DELIVERY_INSTRUCTION_NUMBER = #{deliveryInstructionNumber},
            SUPPLY_LOCATION = #{supplyLocation},
            SNEP = #{snep},
            SAFETY_FLAG = #{safetyFlag},
            LABEL_COUNT = #{labelCount},
            MANAGEMENT_PIC = #{managementPic},
            ISSUE_REASON = #{issueReason},
            VIP_FLAG = #{vipFlag},
            PACKAGE_CODE = #{packageCode},
            INTERNAL_EXTERNAL_CLASS = #{internalExternalClass},
            DELIVERY_INSTRUCTION_COUNT = #{deliveryInstructionCount},
            DELIVERY_METHOD = #{deliveryMethod},
            ORDER_CLASS = #{orderClass},
            RESERVE2 = #{reserve2},
            DELAY_PREVENTION_MAKER_SYMBOL = #{delayPreventionMakerSymbol},
            RESERVE3 = #{reserve3},
            RESERVE4 = #{reserve4},
            RESERVE5 = #{reserve5},
            LABEL_SIZE = #{labelSize},
            LABEL_SPECIFICATION = #{labelSpecification},
            ORDER_TYPE = #{orderType},
            CHECK_SPECIFICATION = #{checkSpecification},
            LOCATION = #{location},
            PACK_CLASS = #{packClass},
            TRADE_SPECIFICATION = #{tradeSpecification},
            OUTPACK_MATERIAL_CODE = #{outpackMaterialCode},
            OUTPACK_MATERIAL_PROC_CLASS = #{outpackMaterialProcClass},
            BIND_PACK_CODE = #{bindPackCode},
            BIND_UNIT = #{bindUnit},
            PACK_UNIT = #{packUnit},
            CONSIST_PACK_CLASS = #{consistPackClass},
            ELEMENTS_FINISH_CLASS = #{elementsFinishClass},
            INPACK_CODE1 = #{inpackCode1},
            INPACK_PROC_CLASS1 = #{inpackProcClass1},
            INPACK_NECESSARY_NUMBER1 = #{inpackNecessaryNumber1},
            INPACK_CODE2 = #{inpackCode2},
            INPACK_PROC_CLASS2 = #{inpackProcClass2},
            INPACK_NECESSARY_NUMBER2 = #{inpackNecessaryNumber2},
            INPACK_CODE3 = #{inpackCode3},
            INPACK_PROC_CLASS3 = #{inpackProcClass3},
            INPACK_NECESSARY_NUMBER3 = #{inpackNecessaryNumber3},
            INPACK_CODE4 = #{inpackCode4},
            INPACK_PROC_CLASS4 = #{inpackProcClass4},
            INPACK_NECESSARY_NUMBER4 = #{inpackNecessaryNumber4},
            INPACK_CODE5 = #{inpackCode5},
            INPACK_PROC_CLASS5 = #{inpackProcClass5},
            INPACK_NECESSARY_NUMBER5 = #{inpackNecessaryNumber5},
            INPACK_CODE6 = #{inpackCode6},
            INPACK_PROC_CLASS6 = #{inpackProcClass6},
            INPACK_NECESSARY_NUMBER6 = #{inpackNecessaryNumber6},
            INPACK_CODE7 = #{inpackCode7},
            INPACK_PROC_CLASS7 = #{inpackProcClass7},
            INPACK_NECESSARY_NUMBER7 = #{inpackNecessaryNumber7},
            INPACK_CODE8 = #{inpackCode8},
            INPACK_PROC_CLASS8 = #{inpackProcClass8},
            INPACK_NECESSARY_NUMBER8 = #{inpackNecessaryNumber8},
            INPACK_CODE9 = #{inpackCode9},
            INPACK_PROC_CLASS9 = #{inpackProcClass9},
            INPACK_NECESSARY_NUMBER9 = #{inpackNecessaryNumber9},
            MODULE_MATERIAL = #{moduleMaterial},
            MODULE_UNIT = #{moduleUnit},
            MODULE_CLASS = #{moduleClass},
            ORIGINAL_PART_NO = #{originalPartNo},
            PART_MANAGE_CLASS = #{partManageClass},
            NEWCAR_PROGRAM_CODE = #{newcarProgramCode},
            ORIGINAL_ISSUE_NO = #{originalIssueNo},
            DESIGN_CHANGE_NO = #{designChangeNo},
            NORMAL_CONTAINER_CODE = #{normalContainerCode},
            BO_FORECAST_DATE = #{boForecastDate},
            BO_SYMBOL = #{boSymbol},
            TRUSTEE_ID = #{trusteeId},
            DELIVERY_DOC_TYPE = #{deliveryDocType},
            PACKAGE_LABEL_TYPE = #{packageLabelType},
            LABEL_ISSUE_ORG = #{labelIssueOrg},
            DELIVERY_CONTAINER = #{deliveryContainer},
            UL_CONTAINER_COUNT = #{ulContainerCount},
            ITEM_SUBNO1 = #{itemSubno1},
            ITEM_SUBNO2 = #{itemSubno2},
            ITEM_SUBNO3 = #{itemSubno3},
            ITEM_SUBNO4 = #{itemSubno4},
            ITEM_SUBNO5 = #{itemSubno5},
            ITEM_SUBNO6 = #{itemSubno6},
            ITEM_SUBNO7 = #{itemSubno7},
            ITEM_SUBNO8 = #{itemSubno8},
            ITEM_SUBNO9 = #{itemSubno9},
            ITEM_SUBNO10 = #{itemSubno10},
            ITEM_SUBNO11 = #{itemSubno11},
            ITEM_SUBNO12 = #{itemSubno12},
            ITEM_SUBNO13 = #{itemSubno13},
            ITEM_SUBNO14 = #{itemSubno14},
            ITEM_SUBNO15 = #{itemSubno15},
            ITEM_SUBNO16 = #{itemSubno16},
            ITEM_SUBNO17 = #{itemSubno17},
            ITEM_SUBNO18 = #{itemSubno18},
            ITEM_SUBNO19 = #{itemSubno19},
            ITEM_SUBNO20 = #{itemSubno20},
            BARCODE_INFO = #{barcodeInfo},
            FREE_COLUMN1 = #{freeColumn1},
            FREE_COLUMN2 = #{freeColumn2},
            FREE_COLUMN3 = #{freeColumn3},
            FREE_COLUMN4 = #{freeColumn4},
            YARD = #{yard},
            ENLARGE_MARK1 = #{enlargeMark1},
            ENLARGE_MARK2 = #{enlargeMark2},
            NO_DIFF_DELIVERY_CLASS = #{noDiffDeliveryClass},
            UL_SNP = #{ulSnp},
            DELIVERY_TICKET_NO = #{deliveryTicketNo},
            WITHDRAWAL_CLASS = #{withdrawalClass},
            PART_PRICE = #{partPrice},
            PROTOTYPE_SYMBOL = #{prototypeSymbol},
            RESEND_SYMBOL = #{resendSymbol},
            ORDER_DATE = #{orderDate},
            RESERVE6 = #{reserve6},
            RESERVE7 = #{reserve7},
            LABEL_TERMINAL_NO = #{labelTerminalNo},
            LIST_TERMINAL_NO = #{listTerminalNo},
            MAKER_PIC = #{makerPic},
            MAKER_SNP = #{makerSnp},
            RESEND_DEPOT = #{resendDepot},
            PARTS_NO = #{partsNo},
            PRODUCT_CLASS = #{productClass},
            PRODUCTION_METHOD = #{productionMethod},
            DELIVERY_METHOD2 = #{deliveryMethod2},
            FROM_PROCESS = #{fromProcess},
            FROM_WORK_AREA = #{fromWorkArea},
            SHIPPING_PIC = #{shippingPic},
            PARTS_NO_ID_CODE = #{partsNoIdCode},
            INSTRUCTION_DATE = #{instructionDate},
            INSTRUCTION_TIME = #{instructionTime},
            INSTRUCTION_NO = #{instructionNo},
            PRODUCTION_HANDLING_METHOD = #{productionHandlingMethod},
            PLANT_CODE = #{plantCode},
            SHIPPING_LOCATION = #{shippingLocation},
            SHIPPING_PORT = #{shippingPort},
            RESERVE8 = #{reserve8},
            LABEL_ISSUE_ASSIGNMENT = #{labelIssueAssignment},
            DIVISION_DEPOT = #{divisionDepot},
            CUSTOMER_CODE = #{customerCode},
            DEMAND_CODE2 = #{demandCode2},
            SHIPPING_LOCATION_CODE2 = #{shippingLocationCode2},
            MOVEMENT_LEAD_TIME = #{movementLeadTime},
            REHANDLING_FLAG = #{rehandlingFlag},
            NO_MASTER_FLAG = #{noMasterFlag},
            RESERVE9 = #{reserve9},
            DIVISION_DEPOT2 = #{divisionDepot2},
            UPDATE_TIME = sysdate(),
            SUPP_CODE = left(#{fromWorkArea}, 5)
        where id = #{id}
    </update>

    <select id="selectTblSapKanbanLineList"  parameterType="TblSapKanbanKd" resultMap="TblSapKanbanKdResult">
        <include refid="selectTblSapKanbanLineVo"/>
        <where>
            <if test="fileNo != null and fileNo != ''"> and FILE_NO = #{fileNo}</if>
            <if test="reserve1 != null and reserve1 != ''"> and RESERVE1 = #{reserve1}</if>
            <if test="demandCode != null and demandCode != ''"> and DEMAND_CODE = #{demandCode}</if>
            <if test="issueNo != null and issueNo != ''"> and ISSUE_NO = #{issueNo}</if>
            <if test="deliveryInstructionDate != null and deliveryInstructionDate != ''"> and DELIVERY_INSTRUCTION_DATE = #{deliveryInstructionDate}</if>
            <if test="deliveryInstructionTime != null and deliveryInstructionTime != ''"> and DELIVERY_INSTRUCTION_TIME = #{deliveryInstructionTime}</if>
            <if test="maker != null and maker != ''"> and MAKER = #{maker}</if>
            <if test="depot != null and depot != ''"> and DEPOT = #{depot}</if>
            <if test="classification != null and classification != ''"> and CLASSIFICATION = #{classification}</if>
            <if test="deliveryLocation != null and deliveryLocation != ''"> and DELIVERY_LOCATION = #{deliveryLocation}</if>
            <if test="unloadingUnit != null and unloadingUnit != ''"> and UNLOADING_UNIT = #{unloadingUnit}</if>
            <if test="customerPartsNo != null and customerPartsNo != ''"> and CUSTOMER_PARTS_NO = #{customerPartsNo}</if>
            <if test="currentProcessClass != null and currentProcessClass != ''"> and CURRENT_PROCESS_CLASS = #{currentProcessClass}</if>
            <if test="partsName != null and partsName != ''"> and PARTS_NAME = #{partsName}</if>
            <if test="deliveryInstructionNumber != null and deliveryInstructionNumber != ''"> and DELIVERY_INSTRUCTION_NUMBER = #{deliveryInstructionNumber}</if>
            <if test="supplyLocation != null and supplyLocation != ''"> and SUPPLY_LOCATION = #{supplyLocation}</if>
            <if test="snep != null and snep != ''"> and SNEP = #{snep}</if>
            <if test="safetyFlag != null and safetyFlag != ''"> and SAFETY_FLAG = #{safetyFlag}</if>
            <if test="labelCount != null and labelCount != ''"> and LABEL_COUNT = #{labelCount}</if>
            <if test="managementPic != null and managementPic != ''"> and MANAGEMENT_PIC = #{managementPic}</if>
            <if test="issueReason != null and issueReason != ''"> and ISSUE_REASON = #{issueReason}</if>
            <if test="vipFlag != null and vipFlag != ''"> and VIP_FLAG = #{vipFlag}</if>
            <if test="packageCode != null and packageCode != ''"> and PACKAGE_CODE = #{packageCode}</if>
            <if test="internalExternalClass != null and internalExternalClass != ''"> and INTERNAL_EXTERNAL_CLASS = #{internalExternalClass}</if>
            <if test="deliveryInstructionCount != null and deliveryInstructionCount != ''"> and DELIVERY_INSTRUCTION_COUNT = #{deliveryInstructionCount}</if>
            <if test="deliveryMethod != null and deliveryMethod != ''"> and DELIVERY_METHOD = #{deliveryMethod}</if>
            <if test="orderClass != null and orderClass != ''"> and ORDER_CLASS = #{orderClass}</if>
            <if test="labelId != null and labelId != ''"> and LABEL_ID = #{labelId}</if>
            <if test="delayPreventionMakerSymbol != null and delayPreventionMakerSymbol != ''"> and DELAY_PREVENTION_MAKER_SYMBOL = #{delayPreventionMakerSymbol}</if>
            <if test="shiptoClass != null and shiptoClass != ''"> and SHIPTO_CLASS = #{shiptoClass}</if>
            <if test="shiptoCode != null and shiptoCode != ''"> and SHIPTO_CODE = #{shiptoCode}</if>
            <if test="labelOutput != null and labelOutput != ''"> and LABEL_OUTPUT = #{labelOutput}</if>
            <if test="reserve2 != null and reserve2 != ''"> and RESERVE2 = #{reserve2}</if>
            <if test="labelNoChangeMark != null and labelNoChangeMark != ''"> and LABEL_NO_CHANGE_MARK = #{labelNoChangeMark}</if>
            <if test="selfProcurementLine != null and selfProcurementLine != ''"> and SELF_PROCUREMENT_LINE = #{selfProcurementLine}</if>
            <if test="labelSpecification != null and labelSpecification != ''"> and LABEL_SPECIFICATION = #{labelSpecification}</if>
            <if test="selfProcurementKd != null and selfProcurementKd != ''"> and SELF_PROCUREMENT_KD = #{selfProcurementKd}</if>
            <if test="reserve3 != null and reserve3 != ''"> and RESERVE3 = #{reserve3}</if>
            <if test="kdElements != null and kdElements != ''"> and KD_ELEMENTS = #{kdElements}</if>
            <if test="transitCode != null and transitCode != ''"> and TRANSIT_CODE = #{transitCode}</if>
            <if test="transitDeliveryDate != null and transitDeliveryDate != ''"> and TRANSIT_DELIVERY_DATE = #{transitDeliveryDate}</if>
            <if test="partsId != null and partsId != ''"> and PARTS_ID = #{partsId}</if>
            <if test="deliveryTicketNo != null and deliveryTicketNo != ''"> and DELIVERY_TICKET_NO = #{deliveryTicketNo}</if>
            <if test="withdrawalClass != null and withdrawalClass != ''"> and WITHDRAWAL_CLASS = #{withdrawalClass}</if>
            <if test="partPrice != null and partPrice != ''"> and PART_PRICE = #{partPrice}</if>
            <if test="prototypeSymbol != null and prototypeSymbol != ''"> and PROTOTYPE_SYMBOL = #{prototypeSymbol}</if>
            <if test="resendSymbol != null and resendSymbol != ''"> and RESEND_SYMBOL = #{resendSymbol}</if>
            <if test="orderDate != null and orderDate != ''"> and ORDER_DATE = #{orderDate}</if>
            <if test="labelSize != null and labelSize != ''"> and LABEL_SIZE = #{labelSize}</if>
            <if test="reserve4 != null and reserve4 != ''"> and RESERVE4 = #{reserve4}</if>
            <if test="reserve5 != null and reserve5 != ''"> and RESERVE5 = #{reserve5}</if>
            <if test="labelTerminalNo != null and labelTerminalNo != ''"> and LABEL_TERMINAL_NO = #{labelTerminalNo}</if>
            <if test="listTerminalNo != null and listTerminalNo != ''"> and LIST_TERMINAL_NO = #{listTerminalNo}</if>
            <if test="makerPic != null and makerPic != ''"> and MAKER_PIC = #{makerPic}</if>
            <if test="makerSnp != null and makerSnp != ''"> and MAKER_SNP = #{makerSnp}</if>
            <if test="resendDepot != null and resendDepot != ''"> and RESEND_DEPOT = #{resendDepot}</if>
            <if test="partsNo != null and partsNo != ''"> and PARTS_NO = #{partsNo}</if>
            <if test="productClass != null and productClass != ''"> and PRODUCT_CLASS = #{productClass}</if>
            <if test="productionMethod != null and productionMethod != ''"> and PRODUCTION_METHOD = #{productionMethod}</if>
            <if test="deliveryMethod2 != null and deliveryMethod2 != ''"> and DELIVERY_METHOD2 = #{deliveryMethod2}</if>
            <if test="fromProcess != null and fromProcess != ''"> and FROM_PROCESS = #{fromProcess}</if>
            <if test="fromWorkArea != null and fromWorkArea != ''"> and FROM_WORK_AREA = #{fromWorkArea}</if>
            <if test="shippingPic != null and shippingPic != ''"> and SHIPPING_PIC = #{shippingPic}</if>
            <if test="partsNoIdCode != null and partsNoIdCode != ''"> and PARTS_NO_ID_CODE = #{partsNoIdCode}</if>
            <if test="instructionDate != null and instructionDate != ''"> and INSTRUCTION_DATE = #{instructionDate}</if>
            <if test="instructionTime != null and instructionTime != ''"> and INSTRUCTION_TIME = #{instructionTime}</if>
            <if test="instructionNo != null and instructionNo != ''"> and INSTRUCTION_NO = #{instructionNo}</if>
            <if test="productionHandlingMethod != null and productionHandlingMethod != ''"> and PRODUCTION_HANDLING_METHOD = #{productionHandlingMethod}</if>
            <if test="plantCode != null and plantCode != ''"> and PLANT_CODE = #{plantCode}</if>
            <if test="shippingLocation != null and shippingLocation != ''"> and SHIPPING_LOCATION = #{shippingLocation}</if>
            <if test="shippingPort != null and shippingPort != ''"> and SHIPPING_PORT = #{shippingPort}</if>
            <if test="shippingCheckFlag != null and shippingCheckFlag != ''"> and SHIPPING_CHECK_FLAG = #{shippingCheckFlag}</if>
            <if test="reserve6 != null and reserve6 != ''"> and RESERVE6 = #{reserve6}</if>
            <if test="labelIssueAssignment != null and labelIssueAssignment != ''"> and LABEL_ISSUE_ASSIGNMENT = #{labelIssueAssignment}</if>
            <if test="divisionDepot != null and divisionDepot != ''"> and DIVISION_DEPOT = #{divisionDepot}</if>
            <if test="customerCode != null and customerCode != ''"> and CUSTOMER_CODE = #{customerCode}</if>
            <if test="demandCode2 != null and demandCode2 != ''"> and DEMAND_CODE2 = #{demandCode2}</if>
            <if test="shippingLocationCode2 != null and shippingLocationCode2 != ''"> and SHIPPING_LOCATION_CODE2 = #{shippingLocationCode2}</if>
            <if test="movementLeadTime != null and movementLeadTime != ''"> and MOVEMENT_LEAD_TIME = #{movementLeadTime}</if>
            <if test="rehandlingFlag != null and rehandlingFlag != ''"> and REHANDLING_FLAG = #{rehandlingFlag}</if>
            <if test="noMasterFlag != null and noMasterFlag != ''"> and NO_MASTER_FLAG = #{noMasterFlag}</if>
            <if test="reserve7 != null and reserve7 != ''"> and RESERVE7 = #{reserve7}</if>
            <if test="divisionDepot2 != null and divisionDepot2 != ''"> and DIVISION_DEPOT2 = #{divisionDepot2}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by DELIVERY_INSTRUCTION_DATE desc, DELIVERY_INSTRUCTION_TIME desc, id desc
    </select>

    <sql id="selectTblSapKanbanSvVo">
        select id, FILE_NO, RESERVE1, DEMAND_CODE, ISSUE_NO_ID, DELIVERY_INSTRUCTION_YY, MAKER, DEPOT, CLASSIFICATION, DELIVERY_LOCATION, CUSTOMER_PARTS_NO, CURRENT_PROCESS_CLASS, PARTS_NAME, DELIVERY_INSTRUCTION_NUMBER, SUPPLY_LOCATION, SNEP, SAFETY_FLAG, LABEL_COUNT, MANAGEMENT_PIC, ISSUE_REASON, VIP_FLAG, PACKAGE_CODE, INTERNAL_EXTERNAL_CLASS, DELIVERY_INSTRUCTION_COUNT, DELIVERY_METHOD, ORDER_CLASS, RESERVE2, DELAY_PREVENTION_MAKER_SYMBOL, RESERVE3, RESERVE4, RESERVE5, LABEL_SIZE, LABEL_SPECIFICATION, ORDER_TYPE, CHECK_SPECIFICATION, LOCATION, PACK_CLASS, TRADE_SPECIFICATION, OUTPACK_MATERIAL_CODE, OUTPACK_MATERIAL_PROC_CLASS, BIND_PACK_CODE, BIND_UNIT, PACK_UNIT, CONSIST_PACK_CLASS, ELEMENTS_FINISH_CLASS, INPACK_CODE1, INPACK_PROC_CLASS1, INPACK_NECESSARY_NUMBER1, INPACK_CODE2, INPACK_PROC_CLASS2, INPACK_NECESSARY_NUMBER2, INPACK_CODE3, INPACK_PROC_CLASS3, INPACK_NECESSARY_NUMBER3, INPACK_CODE4, INPACK_PROC_CLASS4, INPACK_NECESSARY_NUMBER4, INPACK_CODE5, INPACK_PROC_CLASS5, INPACK_NECESSARY_NUMBER5, INPACK_CODE6, INPACK_PROC_CLASS6, INPACK_NECESSARY_NUMBER6, INPACK_CODE7, INPACK_PROC_CLASS7, INPACK_NECESSARY_NUMBER7, INPACK_CODE8, INPACK_PROC_CLASS8, INPACK_NECESSARY_NUMBER8, INPACK_CODE9, INPACK_PROC_CLASS9, INPACK_NECESSARY_NUMBER9, MODULE_MATERIAL, MODULE_UNIT, MODULE_CLASS, ORIGINAL_PART_NO, PART_MANAGE_CLASS, NEWCAR_PROGRAM_CODE, ORIGINAL_ISSUE_NO, DESIGN_CHANGE_NO, NORMAL_CONTAINER_CODE, BO_FORECAST_DATE, BO_SYMBOL, TRUSTEE_ID, DELIVERY_DOC_TYPE, PACKAGE_LABEL_TYPE, LABEL_ISSUE_ORG, DELIVERY_CONTAINER, UL_CONTAINER_COUNT, ITEM_SUBNO1, ITEM_SUBNO2, ITEM_SUBNO3, ITEM_SUBNO4, ITEM_SUBNO5, ITEM_SUBNO6, ITEM_SUBNO7, ITEM_SUBNO8, ITEM_SUBNO9, ITEM_SUBNO10, ITEM_SUBNO11, ITEM_SUBNO12, ITEM_SUBNO13, ITEM_SUBNO14, ITEM_SUBNO15, ITEM_SUBNO16, ITEM_SUBNO17, ITEM_SUBNO18, ITEM_SUBNO19, ITEM_SUBNO20, BARCODE_INFO, FREE_COLUMN1, FREE_COLUMN2, FREE_COLUMN3, FREE_COLUMN4, YARD, ENLARGE_MARK1, ENLARGE_MARK2, NO_DIFF_DELIVERY_CLASS, UL_SNP, DELIVERY_TICKET_NO, WITHDRAWAL_CLASS, PART_PRICE, PROTOTYPE_SYMBOL, RESEND_SYMBOL, ORDER_DATE, RESERVE6, RESERVE7, LABEL_TERMINAL_NO, LIST_TERMINAL_NO, MAKER_PIC, MAKER_SNP, RESEND_DEPOT, PARTS_NO, PRODUCT_CLASS, PRODUCTION_METHOD, DELIVERY_METHOD2, FROM_PROCESS, FROM_WORK_AREA, SHIPPING_PIC, PARTS_NO_ID_CODE, INSTRUCTION_DATE, INSTRUCTION_TIME, INSTRUCTION_NO, PRODUCTION_HANDLING_METHOD, PLANT_CODE, SHIPPING_LOCATION, SHIPPING_PORT, RESERVE8, LABEL_ISSUE_ASSIGNMENT, DIVISION_DEPOT, CUSTOMER_CODE, DEMAND_CODE2, SHIPPING_LOCATION_CODE2, MOVEMENT_LEAD_TIME, REHANDLING_FLAG, NO_MASTER_FLAG, RESERVE9, DIVISION_DEPOT2 from tbl_sap_kanban_sv a
    </sql>

    <select id="selectTblSapKanbanSvList" parameterType="TblSapKanbanSv" resultMap="TblSapKanbanSvResult">
        <include refid="selectTblSapKanbanSvVo"/>
        <where>
            <if test="fileNo != null  and fileNo != ''"> and FILE_NO = #{fileNo}</if>
            <if test="reserve1 != null  and reserve1 != ''"> and RESERVE1 = #{reserve1}</if>
            <if test="demandCode != null  and demandCode != ''"> and DEMAND_CODE = #{demandCode}</if>
            <if test="issueNoId != null  and issueNoId != ''"> and ISSUE_NO_ID = #{issueNoId}</if>
            <if test="deliveryInstructionYy != null  and deliveryInstructionYy != ''"> and DELIVERY_INSTRUCTION_YY = #{deliveryInstructionYy}</if>
            <if test="maker != null  and maker != ''"> and MAKER = #{maker}</if>
            <if test="depot != null  and depot != ''"> and DEPOT = #{depot}</if>
            <if test="classification != null  and classification != ''"> and CLASSIFICATION = #{classification}</if>
            <if test="deliveryLocation != null  and deliveryLocation != ''"> and DELIVERY_LOCATION = #{deliveryLocation}</if>
            <if test="customerPartsNo != null  and customerPartsNo != ''"> and CUSTOMER_PARTS_NO = #{customerPartsNo}</if>
            <if test="currentProcessClass != null  and currentProcessClass != ''"> and CURRENT_PROCESS_CLASS = #{currentProcessClass}</if>
            <if test="partsName != null  and partsName != ''"> and PARTS_NAME like concat('%', #{partsName}, '%')</if>
            <if test="deliveryInstructionNumber != null  and deliveryInstructionNumber != ''"> and DELIVERY_INSTRUCTION_NUMBER = #{deliveryInstructionNumber}</if>
            <if test="supplyLocation != null  and supplyLocation != ''"> and SUPPLY_LOCATION = #{supplyLocation}</if>
            <if test="snep != null  and snep != ''"> and SNEP = #{snep}</if>
            <if test="safetyFlag != null  and safetyFlag != ''"> and SAFETY_FLAG = #{safetyFlag}</if>
            <if test="labelCount != null  and labelCount != ''"> and LABEL_COUNT = #{labelCount}</if>
            <if test="managementPic != null  and managementPic != ''"> and MANAGEMENT_PIC = #{managementPic}</if>
            <if test="issueReason != null  and issueReason != ''"> and ISSUE_REASON = #{issueReason}</if>
            <if test="vipFlag != null  and vipFlag != ''"> and VIP_FLAG = #{vipFlag}</if>
            <if test="packageCode != null  and packageCode != ''"> and PACKAGE_CODE = #{packageCode}</if>
            <if test="internalExternalClass != null  and internalExternalClass != ''"> and INTERNAL_EXTERNAL_CLASS = #{internalExternalClass}</if>
            <if test="deliveryInstructionCount != null  and deliveryInstructionCount != ''"> and DELIVERY_INSTRUCTION_COUNT = #{deliveryInstructionCount}</if>
            <if test="deliveryMethod != null  and deliveryMethod != ''"> and DELIVERY_METHOD = #{deliveryMethod}</if>
            <if test="orderClass != null  and orderClass != ''"> and ORDER_CLASS = #{orderClass}</if>
            <if test="reserve2 != null  and reserve2 != ''"> and RESERVE2 = #{reserve2}</if>
            <if test="delayPreventionMakerSymbol != null  and delayPreventionMakerSymbol != ''"> and DELAY_PREVENTION_MAKER_SYMBOL = #{delayPreventionMakerSymbol}</if>
            <if test="reserve3 != null  and reserve3 != ''"> and RESERVE3 = #{reserve3}</if>
            <if test="reserve4 != null  and reserve4 != ''"> and RESERVE4 = #{reserve4}</if>
            <if test="reserve5 != null  and reserve5 != ''"> and RESERVE5 = #{reserve5}</if>
            <if test="labelSize != null  and labelSize != ''"> and LABEL_SIZE = #{labelSize}</if>
            <if test="labelSpecification != null  and labelSpecification != ''"> and LABEL_SPECIFICATION = #{labelSpecification}</if>
            <if test="orderType != null  and orderType != ''"> and ORDER_TYPE = #{orderType}</if>
            <if test="checkSpecification != null  and checkSpecification != ''"> and CHECK_SPECIFICATION = #{checkSpecification}</if>
            <if test="location != null  and location != ''"> and LOCATION = #{location}</if>
            <if test="packClass != null  and packClass != ''"> and PACK_CLASS = #{packClass}</if>
            <if test="tradeSpecification != null  and tradeSpecification != ''"> and TRADE_SPECIFICATION = #{tradeSpecification}</if>
            <if test="outpackMaterialCode != null  and outpackMaterialCode != ''"> and OUTPACK_MATERIAL_CODE = #{outpackMaterialCode}</if>
            <if test="outpackMaterialProcClass != null  and outpackMaterialProcClass != ''"> and OUTPACK_MATERIAL_PROC_CLASS = #{outpackMaterialProcClass}</if>
            <if test="bindPackCode != null  and bindPackCode != ''"> and BIND_PACK_CODE = #{bindPackCode}</if>
            <if test="bindUnit != null  and bindUnit != ''"> and BIND_UNIT = #{bindUnit}</if>
            <if test="packUnit != null  and packUnit != ''"> and PACK_UNIT = #{packUnit}</if>
            <if test="consistPackClass != null  and consistPackClass != ''"> and CONSIST_PACK_CLASS = #{consistPackClass}</if>
            <if test="elementsFinishClass != null  and elementsFinishClass != ''"> and ELEMENTS_FINISH_CLASS = #{elementsFinishClass}</if>
            <if test="inpackCode1 != null  and inpackCode1 != ''"> and INPACK_CODE1 = #{inpackCode1}</if>
            <if test="inpackProcClass1 != null  and inpackProcClass1 != ''"> and INPACK_PROC_CLASS1 = #{inpackProcClass1}</if>
            <if test="inpackNecessaryNumber1 != null  and inpackNecessaryNumber1 != ''"> and INPACK_NECESSARY_NUMBER1 = #{inpackNecessaryNumber1}</if>
            <if test="inpackCode2 != null  and inpackCode2 != ''"> and INPACK_CODE2 = #{inpackCode2}</if>
            <if test="inpackProcClass2 != null  and inpackProcClass2 != ''"> and INPACK_PROC_CLASS2 = #{inpackProcClass2}</if>
            <if test="inpackNecessaryNumber2 != null  and inpackNecessaryNumber2 != ''"> and INPACK_NECESSARY_NUMBER2 = #{inpackNecessaryNumber2}</if>
            <if test="inpackCode3 != null  and inpackCode3 != ''"> and INPACK_CODE3 = #{inpackCode3}</if>
            <if test="inpackProcClass3 != null  and inpackProcClass3 != ''"> and INPACK_PROC_CLASS3 = #{inpackProcClass3}</if>
            <if test="inpackNecessaryNumber3 != null  and inpackNecessaryNumber3 != ''"> and INPACK_NECESSARY_NUMBER3 = #{inpackNecessaryNumber3}</if>
            <if test="inpackCode4 != null  and inpackCode4 != ''"> and INPACK_CODE4 = #{inpackCode4}</if>
            <if test="inpackProcClass4 != null  and inpackProcClass4 != ''"> and INPACK_PROC_CLASS4 = #{inpackProcClass4}</if>
            <if test="inpackNecessaryNumber4 != null  and inpackNecessaryNumber4 != ''"> and INPACK_NECESSARY_NUMBER4 = #{inpackNecessaryNumber4}</if>
            <if test="inpackCode5 != null  and inpackCode5 != ''"> and INPACK_CODE5 = #{inpackCode5}</if>
            <if test="inpackProcClass5 != null  and inpackProcClass5 != ''"> and INPACK_PROC_CLASS5 = #{inpackProcClass5}</if>
            <if test="inpackNecessaryNumber5 != null  and inpackNecessaryNumber5 != ''"> and INPACK_NECESSARY_NUMBER5 = #{inpackNecessaryNumber5}</if>
            <if test="inpackCode6 != null  and inpackCode6 != ''"> and INPACK_CODE6 = #{inpackCode6}</if>
            <if test="inpackProcClass6 != null  and inpackProcClass6 != ''"> and INPACK_PROC_CLASS6 = #{inpackProcClass6}</if>
            <if test="inpackNecessaryNumber6 != null  and inpackNecessaryNumber6 != ''"> and INPACK_NECESSARY_NUMBER6 = #{inpackNecessaryNumber6}</if>
            <if test="inpackCode7 != null  and inpackCode7 != ''"> and INPACK_CODE7 = #{inpackCode7}</if>
            <if test="inpackProcClass7 != null  and inpackProcClass7 != ''"> and INPACK_PROC_CLASS7 = #{inpackProcClass7}</if>
            <if test="inpackNecessaryNumber7 != null  and inpackNecessaryNumber7 != ''"> and INPACK_NECESSARY_NUMBER7 = #{inpackNecessaryNumber7}</if>
            <if test="inpackCode8 != null  and inpackCode8 != ''"> and INPACK_CODE8 = #{inpackCode8}</if>
            <if test="inpackProcClass8 != null  and inpackProcClass8 != ''"> and INPACK_PROC_CLASS8 = #{inpackProcClass8}</if>
            <if test="inpackNecessaryNumber8 != null  and inpackNecessaryNumber8 != ''"> and INPACK_NECESSARY_NUMBER8 = #{inpackNecessaryNumber8}</if>
            <if test="inpackCode9 != null  and inpackCode9 != ''"> and INPACK_CODE9 = #{inpackCode9}</if>
            <if test="inpackProcClass9 != null  and inpackProcClass9 != ''"> and INPACK_PROC_CLASS9 = #{inpackProcClass9}</if>
            <if test="inpackNecessaryNumber9 != null  and inpackNecessaryNumber9 != ''"> and INPACK_NECESSARY_NUMBER9 = #{inpackNecessaryNumber9}</if>
            <if test="moduleMaterial != null  and moduleMaterial != ''"> and MODULE_MATERIAL = #{moduleMaterial}</if>
            <if test="moduleUnit != null  and moduleUnit != ''"> and MODULE_UNIT = #{moduleUnit}</if>
            <if test="moduleClass != null  and moduleClass != ''"> and MODULE_CLASS = #{moduleClass}</if>
            <if test="originalPartNo != null  and originalPartNo != ''"> and ORIGINAL_PART_NO = #{originalPartNo}</if>
            <if test="partManageClass != null  and partManageClass != ''"> and PART_MANAGE_CLASS = #{partManageClass}</if>
            <if test="newcarProgramCode != null  and newcarProgramCode != ''"> and NEWCAR_PROGRAM_CODE = #{newcarProgramCode}</if>
            <if test="originalIssueNo != null  and originalIssueNo != ''"> and ORIGINAL_ISSUE_NO = #{originalIssueNo}</if>
            <if test="designChangeNo != null  and designChangeNo != ''"> and DESIGN_CHANGE_NO = #{designChangeNo}</if>
            <if test="normalContainerCode != null  and normalContainerCode != ''"> and NORMAL_CONTAINER_CODE = #{normalContainerCode}</if>
            <if test="boForecastDate != null  and boForecastDate != ''"> and BO_FORECAST_DATE = #{boForecastDate}</if>
            <if test="boSymbol != null  and boSymbol != ''"> and BO_SYMBOL = #{boSymbol}</if>
            <if test="trusteeId != null  and trusteeId != ''"> and TRUSTEE_ID = #{trusteeId}</if>
            <if test="deliveryDocType != null  and deliveryDocType != ''"> and DELIVERY_DOC_TYPE = #{deliveryDocType}</if>
            <if test="packageLabelType != null  and packageLabelType != ''"> and PACKAGE_LABEL_TYPE = #{packageLabelType}</if>
            <if test="labelIssueOrg != null  and labelIssueOrg != ''"> and LABEL_ISSUE_ORG = #{labelIssueOrg}</if>
            <if test="deliveryContainer != null  and deliveryContainer != ''"> and DELIVERY_CONTAINER = #{deliveryContainer}</if>
            <if test="ulContainerCount != null  and ulContainerCount != ''"> and UL_CONTAINER_COUNT = #{ulContainerCount}</if>
            <if test="itemSubno1 != null  and itemSubno1 != ''"> and ITEM_SUBNO1 = #{itemSubno1}</if>
            <if test="itemSubno2 != null  and itemSubno2 != ''"> and ITEM_SUBNO2 = #{itemSubno2}</if>
            <if test="itemSubno3 != null  and itemSubno3 != ''"> and ITEM_SUBNO3 = #{itemSubno3}</if>
            <if test="itemSubno4 != null  and itemSubno4 != ''"> and ITEM_SUBNO4 = #{itemSubno4}</if>
            <if test="itemSubno5 != null  and itemSubno5 != ''"> and ITEM_SUBNO5 = #{itemSubno5}</if>
            <if test="itemSubno6 != null  and itemSubno6 != ''"> and ITEM_SUBNO6 = #{itemSubno6}</if>
            <if test="itemSubno7 != null  and itemSubno7 != ''"> and ITEM_SUBNO7 = #{itemSubno7}</if>
            <if test="itemSubno8 != null  and itemSubno8 != ''"> and ITEM_SUBNO8 = #{itemSubno8}</if>
            <if test="itemSubno9 != null  and itemSubno9 != ''"> and ITEM_SUBNO9 = #{itemSubno9}</if>
            <if test="itemSubno10 != null  and itemSubno10 != ''"> and ITEM_SUBNO10 = #{itemSubno10}</if>
            <if test="itemSubno11 != null  and itemSubno11 != ''"> and ITEM_SUBNO11 = #{itemSubno11}</if>
            <if test="itemSubno12 != null  and itemSubno12 != ''"> and ITEM_SUBNO12 = #{itemSubno12}</if>
            <if test="itemSubno13 != null  and itemSubno13 != ''"> and ITEM_SUBNO13 = #{itemSubno13}</if>
            <if test="itemSubno14 != null  and itemSubno14 != ''"> and ITEM_SUBNO14 = #{itemSubno14}</if>
            <if test="itemSubno15 != null  and itemSubno15 != ''"> and ITEM_SUBNO15 = #{itemSubno15}</if>
            <if test="itemSubno16 != null  and itemSubno16 != ''"> and ITEM_SUBNO16 = #{itemSubno16}</if>
            <if test="itemSubno17 != null  and itemSubno17 != ''"> and ITEM_SUBNO17 = #{itemSubno17}</if>
            <if test="itemSubno18 != null  and itemSubno18 != ''"> and ITEM_SUBNO18 = #{itemSubno18}</if>
            <if test="itemSubno19 != null  and itemSubno19 != ''"> and ITEM_SUBNO19 = #{itemSubno19}</if>
            <if test="itemSubno20 != null  and itemSubno20 != ''"> and ITEM_SUBNO20 = #{itemSubno20}</if>
            <if test="barcodeInfo != null  and barcodeInfo != ''"> and BARCODE_INFO = #{barcodeInfo}</if>
            <if test="freeColumn1 != null  and freeColumn1 != ''"> and FREE_COLUMN1 = #{freeColumn1}</if>
            <if test="freeColumn2 != null  and freeColumn2 != ''"> and FREE_COLUMN2 = #{freeColumn2}</if>
            <if test="freeColumn3 != null  and freeColumn3 != ''"> and FREE_COLUMN3 = #{freeColumn3}</if>
            <if test="freeColumn4 != null  and freeColumn4 != ''"> and FREE_COLUMN4 = #{freeColumn4}</if>
            <if test="yard != null  and yard != ''"> and YARD = #{yard}</if>
            <if test="enlargeMark1 != null  and enlargeMark1 != ''"> and ENLARGE_MARK1 = #{enlargeMark1}</if>
            <if test="enlargeMark2 != null  and enlargeMark2 != ''"> and ENLARGE_MARK2 = #{enlargeMark2}</if>
            <if test="noDiffDeliveryClass != null  and noDiffDeliveryClass != ''"> and NO_DIFF_DELIVERY_CLASS = #{noDiffDeliveryClass}</if>
            <if test="ulSnp != null  and ulSnp != ''"> and UL_SNP = #{ulSnp}</if>
            <if test="deliveryTicketNo != null  and deliveryTicketNo != ''"> and DELIVERY_TICKET_NO = #{deliveryTicketNo}</if>
            <if test="withdrawalClass != null  and withdrawalClass != ''"> and WITHDRAWAL_CLASS = #{withdrawalClass}</if>
            <if test="partPrice != null  and partPrice != ''"> and PART_PRICE = #{partPrice}</if>
            <if test="prototypeSymbol != null  and prototypeSymbol != ''"> and PROTOTYPE_SYMBOL = #{prototypeSymbol}</if>
            <if test="resendSymbol != null  and resendSymbol != ''"> and RESEND_SYMBOL = #{resendSymbol}</if>
            <if test="orderDate != null  and orderDate != ''"> and ORDER_DATE = #{orderDate}</if>
            <if test="reserve6 != null  and reserve6 != ''"> and RESERVE6 = #{reserve6}</if>
            <if test="reserve7 != null  and reserve7 != ''"> and RESERVE7 = #{reserve7}</if>
            <if test="labelTerminalNo != null  and labelTerminalNo != ''"> and LABEL_TERMINAL_NO = #{labelTerminalNo}</if>
            <if test="listTerminalNo != null  and listTerminalNo != ''"> and LIST_TERMINAL_NO = #{listTerminalNo}</if>
            <if test="makerPic != null  and makerPic != ''"> and MAKER_PIC = #{makerPic}</if>
            <if test="makerSnp != null  and makerSnp != ''"> and MAKER_SNP = #{makerSnp}</if>
            <if test="resendDepot != null  and resendDepot != ''"> and RESEND_DEPOT = #{resendDepot}</if>
            <if test="partsNo != null  and partsNo != ''"> and PARTS_NO = #{partsNo}</if>
            <if test="productClass != null  and productClass != ''"> and PRODUCT_CLASS = #{productClass}</if>
            <if test="productionMethod != null  and productionMethod != ''"> and PRODUCTION_METHOD = #{productionMethod}</if>
            <if test="deliveryMethod2 != null  and deliveryMethod2 != ''"> and DELIVERY_METHOD2 = #{deliveryMethod2}</if>
            <if test="fromProcess != null  and fromProcess != ''"> and FROM_PROCESS = #{fromProcess}</if>
            <if test="fromWorkArea != null  and fromWorkArea != ''"> and FROM_WORK_AREA = #{fromWorkArea}</if>
            <if test="shippingPic != null  and shippingPic != ''"> and SHIPPING_PIC = #{shippingPic}</if>
            <if test="partsNoIdCode != null  and partsNoIdCode != ''"> and PARTS_NO_ID_CODE = #{partsNoIdCode}</if>
            <if test="instructionDate != null  and instructionDate != ''"> and INSTRUCTION_DATE = #{instructionDate}</if>
            <if test="instructionTime != null  and instructionTime != ''"> and INSTRUCTION_TIME = #{instructionTime}</if>
            <if test="instructionNo != null  and instructionNo != ''"> and INSTRUCTION_NO = #{instructionNo}</if>
            <if test="productionHandlingMethod != null  and productionHandlingMethod != ''"> and PRODUCTION_HANDLING_METHOD = #{productionHandlingMethod}</if>
            <if test="plantCode != null  and plantCode != ''"> and PLANT_CODE = #{plantCode}</if>
            <if test="shippingLocation != null  and shippingLocation != ''"> and SHIPPING_LOCATION = #{shippingLocation}</if>
            <if test="shippingPort != null  and shippingPort != ''"> and SHIPPING_PORT = #{shippingPort}</if>
            <if test="reserve8 != null  and reserve8 != ''"> and RESERVE8 = #{reserve8}</if>
            <if test="labelIssueAssignment != null  and labelIssueAssignment != ''"> and LABEL_ISSUE_ASSIGNMENT = #{labelIssueAssignment}</if>
            <if test="divisionDepot != null  and divisionDepot != ''"> and DIVISION_DEPOT = #{divisionDepot}</if>
            <if test="customerCode != null  and customerCode != ''"> and CUSTOMER_CODE = #{customerCode}</if>
            <if test="demandCode2 != null  and demandCode2 != ''"> and DEMAND_CODE2 = #{demandCode2}</if>
            <if test="shippingLocationCode2 != null  and shippingLocationCode2 != ''"> and SHIPPING_LOCATION_CODE2 = #{shippingLocationCode2}</if>
            <if test="movementLeadTime != null  and movementLeadTime != ''"> and MOVEMENT_LEAD_TIME = #{movementLeadTime}</if>
            <if test="rehandlingFlag != null  and rehandlingFlag != ''"> and REHANDLING_FLAG = #{rehandlingFlag}</if>
            <if test="noMasterFlag != null  and noMasterFlag != ''"> and NO_MASTER_FLAG = #{noMasterFlag}</if>
            <if test="reserve9 != null  and reserve9 != ''"> and RESERVE9 = #{reserve9}</if>
            <if test="divisionDepot2 != null  and divisionDepot2 != ''"> and DIVISION_DEPOT2 = #{divisionDepot2}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by DELIVERY_INSTRUCTION_YY desc, id desc
    </select>

    <select id="selectTblSapKanbanSvById" parameterType="Long" resultMap="TblSapKanbanSvResult">
        <include refid="selectTblSapKanbanSvVo"/>
        where id = #{id}
    </select>
</mapper>