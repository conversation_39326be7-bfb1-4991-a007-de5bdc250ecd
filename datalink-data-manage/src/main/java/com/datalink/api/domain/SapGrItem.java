package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;

public class SapGrItem {

    //公司代码
    @Json<PERSON>lias("bukrs")
    @JsonProperty("BUKRS")
    private String BUKRS;

    //Depot
    @JsonAlias("zdepot")
    @JsonProperty("ZDEPOT")
    private String ZDEPOT;

    //工厂代码
    @JsonAlias("werks")
    @JsonProperty("WERKS")
    private String WERKS;

    //采购订单
    @JsonAlias("ebeln")
    @JsonProperty("EBELN")
    private String EBELN;

    //物料编码
    @JsonAlias("matnr")
    @JsonProperty("MATNR")
    private String MATNR;

    //收货场所
    @JsonAlias("wanst")
    @JsonProperty("WANST")
    private String WANST;

    //订单计划交期
    @JsonAlias("eindt")
    @JsonProperty("EINDT")
    private String EINDT;

    //订单数量
    @JsonAlias("menge")
    @JsonProperty("MENGE")
    private String MENGE;

    //采购计量单位
    @JsonAlias("meins")
    @JsonProperty("MEINS")
    private String MEINS;

    //实际收货日期
    @JsonAlias("budat")
    @JsonProperty("BUDAT")
    private String BUDAT;

    //收货数量
    @JsonAlias("menge_1")
    @JsonProperty("MENGE_1")
    private String MENGE_1;

    //ASN号  外部ID号
    @JsonAlias("lifex")
    @JsonProperty("LIFEX")
    private String LIFEX;

    //交货单创建日期
    @JsonAlias("erdat")
    @JsonProperty("ERDAT")
    private String ERDAT;

    //交货单创建时间
    @JsonAlias("erzet")
    @JsonProperty("ERZET")
    private String ERZET;

    //推送打印日期
    @JsonAlias("erdat_1")
    @JsonProperty("ERDAT_1")
    private String ERDAT_1;

    //订单行项目
    @JsonAlias("ebelp")
    @JsonProperty("EBELP")
    private String EBELP;

    //移动类型
    @JsonAlias("bwart")
    @JsonProperty("BWART")
    private String BWART;

    //采购组织
    @JsonAlias("ekorg")
    @JsonProperty("EKORG")
    private String EKORG;

    //物料凭证
    @JsonAlias("mblnr")
    @JsonProperty("MBLNR")
    private String MBLNR;

    //物料凭证行项目
    @JsonAlias("zeile")
    @JsonProperty("ZEILE")
    private String ZEILE;

    //原物料凭证
    @JsonAlias("smbln")
    @JsonProperty("SMBLN")
    private String SMBLN;

    //原物料凭证项目
    @JsonAlias("smblp")
    @JsonProperty("SMBLP")
    private String SMBLP;

    //供应商编码
    @JsonAlias("lifnr")
    @JsonProperty("LIFNR")
    private String LIFNR;

    //基本计量单位
    @JsonAlias("meins_1")
    @JsonProperty("MEINS_1")
    private String MEINS_1;

    //物料描述
    @JsonAlias("maktx")
    @JsonProperty("MAKTX")
    private String MAKTX;

    public String getWANST() {
        return WANST;
    }

    public void setWANST(String WANST) {
        this.WANST = WANST;
    }

    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }

    public String getZDEPOT() {
        return ZDEPOT;
    }

    public void setZDEPOT(String ZDEPOT) {
        this.ZDEPOT = ZDEPOT;
    }

    public String getWERKS() {
        return WERKS;
    }

    public void setWERKS(String WERKS) {
        this.WERKS = WERKS;
    }

    public String getEBELN() {
        return EBELN;
    }

    public void setEBELN(String EBELN) {
        this.EBELN = EBELN;
    }

    public String getMATNR() {
        return MATNR;
    }

    public void setMATNR(String MATNR) {
        this.MATNR = MATNR;
    }

    public String getEINDT() {
        return EINDT;
    }

    public void setEINDT(String EINDT) {
        this.EINDT = EINDT;
    }

    public String getMENGE() {
        return MENGE;
    }

    public void setMENGE(String MENGE) {
        this.MENGE = MENGE;
    }

    public String getMEINS() {
        return MEINS;
    }

    public void setMEINS(String MEINS) {
        this.MEINS = MEINS;
    }

    public String getBUDAT() {
        return BUDAT;
    }

    public void setBUDAT(String BUDAT) {
        this.BUDAT = BUDAT;
    }

    public String getMENGE_1() {
        return MENGE_1;
    }

    public void setMENGE_1(String MENGE_1) {
        this.MENGE_1 = MENGE_1;
    }

    public String getLIFEX() {
        return LIFEX;
    }

    public void setLIFEX(String LIFEX) {
        this.LIFEX = LIFEX;
    }

    public String getERDAT() {
        return ERDAT;
    }

    public void setERDAT(String ERDAT) {
        this.ERDAT = ERDAT;
    }

    public String getERZET() {
        return ERZET;
    }

    public void setERZET(String ERZET) {
        this.ERZET = ERZET;
    }

    public String getERDAT_1() {
        return ERDAT_1;
    }

    public void setERDAT_1(String ERDAT_1) {
        this.ERDAT_1 = ERDAT_1;
    }

    public String getEBELP() {
        return EBELP;
    }

    public void setEBELP(String EBELP) {
        this.EBELP = EBELP;
    }

    public String getBWART() {
        return BWART;
    }

    public void setBWART(String BWART) {
        this.BWART = BWART;
    }

    public String getEKORG() {
        return EKORG;
    }

    public void setEKORG(String EKORG) {
        this.EKORG = EKORG;
    }

    public String getMBLNR() {
        return MBLNR;
    }

    public void setMBLNR(String MBLNR) {
        this.MBLNR = MBLNR;
    }

    public String getZEILE() {
        return ZEILE;
    }

    public void setZEILE(String ZEILE) {
        this.ZEILE = ZEILE;
    }

    public String getSMBLN() {
        return SMBLN;
    }

    public void setSMBLN(String SMBLN) {
        this.SMBLN = SMBLN;
    }

    public String getSMBLP() {
        return SMBLP;
    }

    public void setSMBLP(String SMBLP) {
        this.SMBLP = SMBLP;
    }

    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }

    public String getMEINS_1() {
        return MEINS_1;
    }

    public void setMEINS_1(String MEINS_1) {
        this.MEINS_1 = MEINS_1;
    }

    public String getMAKTX() {
        return MAKTX;
    }

    public void setMAKTX(String MAKTX) {
        this.MAKTX = MAKTX;
    }
}
