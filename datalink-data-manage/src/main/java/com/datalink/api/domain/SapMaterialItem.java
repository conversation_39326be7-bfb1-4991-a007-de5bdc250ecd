package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * SAP物料接口请求对象
 */
@Data
public class SapMaterialItem {
    
    /**
     * 物料编号
     */
    @JsonProperty("MATNR")
    private String matnr;
    
    /**
     * 基本计量单位
     */
    @JsonProperty("MEINS")
    private String meins;
    
    /**
     * 物料描述列表
     */
    @JsonProperty("MATK")
    private List<SapMaterialText> matk;
    
    /**
     * 工厂信息列表
     */
    @JsonProperty("MARC")
    private List<SapMaterialPlant> marc;
} 