package com.datalink.datamanage.controller;

import com.datalink.common.DataConstants;
import com.datalink.common.ServiceEnum;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.service.ITblEventService;
import com.datalink.datamanage.service.ITblNoticeService;
import com.datalink.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公告Controller
 *
 * <AUTHOR>
 * @date 2022-05-02
 */
@RestController
@RequestMapping("/datamanage/notice")
public class TblNoticeController extends BaseController
{
    @Autowired
    private ITblNoticeService tblNoticeService;

    @Autowired
    private ISysConfigService configService;

    @Resource
    private ITblEventService tblEventService;

    /**
     * 查询公告列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:notice:list')")
    @GetMapping("/list")
    public TableDataInfo list(TblNotice tblNotice)
    {
        startPage();
        List<TblNotice> list = tblNoticeService.selectTblNoticeList(tblNotice);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('datamanage:notice:list')")
    @GetMapping("/listReply")
    public TableDataInfo listReply(TblNoticeReply tblNoticeReply)
    {
        startPage();
        List<TblNoticeReply> list = tblNoticeService.selectTblNoticeReplyList(tblNoticeReply);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('datamanage:notice:list')")
    @GetMapping("/listNoticeReply")
    public TableDataInfo listNoticeReply(NoticeReply noticeReply)
    {
        startPage();
        List<NoticeReply> list = tblNoticeService.selectNoticeReplyList(noticeReply);
        return getDataTable(list);
    }

    /**
     * 导出公告列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:notice:export')")
    @Log(title = "公告", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblNotice tblNotice)
    {
        List<TblNotice> list = tblNoticeService.selectTblNoticeList(tblNotice);
        ExcelUtil<TblNotice> util = new ExcelUtil<TblNotice>(TblNotice.class);
        return util.exportExcel(list, "公告数据");
    }

    /**
     * 获取公告详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:notice:query')")
    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable("noticeId") Long noticeId)
    {
        return AjaxResult.success(tblNoticeService.selectTblNoticeById(noticeId));
    }

    /**
     * 新增公告
     */
    @PreAuthorize("@ss.hasPermi('datamanage:notice:add')")
    @Log(title = "公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblNotice tblNotice)
    {
        tblNotice.setCompCode(configService.selectConfigByKey(DataConstants.COMPANY_ID_CONFIG_KEY));
        tblNotice.setPlantCode(configService.selectConfigByKey(DataConstants.PLANT_CODE_CONFIG_KEY));
        tblNotice.setPlantName(configService.selectConfigByKey(DataConstants.PLANT_NAME_CONFIG_KEY));
        return toAjax(tblNoticeService.insertTblNotice(tblNotice));
    }

    /**
     * 修改公告
     */
    @PreAuthorize("@ss.hasPermi('datamanage:notice:edit')")
    @Log(title = "公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblNotice tblNotice)
    {
        return toAjax(tblNoticeService.updateTblNotice(tblNotice));
    }

    /**
     * 删除公告
     */
    @PreAuthorize("@ss.hasPermi('datamanage:notice:remove')")
    @Log(title = "公告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds)
    {
        return toAjax(tblNoticeService.deleteTblNoticeByIds(noticeIds));
    }

    @Log(title = "公告回复", businessType = BusinessType.UPDATE)
    @PostMapping("/updateReply")
    public AjaxResult updateReply(@RequestBody NoticeReply noticeReply)
    {
        if(null != noticeReply.getReplyTime()){
            Map<String, Object> map = new HashMap<>();
            map.put("content", noticeReply.getContent());
            map.put("replyTime", DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", noticeReply.getReplyTime()));
            map.put("noticeCode", noticeReply.getNoticeCode());
            map.put("suppCode", noticeReply.getSuppCode());
            Map<String, String> attachmentMap = new HashMap<>();
            for (TblAttachment attachment : noticeReply.getReplyAttachmentList()){
                attachmentMap.put(attachment.getName(), attachment.getUrl());
            }
            map.put("replyAttachmentMap", attachmentMap);
            TblEvent event = new TblEvent(ServiceEnum.NoticeService, "updateTblNoticeReplyByNoticeCodeMap", map, configService.selectConfigByKey(DataConstants.COMPANY_ID_CONFIG_KEY), noticeReply.getCompCode(), noticeReply.getPlantName());
            tblEventService.insertTblEvent(event);
        }
        return toAjax(tblNoticeService.updateTblNoticeReply(noticeReply));
    }

    @PostMapping("/updateReplyRead")
    public AjaxResult updateReplyRead(@RequestBody NoticeReply noticeReply)
    {
        TblNoticeReply update = new TblNoticeReply();
        update.setReplyId(noticeReply.getReplyId());
        update.setIsRead("Y");
        Map<String, Object> map = new HashMap<>();
        map.put("noticeCode", noticeReply.getNoticeCode());
        map.put("isRead", "Y");
        map.put("suppCode", noticeReply.getSuppCode());
        TblEvent event = new TblEvent(ServiceEnum.NoticeService, "updateTblNoticeReplyByNoticeCodeMap", map, configService.selectConfigByKey(DataConstants.COMPANY_ID_CONFIG_KEY), noticeReply.getCompCode(), noticeReply.getPlantName());
        tblEventService.insertTblEvent(event);
        return toAjax(tblNoticeService.updateTblNoticeReply(update));
    }
}
