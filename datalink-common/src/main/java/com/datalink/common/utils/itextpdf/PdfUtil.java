package com.datalink.common.utils.itextpdf;

import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.utils.uuid.UUID;
import com.itextpdf.barcodes.BarcodeQRCode;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.io.util.StreamUtil;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.utils.PdfMerger;

import java.io.*;
import java.util.List;
import java.util.Map;

public class PdfUtil {

    public static AjaxResult genPdf(List<Map<String, String>> dataMapList, List<Map<String, String>> barcodeMapList, InputStream template, String fileName){
        PdfMerger merger = null;
        fileName = UUID.randomUUID().toString()+"_"+fileName;
        try {
            byte[] temp = StreamUtil.inputStreamToArray(template);
            PdfDocument doc = new PdfDocument(new PdfWriter(getAbsoluteFile(fileName)));
            merger = new PdfMerger(doc);
            for (int i = 0; i < dataMapList.size(); i++){
                byte[] pageData = createPdfPage(dataMapList.get(i), barcodeMapList.get(i), temp);
                PdfDocument page = new PdfDocument(new PdfReader(new ByteArrayInputStream(pageData)));
                merger.merge(page, 1, page.getNumberOfPages());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (null != template){
                    template.close();
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
            if (merger != null){
                merger.close();
            }
        }
        return AjaxResult.success(fileName);
    }

    private static byte[] createPdfPage(Map<String, String > dataMap, Map<String, String> barcodeMap, byte[] template){
        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        PdfDocument doc = null;
        try {
            doc = new PdfDocument(new PdfReader(new ByteArrayInputStream(template)), new PdfWriter(arrayOutputStream));
            PdfAcroForm form = PdfAcroForm.getAcroForm(doc, true);
            for (Map.Entry<String, String> data : dataMap.entrySet()) {
                form.getField(data.getKey()).setValue(data.getValue()).setFontSize(12).setFontSizeAutoScale();
            }
            if (barcodeMap.size() > 0) {
                PdfCanvas canvas = new PdfCanvas(doc.getFirstPage());
                for (Map.Entry<String, String> barcode : barcodeMap.entrySet()) {
                    BarcodeQRCode barcodeQRCode = new BarcodeQRCode(barcode.getValue());
                    Rectangle rectangle = form.getField(barcode.getKey()).getWidgets().get(0).getRectangle().toRectangle();
                    canvas.addXObjectFittedIntoRectangle(barcodeQRCode.createFormXObject(ColorConstants.BLACK, doc), rectangle);
                }
            }
            form.flattenFields();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (doc != null) {
                doc.close();
            }
        }

        return arrayOutputStream.toByteArray();
    }

    /**
     * 获取下载路径
     *
     * @param filename 文件名称
     */
    private static String getAbsoluteFile(String filename)
    {
        String downloadPath = RuoYiConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists())
        {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
}
