<template>
  <el-card shadow="never" class="border-none margin-t24">
    <div slot="header">
      {{ $t('asn.article.materialInfo') }}
    </div>
    <el-table :data="asnArticleList">
      <el-table-column :label="$t('asn.article.orderLineNo')" align="center" prop="orderLineNo" />
      <el-table-column :label="$t('asn.article.articleNo')" align="center" prop="articleNo" />
      <el-table-column :label="$t('asn.article.articleName')" align="center" prop="articleName" />
      <el-table-column :label="$t('asn.article.quantity')" align="center" prop="quantity" >
        <template slot-scope="scope">
          <el-form-item :prop="'items.'+index+'.articles.'+scope.$index+'.quantity'" label-width="0">
            <el-input-number v-model="scope.row.quantity" :min="1" :max="scope.row.quantity" :controls="false" style="width: 100%"></el-input-number>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column :label="$t('asn.article.unit')" align="center" prop="unit" />
      <!-- <el-table-column :label="$t('asn.article.batchNo')" align="center" prop="batchNo" >
        <template slot-scope="scope">
          <el-form-item :prop="'items.'+index+'.articles.'+scope.$index+'.batchNo'" label-width="0">
            <el-input v-model="scope.row.batchNo"></el-input>
          </el-form-item>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('asn.article.qtyPerPack')" align="center" prop="qtyPerPack" >
        <!-- <template slot-scope="scope">
          <el-form-item :prop="'items.'+index+'.articles.'+scope.$index+'.qtyPerPack'" label-width="0">
            <el-input v-model="scope.row.qtyPerPack"></el-input>
          </el-form-item>
        </template> -->
      </el-table-column>
      <el-table-column :label="$t('asn.article.packQty')" align="center" prop="packQty" />
      <!-- <el-table-column :label="$t('asn.article.startWith')" align="center" prop="startWith" >
        <template slot-scope="scope">
          <el-form-item :prop="'items.'+index+'.articles.'+scope.$index+'.startWith'" label-width="0">
            <el-input v-model="scope.row.startWith"></el-input>
          </el-form-item>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('asn.article.endWith')" align="center" prop="endWith" >
        <template slot-scope="scope">
          <el-form-item :prop="'items.'+index+'.articles.'+scope.$index+'.endWith'" label-width="0">
            <el-input v-model="scope.row.endWith"></el-input>
          </el-form-item>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('asn.article.nonStd')" align="center" prop="nonStd" >
        <template slot-scope="scope">
          <el-form-item :prop="'items.'+index+'.articles.'+scope.$index+'.nonStd'" label-width="0">
            <el-input v-model="scope.row.nonStd"></el-input>
          </el-form-item>
        </template>
      </el-table-column> -->
    </el-table>
  </el-card>
</template>

<script>
export default {
  name: "EditableArticleTable",
  props: {
    asnArticleList:Array,
    index: Number
  }
}
</script>

<style scoped>

</style>
