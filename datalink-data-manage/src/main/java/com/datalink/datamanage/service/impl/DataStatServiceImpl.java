package com.datalink.datamanage.service.impl;

import com.datalink.datamanage.domain.DataStat;
import com.datalink.datamanage.mapper.DataStatMapper;
import com.datalink.datamanage.service.IDataStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DataStatServiceImpl implements IDataStatService {

    @Autowired
    private DataStatMapper dataStatMapper;

    /**
     * 查询数据状态
     *
     * @param direction 收发方向
     * @return 数据状态
     */
    @Override
    public List<DataStat> selectDataStats(String direction) {
        return dataStatMapper.selectDataStats(direction);
    }
}
