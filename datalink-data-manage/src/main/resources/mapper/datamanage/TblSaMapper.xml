<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblSaMapper">

    <resultMap type="TblSa" id="TblSaResult">
        <result property="saId"    column="sa_id"    />
        <result property="saCode"    column="sa_code"    />
        <result property="msgType"    column="msg_type"    />
        <result property="docNum"    column="doc_num"    />
        <result property="compCode"    column="comp_code"    />
        <result property="plantCode"    column="plant_code"    />
        <result property="plantName"    column="plant_name"    />
        <result property="plannerNo"    column="planner_no"    />
        <result property="plannerName"    column="planner_name"    />
        <result property="suppCode"    column="supp_code"    />
        <result property="suppName"    column="supp_name"    />
        <result property="poDate"    column="po_date"    />
        <result property="info1"    column="info1"    />
        <result property="info2"    column="info2"    />
        <result property="info3"    column="info3"    />
        <result property="direction"    column="direction"    />
        <result property="kafkaStatus"    column="kafka_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <resultMap id="TblSaTblSaItemResult" type="TblSa" extends="TblSaResult">
        <collection property="detail" notNullColumn="sub_item_id" javaType="java.util.List" resultMap="TblSaItemWithScheduleLineResult" />
    </resultMap>

    <resultMap id="TblSaWithItemResult" type="TblSa" extends="TblSaResult">
        <collection property="detail" ofType="com.datalink.datamanage.domain.TblSaItem" select="selectTblSaItemBySaId" column="sa_id"/>
    </resultMap>

    <resultMap type="TblSaItem" id="TblSaItemResult">
        <result property="itemId"    column="sub_item_id"    />
        <result property="itemNo"    column="sub_item_no"    />
        <result property="articleNo"    column="sub_article_no"    />
        <result property="articleName"    column="sub_article_name"    />
        <result property="articleNameEn"    column="sub_article_name_en"    />
        <result property="unloadingNo"    column="sub_unloading_no"    />
        <result property="plantCode"    column="sub_plant_code"    />
        <result property="lastDeliveryReceivedOrderNo"    column="sub_last_delivery_received_order_no"    />
        <result property="lastDeliveryReceivedQuantity"    column="sub_last_delivery_received_quantity"    />
        <result property="lastDeliveryDate"    column="sub_last_delivery_date"    />
        <result property="cumulativeQuantityOrdered"    column="sub_cumulative_quantity_ordered"    />
        <result property="cumulativeQuantityReceived"    column="sub_cumulative_quantity_received"    />
        <result property="unit"    column="sub_unit"    />
        <result property="productionGoAhead"    column="sub_production_go_ahead"    />
        <result property="materialGoAhead"    column="sub_material_go_ahead"    />
        <result property="createDate"    column="sub_create_date"    />
        <result property="version"    column="sub_version"    />
        <result property="lastVersion"    column="sub_last_version"    />
        <result property="lastVersionReleaseDate"    column="sub_last_version_release_date"    />
        <result property="workbinNo"    column="sub_workbin_no"    />
        <result property="qtyPerPack"    column="sub_qty_per_pack"    />
        <result property="motorcycleType"    column="sub_motorcycle_type"    />
        <result property="attribute"    column="sub_attribute"    />
        <result property="productionConsumptionCycle"    column="sub_production_consumption_cycle"    />
        <result property="receivingWarrantyPeriod"    column="sub_receiving_warranty_period"    />
        <result property="info1"    column="sub_info1"    />
        <result property="info2"    column="sub_info2"    />
        <result property="info3"    column="sub_info3"    />
        <result property="saId"    column="sub_sa_id"    />
        <result property="createBy"    column="sub_create_by"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateBy"    column="sub_update_by"    />
        <result property="updateTime"    column="sub_update_time"    />
        <result property="remark"    column="sub_remark"    />
    </resultMap>

    <resultMap id="TblSaItemWithScheduleLineResult" type="TblSaItem" extends="TblSaItemResult">
        <collection property="scheduleLines" ofType="com.datalink.datamanage.domain.TblSaScheduleLine" select="selectTblSaScheduleLineByItemId" column="sub_item_id"/>
    </resultMap>

    <resultMap type="TblSaScheduleLine" id="TblSaScheduleLineResult">
        <result property="lineId"    column="sub_sub_line_id"    />
        <result property="dateType"    column="sub_sub_date_type"    />
        <result property="dateFrom"    column="sub_sub_date_from"    />
        <result property="dateTo"    column="sub_sub_date_to"    />
        <result property="releasedQuantity"    column="sub_sub_released_quantity"    />
        <result property="cumulativeReceivedQuantity"    column="sub_sub_cumulative_received_quantity"    />
        <result property="info1"    column="sub_sub_info1"    />
        <result property="info2"    column="sub_sub_info2"    />
        <result property="info3"    column="sub_sub_info3"    />
        <result property="createBy"    column="sub_sub_create_by"    />
        <result property="itemId"    column="sub_sub_item_id"    />
        <result property="createTime"    column="sub_sub_create_time"    />
        <result property="updateBy"    column="sub_sub_update_by"    />
        <result property="updateTime"    column="sub_sub_update_time"    />
        <result property="remark"    column="sub_sub_remark"    />
    </resultMap>

    <sql id="selectTblSaVo">
        select sa_id, sa_code, msg_type, doc_num, comp_code, plant_code, plant_name, planner_no, planner_name, supp_code, supp_name, po_date, info1, info2, info3, direction, kafka_status, create_by, create_time, update_by, update_time, remark from tbl_sa
    </sql>

    <sql id="selectTblSaItemVo">
        select item_id as sub_item_id, item_no as sub_item_no, article_no as sub_article_no, article_name as sub_article_name, article_name_en as sub_article_name_en, unloading_no as sub_unloading_no, plant_code as sub_plant_code, last_delivery_received_order_no as sub_last_delivery_received_order_no, last_delivery_received_quantity as sub_last_delivery_received_quantity, last_delivery_date as sub_last_delivery_date, cumulative_quantity_ordered as sub_cumulative_quantity_ordered, cumulative_quantity_received as sub_cumulative_quantity_received, unit as sub_unit, production_go_ahead as sub_production_go_ahead, material_go_ahead as sub_material_go_ahead, create_date as sub_create_date, version as sub_version, last_version as sub_last_version, last_version_release_date as sub_last_version_release_date, workbin_no as sub_workbin_no, qty_per_pack as sub_qty_per_pack, motorcycle_type as sub_motorcycle_type, attribute as sub_attribute, production_consumption_cycle as sub_production_consumption_cycle, receiving_warranty_period as sub_receiving_warranty_period, info1 as sub_info1, info2 as sub_info2, info3 as sub_info3, sa_id as sub_sa_id, create_by as sub_create_by, create_time as sub_create_time, update_by as sub_update_by, update_time as sub_update_time, remark as sub_remark from tbl_sa_item
    </sql>

    <sql id="selectTblSaScheduleLineVo">
        select line_id as sub_sub_line_id, date_type as sub_sub_date_type, date_from as sub_sub_date_from, date_to as sub_sub_date_to, released_quantity as sub_sub_released_quantity, cumulative_received_quantity as sub_sub_cumulative_received_quantity, info1 as sub_sub_info1, info2 as sub_sub_info2, info3 as sub_sub_info3, create_by as sub_sub_create_by, item_id as sub_sub_item_id, create_time as sub_sub_create_time, update_by as sub_sub_update_by, update_time as sub_sub_update_time, remark as sub_sub_remark from tbl_sa_schedule_line
    </sql>

    <select id="selectTblSaList" parameterType="TblSa" resultMap="TblSaResult">
        <include refid="selectTblSaVo"/>
        <where>
            <if test="saCode != null  and saCode != ''"> and sa_code = #{saCode}</if>
            <if test="msgType != null  and msgType != ''"> and msg_type = #{msgType}</if>
            <if test="docNum != null "> and doc_num = #{docNum}</if>
            <if test="compCode != null  and compCode != ''"> and comp_code = #{compCode}</if>
            <if test="plantCode != null  and plantCode != ''"> and plant_code = #{plantCode}</if>
            <if test="plantName != null  and plantName != ''"> and plant_name like concat('%', #{plantName}, '%')</if>
            <if test="plannerNo != null  and plannerNo != ''"> and planner_no = #{plannerNo}</if>
            <if test="plannerName != null  and plannerName != ''"> and planner_name like concat('%', #{plannerName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and supp_code = #{suppCode}</if>
            <if test="suppName != null  and suppName != ''"> and supp_name like concat('%', #{suppName}, '%')</if>
            <if test="poDate != null "> and po_date = #{poDate}</if>
            <if test="info1 != null  and info1 != ''"> and info1 = #{info1}</if>
            <if test="info2 != null  and info2 != ''"> and info2 = #{info2}</if>
            <if test="info3 != null  and info3 != ''"> and info3 = #{info3}</if>
            <if test="direction != null  and direction != ''"> and direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and kafka_status = #{kafkaStatus}</if>
        </where>
    </select>

    <select id="selectTblSaById" parameterType="Long" resultMap="TblSaTblSaItemResult">
        select a.sa_id, a.sa_code, a.msg_type, a.doc_num, a.comp_code, a.plant_code, a.plant_name, a.planner_no, a.planner_name, a.supp_code, a.supp_name, a.po_date, a.info1, a.info2, a.info3, a.direction, a.kafka_status, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
            b.item_id as sub_item_id, b.item_no as sub_item_no, b.article_no as sub_article_no, b.article_name as sub_article_name, b.article_name_en as sub_article_name_en, b.unloading_no as sub_unloading_no, b.plant_code as sub_plant_code, b.last_delivery_received_order_no as sub_last_delivery_received_order_no, b.last_delivery_received_quantity as sub_last_delivery_received_quantity, b.last_delivery_date as sub_last_delivery_date, b.cumulative_quantity_ordered as sub_cumulative_quantity_ordered, b.cumulative_quantity_received as sub_cumulative_quantity_received, b.unit as sub_unit, b.production_go_ahead as sub_production_go_ahead, b.material_go_ahead as sub_material_go_ahead, b.create_date as sub_create_date, b.version as sub_version, b.last_version as sub_last_version, b.last_version_release_date as sub_last_version_release_date, b.workbin_no as sub_workbin_no, b.qty_per_pack as sub_qty_per_pack, b.motorcycle_type as sub_motorcycle_type, b.attribute as sub_attribute, b.production_consumption_cycle as sub_production_consumption_cycle, b.receiving_warranty_period as sub_receiving_warranty_period, b.info1 as sub_info1, b.info2 as sub_info2, b.info3 as sub_info3, b.sa_id as sub_sa_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark
        from tbl_sa a
        left join tbl_sa_item b on b.sa_id = a.sa_id
        where a.sa_id = #{saId}
    </select>

    <select id="selectTblSaWithItemList" parameterType="TblSa" resultMap="TblSaTblSaItemResult">
        select a.sa_id, a.sa_code, a.msg_type, a.doc_num, a.comp_code, a.plant_code, a.plant_name, a.planner_no, a.planner_name, a.supp_code, a.supp_name, a.po_date, a.info1, a.info2, a.info3, a.direction, a.kafka_status, a.create_by, a.create_time, a.update_by, a.update_time, a.remark,
               b.item_id as sub_item_id, b.item_no as sub_item_no, b.article_no as sub_article_no, b.article_name as sub_article_name, b.article_name_en as sub_article_name_en, b.unloading_no as sub_unloading_no, b.plant_code as sub_plant_code, b.last_delivery_received_order_no as sub_last_delivery_received_order_no, b.last_delivery_received_quantity as sub_last_delivery_received_quantity, b.last_delivery_date as sub_last_delivery_date, b.cumulative_quantity_ordered as sub_cumulative_quantity_ordered, b.cumulative_quantity_received as sub_cumulative_quantity_received, b.unit as sub_unit, b.production_go_ahead as sub_production_go_ahead, b.material_go_ahead as sub_material_go_ahead, b.create_date as sub_create_date, b.version as sub_version, b.last_version as sub_last_version, b.last_version_release_date as sub_last_version_release_date, b.workbin_no as sub_workbin_no, b.qty_per_pack as sub_qty_per_pack, b.motorcycle_type as sub_motorcycle_type, b.attribute as sub_attribute, b.production_consumption_cycle as sub_production_consumption_cycle, b.receiving_warranty_period as sub_receiving_warranty_period, b.info1 as sub_info1, b.info2 as sub_info2, b.info3 as sub_info3, b.sa_id as sub_sa_id, b.create_by as sub_create_by, b.create_time as sub_create_time, b.update_by as sub_update_by, b.update_time as sub_update_time, b.remark as sub_remark
        from tbl_sa a
                 left join tbl_sa_item b on b.sa_id = a.sa_id
        <where>
            <if test="saCode != null  and saCode != ''"> and a.sa_code = #{saCode}</if>
            <if test="msgType != null  and msgType != ''"> and a.msg_type = #{msgType}</if>
            <if test="docNum != null "> and a.doc_num = #{docNum}</if>
            <if test="compCode != null  and compCode != ''"> and a.comp_code = #{compCode}</if>
            <if test="plantCode != null  and plantCode != ''"> and a.plant_code = #{plantCode}</if>
            <if test="plantName != null  and plantName != ''"> and a.plant_name like concat('%', #{plantName}, '%')</if>
            <if test="plannerNo != null  and plannerNo != ''"> and a.planner_no = #{plannerNo}</if>
            <if test="plannerName != null  and plannerName != ''"> and a.planner_name like concat('%', #{plannerName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and a.supp_code = #{suppCode}</if>
            <if test="suppName != null  and suppName != ''"> and a.supp_name like concat('%', #{suppName}, '%')</if>
            <if test="poDate != null "> and po_date = #{poDate}</if>
            <if test="info1 != null  and info1 != ''"> and a.info1 = #{info1}</if>
            <if test="info2 != null  and info2 != ''"> and a.info2 = #{info2}</if>
            <if test="info3 != null  and info3 != ''"> and a.info3 = #{info3}</if>
            <if test="direction != null  and direction != ''"> and a.direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and a.kafka_status = #{kafkaStatus}</if>
        </where>
    </select>

    <select id="selectTblSaItemBySaId" parameterType="Long" resultMap="TblSaItemWithScheduleLineResult">
        <include refid="selectTblSaItemVo"/>
        where sa_id = #{sa_id}
    </select>

    <select id="selectTblSaScheduleLineByItemId" parameterType="Long" resultMap="TblSaScheduleLineResult">
        <include refid="selectTblSaScheduleLineVo"/>
        where item_id = #{item_id}
    </select>

    <select id="selectTblSaFullList" parameterType="TblSa" resultMap="TblSaWithItemResult">
        <include refid="selectTblSaVo"/>
        <where>
            direction = 'I'
            <if test="params.cursor !=null">and sa_id > #{params.cursor}</if>
            <if test="params.cursorInclude !=null">and sa_id >= #{params.cursorInclude}</if>
            <if test="params.time !=null">and create_time > #{params.time}</if>
            <if test="params.timeInclude !=null">and create_time >= #{params.timeInclude}</if>
        </where>
        Order by sa_id asc
        <if test="params.limit !=null and params.limit !=''">limit #{params.limit}</if>
    </select>

    <insert id="insertTblSa" parameterType="TblSa" useGeneratedKeys="true" keyProperty="saId">
        insert into tbl_sa
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="saCode != null and saCode != ''">sa_code,</if>
            <if test="msgType != null">msg_type,</if>
            <if test="docNum != null">doc_num,</if>
            <if test="compCode != null and compCode != ''">comp_code,</if>
            <if test="plantCode != null and plantCode != ''">plant_code,</if>
            <if test="plantName != null and plantName != ''">plant_name,</if>
            <if test="plannerNo != null and plannerNo != ''">planner_no,</if>
            <if test="plannerName != null and plannerName != ''">planner_name,</if>
            <if test="suppCode != null and suppCode != ''">supp_code,</if>
            <if test="suppName != null and suppName != ''">supp_name,</if>
            <if test="poDate != null">po_date,</if>
            <if test="info1 != null">info1,</if>
            <if test="info2 != null">info2,</if>
            <if test="info3 != null">info3,</if>
            <if test="direction != null and direction != ''">direction,</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">kafka_status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="saCode != null and saCode != ''">#{saCode},</if>
            <if test="msgType != null">#{msgType},</if>
            <if test="docNum != null">#{docNum},</if>
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="plantCode != null and plantCode != ''">#{plantCode},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="plannerNo != null and plannerNo != ''">#{plannerNo},</if>
            <if test="plannerName != null and plannerName != ''">#{plannerName},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="poDate != null">#{poDate},</if>
            <if test="info1 != null">#{info1},</if>
            <if test="info2 != null">#{info2},</if>
            <if test="info3 != null">#{info3},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">#{kafkaStatus},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTblSa" parameterType="TblSa">
        update tbl_sa
        <trim prefix="SET" suffixOverrides=",">
            <if test="saCode != null and saCode != ''">sa_code = #{saCode},</if>
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="docNum != null">doc_num = #{docNum},</if>
            <if test="compCode != null and compCode != ''">comp_code = #{compCode},</if>
            <if test="plantCode != null and plantCode != ''">plant_code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">plant_name = #{plantName},</if>
            <if test="plannerNo != null and plannerNo != ''">planner_no = #{plannerNo},</if>
            <if test="plannerName != null and plannerName != ''">planner_name = #{plannerName},</if>
            <if test="suppCode != null and suppCode != ''">supp_code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">supp_name = #{suppName},</if>
            <if test="poDate != null">po_date = #{poDate},</if>
            <if test="info1 != null">info1 = #{info1},</if>
            <if test="info2 != null">info2 = #{info2},</if>
            <if test="info3 != null">info3 = #{info3},</if>
            <if test="direction != null and direction != ''">direction = #{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">kafka_status = #{kafkaStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sa_id = #{saId}
    </update>

    <delete id="deleteTblSaById" parameterType="Long">
        delete from tbl_sa where sa_id = #{saId}
    </delete>

    <delete id="deleteTblSaByIds" parameterType="String">
        delete from tbl_sa where sa_id in
        <foreach item="saId" collection="array" open="(" separator="," close=")">
            #{saId}
        </foreach>
    </delete>

    <delete id="deleteTblSaItemBySaIds" parameterType="String">
        delete from tbl_sa_item where sa_id in
        <foreach item="saId" collection="array" open="(" separator="," close=")">
            #{saId}
        </foreach>
    </delete>

    <delete id="deleteTblSaItemBySaId" parameterType="Long">
        delete from tbl_sa_item where sa_id = #{saId}
    </delete>

    <insert id="batchTblSaItem" useGeneratedKeys="true" keyProperty="itemId" keyColumn="item_id">
        insert into tbl_sa_item( item_no, article_no, article_name, article_name_en, unloading_no, plant_code, last_delivery_received_order_no, last_delivery_received_quantity, last_delivery_date, cumulative_quantity_ordered, cumulative_quantity_received, unit, production_go_ahead, material_go_ahead, create_date, version, last_version, last_version_release_date, workbin_no, qty_per_pack, motorcycle_type, attribute, production_consumption_cycle, receiving_warranty_period, info1, info2, info3, sa_id, create_by, create_time, update_by, update_time, remark) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.itemNo}, #{item.articleNo}, #{item.articleName}, #{item.articleNameEn}, #{item.unloadingNo}, #{item.plantCode}, #{item.lastDeliveryReceivedOrderNo}, #{item.lastDeliveryReceivedQuantity}, #{item.lastDeliveryDate}, #{item.cumulativeQuantityOrdered}, #{item.cumulativeQuantityReceived}, #{item.unit}, #{item.productionGoAhead}, #{item.materialGoAhead}, #{item.createDate}, #{item.version}, #{item.lastVersion}, #{item.lastVersionReleaseDate}, #{item.workbinNo}, #{item.qtyPerPack}, #{item.motorcycleType}, #{item.attribute}, #{item.productionConsumptionCycle}, #{item.receivingWarrantyPeriod}, #{item.info1}, #{item.info2}, #{item.info3}, #{item.saId}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <insert id="batchTblSaScheduleLine" useGeneratedKeys="true" keyProperty="lineId" keyColumn="line_id">
        insert into tbl_sa_schedule_line( date_type, date_from, date_to, released_quantity, cumulative_received_quantity, info1, info2, info3, create_by, item_id, create_time, update_by, update_time, remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.dateType}, #{item.dateFrom}, #{item.dateTo}, #{item.releasedQuantity}, #{item.cumulativeReceivedQuantity}, #{item.info1}, #{item.info2}, #{item.info3}, #{item.createBy}, #{item.itemId}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <delete id="deleteTblSaScheduleLineByItemIds" parameterType="String">
        delete from tbl_sa_schedule_line where item_id in
        <foreach item="itemId" collection="array" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </delete>

    <select id="selectLastId" resultType="Long">
        select max(sa_id) from tbl_sa;
    </select>
</mapper>
