<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="工厂间搬送指示书" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" isTitleNewPage="true" uuid="226593c4-a077-4088-a9fe-bac6d41aa2dc">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="compCode" class="java.lang.String"/>
	<parameter name="compName" class="java.lang.String"/>
	<parameter name="deliveryDate" class="java.lang.String"/>
	<parameter name="deliveryTime" class="java.lang.String"/>
	<parameter name="asnCode" class="java.lang.String"/>
	<parameter name="suppCode" class="java.lang.String"/>
	<parameter name="depot" class="java.lang.String"/>
	<parameter name="createDate" class="java.lang.String"/>
	<parameter name="issueTime" class="java.lang.String"/>
	<parameter name="suppName" class="java.lang.String"/>
	<parameter name="plantCode" class="java.lang.String"/>
	<parameter name="unloadingName" class="java.lang.String"/>
	<parameter name="unloadingNo" class="java.lang.String"/>
	<parameter name="qrCode" class="java.lang.String"/>
	<parameter name="deliveryDateTime" class="java.lang.String"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="lineNo" class="java.lang.String"/>
	<field name="articleNo" class="java.lang.String"/>
	<field name="unit" class="java.lang.String"/>
	<field name="inclusionInstructionQuantity" class="java.math.BigDecimal"/>
	<field name="inclusionInstructionWeight" class="java.math.BigDecimal"/>
	<field name="issueNo" class="java.lang.String"/>
	<field name="accountingReason" class="java.lang.String"/>
	<field name="accountingArea" class="java.lang.String"/>
	<field name="unitCondition" class="java.lang.String"/>
	<field name="conversionFactor" class="java.math.BigDecimal"/>
	<field name="quantity" class="java.math.BigDecimal"/>
	<field name="inclusionWeight" class="java.math.BigDecimal"/>
	<field name="orderCode" class="java.lang.String"/>
	<field name="packQty" class="java.math.BigDecimal"/>
	<field name="qtyPerPack" class="java.math.BigDecimal"/>
	<variable name="sumContainerCount" class="java.lang.Integer" calculation="Sum">
		<variableExpression><![CDATA[$F{packQty}]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="75" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="321" y="0" width="160" height="25" uuid="9125ae40-718a-4367-ba49-f51f658e77a5">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<bottomPen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="16" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[ピッキングリスト]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="40" height="15" uuid="ca25f09a-7bf2-4583-b44a-b0f01a607995">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[発注会社]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="15" width="40" height="15" uuid="06bfbcb1-01d2-4c17-b5f2-6c7e4d351688">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[納入先名]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="31" width="40" height="15" uuid="5632ea87-f013-424e-9d07-c984eb3571de">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[要求工場]]></text>
			</staticText>
			<staticText>
				<reportElement x="110" y="31" width="40" height="15" uuid="70033ea5-89fc-4009-b95f-1a74c31b11db">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[納入場所]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="0" width="30" height="15" uuid="e1f5905a-97e1-4e8b-acaa-144f3bc59445">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[発行日]]></text>
			</staticText>
			<staticText>
				<reportElement x="727" y="0" width="30" height="15" uuid="c0e93a04-38cb-4959-bdef-ba7092a6c314">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[Page:]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="15" width="30" height="15" uuid="5a0dcbe8-354d-4edc-81e5-e721ae17bdb7">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[取引先]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="761" y="0" width="20" height="15" uuid="efd6a521-5ab8-4baf-9e8c-829d10ac0476">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report" isBlankWhenNull="true">
				<reportElement x="781" y="0" width="20" height="15" uuid="8e56df2d-60d4-4f1d-9e0e-166bbf007fe7">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA["/ " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="303" y="28" width="67" height="30" uuid="5c3f503e-e42d-4b2b-9f06-b52a6961b0db">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.75"/>
					<topPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<text><![CDATA[納入指示日]]></text>
			</staticText>
			<staticText>
				<reportElement x="428" y="28" width="35" height="30" uuid="16b19de0-2017-4521-94c0-a7d91377843c">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12"/>
				</textElement>
				<text><![CDATA[時刻]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="0" width="160" height="15" uuid="0dd72cc2-9ce6-4cde-a830-37188f2e322e">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{compName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="15" width="50" height="15" uuid="679dbab4-57d7-47d8-9353-575dd2b3f526">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{unloadingName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="50" y="31" width="50" height="15" uuid="f352d41f-70ea-4ffc-8de8-6c22407857dc">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{plantCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="160" y="31" width="50" height="15" uuid="4b2aaa4c-c3fe-4c12-9a96-d256b2b17f12">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{unloadingNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="663" y="0" width="60" height="15" uuid="87ccf13e-0aa3-43fe-a99d-524f7a39ad9a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{createDate}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="663" y="15" width="60" height="15" uuid="74511c84-ff1c-4eed-b2eb-861b5072f6e4">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{suppCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="370" y="28" width="58" height="30" uuid="5d8e7fe9-375a-45fe-9499-c253ebd12db0">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.75"/>
					<topPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{deliveryDate}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="761" y="15" width="40" height="15" uuid="*************-41ab-a47e-b90da9192d19">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{depot}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="727" y="15" width="30" height="15" uuid="925d8d7d-2ba0-4753-b882-ec3fe491ea19">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[デポ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="630" y="30" width="171" height="15" uuid="479e1f7e-3a5a-4da2-af45-fb59bec16a5e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{suppName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="463" y="28" width="36" height="30" uuid="116349a7-a8fd-4b2d-a369-5c4b2dda9d80">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.75"/>
					<topPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.75" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{deliveryTime}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="40" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
			<staticText>
				<reportElement x="0" y="0" width="24" height="40" uuid="be3171c5-1854-4086-88da-c653cf43cde1">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.3"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[No.]]></text>
			</staticText>
			<staticText>
				<reportElement x="24" y="0" width="79" height="40" uuid="210f8621-64d8-4977-8b68-713f6013748c"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[発行No.]]></text>
			</staticText>
			<staticText>
				<reportElement x="103" y="0" width="196" height="40" uuid="eacbbea9-a9a4-4956-8de2-169045ececa9">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="2.5"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[部品番号／原材料コード]]></text>
			</staticText>
			<staticText>
				<reportElement x="299" y="0" width="79" height="40" uuid="c4773062-a19e-45a4-8065-eedcedf3df19"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[納入指示数]]></text>
			</staticText>
			<staticText>
				<reportElement x="378" y="0" width="79" height="40" uuid="8059f18c-f421-4ea3-9aa0-e46e7a4f2032"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[出荷実績数]]></text>
			</staticText>
			<staticText>
				<reportElement x="457" y="0" width="27" height="40" uuid="8c2eeb90-a623-4e58-a2e9-11d05bc316fd">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.35"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[発注単位]]></text>
			</staticText>
			<staticText>
				<reportElement x="484" y="0" width="15" height="40" uuid="a2ccb99c-aab8-416e-84e8-c7c4ed7813b4">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.2"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[条件]]></text>
			</staticText>
			<staticText>
				<reportElement x="499" y="0" width="93" height="40" uuid="d0fdcf63-40ea-4108-8a70-19869377148a">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[換算重量]]></text>
			</staticText>
			<staticText>
				<reportElement x="592" y="0" width="78" height="40" uuid="a2c4b808-11b0-49d1-b3d5-56b157089208">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[容器数]]></text>
			</staticText>
			<staticText>
				<reportElement x="670" y="0" width="78" height="40" uuid="a2c4b808-11b0-49d1-b3d5-56b157089208"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[ＳＮＥＰ]]></text>
			</staticText>
			<staticText>
				<reportElement x="748" y="0" width="54" height="40" uuid="292ef770-d92a-4051-add0-6fd804d8c91e">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.7"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[荷姿コード]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="21" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.grid.JSSGridBagLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="24" height="21" uuid="71cf289e-96e4-453b-91c0-d4343c9103f9">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.3"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box topPadding="0" leftPadding="2" bottomPadding="0" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="24" y="0" width="79" height="21" uuid="f4d03cc8-ae62-4eb6-a042-7d62dc4ca2c3"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="103" y="0" width="196" height="21" uuid="12c0ea86-1204-4e9e-aa3f-56336ef5a96b">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="2.5"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{articleNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="299" y="0" width="79" height="21" uuid="0a823a6e-52fe-4c86-b103-27550f8feb39"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantity}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="378" y="0" width="79" height="21" uuid="3d146d48-816b-4cad-ac2d-24fe7302e024"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="457" y="0" width="27" height="21" uuid="fc63aa2e-c14a-4695-b326-************">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.35"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unit}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="484" y="0" width="15" height="21" uuid="146edd19-5b68-4196-ba9d-1d2db336c4c5">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.2"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="499" y="0" width="93" height="21" uuid="b572a147-3153-44ab-8366-ba1effd79fbb">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.2"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inclusionWeight}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="592" y="0" width="78" height="21" uuid="e182e810-9e02-4336-88cb-601040ad3af4">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{packQty}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="670" y="0" width="78" height="21" uuid="6c30eb44-49fb-471d-90d5-29bd123c6d1f"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{qtyPerPack}.setScale($F{unit} != null && (Arrays.asList("G", "KG", "TON").contains($F{unit}.toUpperCase())) ? 2 : 0, RoundingMode.HALF_UP)]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="748" y="0" width="54" height="21" uuid="ef02a08d-5037-408f-83a4-2a633264fb63">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.7"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
					<property name="com.jaspersoft.studio.unit.height" value="pixel"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="20" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField isBlankWhenNull="true">
				<reportElement x="0" y="0" width="24" height="20" uuid="b3e840f5-f51b-4664-b427-1c6fc980b577">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.3"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box topPadding="0" leftPadding="2" bottomPadding="0" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="24" y="0" width="79" height="20" uuid="92cdadc0-cba3-406d-9992-2f6a11407614"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="103" y="0" width="196" height="20" uuid="f87f0247-1ea4-4203-a12b-d5c9724f2655">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="2.5"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="299" y="0" width="79" height="20" uuid="0e0f2b2e-d80a-41d3-b5d0-3c10e0447fd0"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="378" y="0" width="79" height="20" uuid="042a8250-52c6-4ccb-84dd-56d7bd4a8663"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="457" y="0" width="27" height="20" uuid="0133f80e-085f-49a1-a813-f5691b4eb338">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.35"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="592" y="0" width="78" height="20" uuid="ce7df0e7-5426-4a66-8091-d4226f3a7540">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{sumContainerCount}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="670" y="0" width="78" height="20" uuid="a7514515-085b-4e20-96cd-4473fb37c898"/>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="748" y="0" width="54" height="20" uuid="ff1cddf0-17b0-4f32-a72f-e0fd0b732636">
					<property name="com.jaspersoft.layout.grid.x" value="-1"/>
					<property name="com.jaspersoft.layout.grid.y" value="-1"/>
					<property name="com.jaspersoft.layout.grid.weight.x" value="0.7"/>
					<property name="com.jaspersoft.layout.grid.weight.y" value="1.0"/>
					<property name="com.jaspersoft.layout.grid.rowspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.colspan" value="1"/>
					<property name="com.jaspersoft.layout.grid.weight.fixed" value="false"/>
				</reportElement>
				<box leftPadding="2" rightPadding="2">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
			</textField>
			<staticText>
				<reportElement x="484" y="0" width="15" height="20" uuid="a2cf277a-1e1a-4a7c-957c-292ce49b5161"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="499" y="0" width="93" height="20" uuid="9514f7d3-8c98-4b7a-ba35-db9ff2e2b190"/>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑"/>
				</textElement>
				<text><![CDATA[容器合計数]]></text>
			</staticText>
		</band>
	</columnFooter>
</jasperReport>
