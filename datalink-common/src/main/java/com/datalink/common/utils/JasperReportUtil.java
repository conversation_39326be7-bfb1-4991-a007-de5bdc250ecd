package com.datalink.common.utils;

import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public class JasperReportUtil {

    private static final Logger logger = LoggerFactory.getLogger(JasperReportUtil.class);

    private static String getContentType(ReportType type) {
        String contentType;
        switch (type) {
            case PDF:
                contentType = "application/pdf";
                break;
            case XLS:
                contentType = "application/vnd.ms-excel";
                break;
            case XLSX:
                contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                break;
            case XML:
                contentType = "text/xml";
                break;
            case RTF:
                contentType = "application/rtf";
                break;
            case CSV:
                contentType = "text/plain";
                break;
            case DOC:
                contentType = "application/msword";
                break;
            case HTML:
            default:
                contentType = "text/html;charset=utf-8";
        }
        return contentType;
    }

    static JasperPrint getJasperPrint(JasperReport jasperReport, Map parameters, List<?> list) throws JRException {
        JRDataSource dataSource = null;
        if (null == list || list.size() == 0) {
            dataSource = new JREmptyDataSource();
        } else {
            dataSource = new JRBeanCollectionDataSource(list);
        }
        JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
        return jasperPrint;
    }

    public static String exportToPdf(String jasperPath, String destDir, Map parameters, List<?> list) throws Exception {
        try {
            String fileName = UUID.randomUUID() + ".pdf";
            JasperReport jasperReport = JasperCompileManager.compileReport(jasperPath);
            JasperPrint jasperPrint = getJasperPrint(jasperReport, parameters, list);
            JasperExportManager.exportReportToPdfFile(jasperPrint, destDir + fileName);
            return fileName;
        } catch (Exception e) {
            logger.error("读取报表异常", e);
            throw e;
        }
    }


    public enum ReportType {
        HTML,
        PDF,
        XLS,
        XLSX,
        XML,
        RTF,
        CSV,
        DOC
    }
}
