package com.datalink.api.domain;

import com.datalink.datamanage.domain.TblOrder;
import com.datalink.datamanage.domain.TblOrderItem;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

@JsonNaming(PropertyNamingStrategy.LowerCaseStrategy.class)
public class TblApiOrder extends TblOrder {
    /** 订单行项目信息 */
//    private List<TblApiOrderItem> items;
}
