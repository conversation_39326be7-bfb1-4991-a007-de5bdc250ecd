<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="发布公司代码" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          placeholder="请输入发布公司代码"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告标题" prop="noticeTitle">
        <el-input
          v-model="queryParams.noticeTitle"
          placeholder="请输入公告标题"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker
          v-model="daterangePublishTime"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="回复时间">
        <el-date-picker
          v-model="daterangeReplyTime"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="是否回复" prop="isReply">
        <el-select v-model="queryParams.params.isReply" placeholder="是否回复" clearable size="small">
          <el-option
            v-for="dict in yesNoOption"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="replyList">
      <el-table-column label="发布公司代码" align="center" prop="compCode" />
      <el-table-column label="公告标题" align="center" prop="noticeTitle" />
      <el-table-column label="发布时间" align="center" prop="publishTime" width="180">
        <!--        <template slot-scope="scope">-->
        <!--          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>-->
        <!--        </template>-->
      </el-table-column>
      <el-table-column label="回复时间" align="center" prop="replyTime" width="180">
        <!--        <template slot-scope="scope">-->
        <!--          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>-->
        <!--        </template>-->
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleView(scope.row)"
            v-hasPermi="['datamanage:notice:list']"
          >查看公告</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-card shadow="never" class="border-none margin-t24">
        <div slot="header">
          {{ reply.noticeTitle }}
        </div>
        <p v-html="reply.noticeContent" style="height:300px;width:100%;overflow: auto"/>
        <p v-if="reply.attachmentList && reply.attachmentList.length>0">附件</p>
        <attachment-upload v-model="reply.attachmentList" readonly ref="attach"/>
      </el-card>
      <el-form ref="form" :model="form" :rules="rules" label-position="top" :disabled="null != reply.replyTime" v-if="'Y'===reply.needReply">
        <el-form-item label="回复" prop="content">
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
            v-model="form.content">
          </el-input>
        </el-form-item>
        <el-form-item label="附件">
          <attachment-upload v-model="form.tempAttachmentList" :readonly="null != reply.replyTime" ref="replyAttach"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm(false)" v-if="'Y'===reply.needReply && null == reply.replyTime">保存草稿</el-button>
        <el-button type="primary" @click="submitForm(true)" v-if="'Y'===reply.needReply && null == reply.replyTime">发送回复</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listNoticeReply, updateReply, updateReplyRead} from "@/api/datamanage/notice";
import AttachmentUpload from "@/components/AttachmentUpload/index.vue";
import {listAttachment} from "@/api/datamanage/attachment";

export default {
  name: "notice_reply",
  components:{
    AttachmentUpload
  },
  data(){
    return {

      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告回复表格数据
      replyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 创建时间时间范围
      daterangeCreateTime: [],
      // 回复时间时间范围
      daterangeReplyTime: [],
      // 更新时间时间范围
      daterangePublishTime: [],
      yesNoOption:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        suppCode: null,
        suppName: null,
        noticeId: null,
        createTime: null,
        replyTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        direction: 'I',
        kafkaStatus: null,
        publishTime: null,
        params:{isReply:null},
        orderByColumn: 'publishTime',
        isAsc: 'desc',
      },
      // 表单参数
      form: {},
      reply:{},
      // 表单校验
      rules: {
        content: [
          { required: true, message: "回复内容不能为空", trigger: "blur" }
        ]
      }
    };

  },
  created() {
    this.getList();
    this.getDicts("sys_yes_no").then(response=>{
      this.yesNoOption = response.data;
    });
  },
  methods:{
    getList(){
      this.loading = true;
      if (null != this.daterangeReplyTime && '' != this.daterangeReplyTime) {
        this.queryParams.params["beginReplyTime"] = this.daterangeReplyTime[0];
        this.queryParams.params["endReplyTime"] = this.daterangeReplyTime[1];
      }
      if (null != this.daterangePublishTime && '' != this.daterangePublishTime) {
        this.queryParams.params["beginPublishTime"] = this.daterangePublishTime[0];
        this.queryParams.params["endPublishTime"] = this.daterangePublishTime[1];
      }
      listNoticeReply(this.queryParams).then(response => {
        this.replyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleView(row){
      this.reply = row;
      this.form = {
        replyId:row.replyId,
        content: row.content,
        compCode: row.compCode,
        plantName: row.plantName,
        noticeCode: row.noticeCode,
        suppCode: row.suppCode,
      };
      listAttachment({parent:this.reply.replyId, type:"NoticeReply"}).then(response=>{
        this.form.tempAttachmentList = response.rows;
        if (this.$refs.replyAttach){
          this.$refs.replyAttach.fileList =response.rows;
        }
      });
      if ("Y" !== row.isRead){
        updateReplyRead(this.form).then(response=>{
          row.isRead = true;
        });

      }
      listAttachment({parent:this.reply.noticeId, type:"Notice"}).then(response=>{
        this.reply.attachmentList = response.rows;
        if (this.$refs.attach){
          this.$refs.attach.fileList =response.rows;
        }
      });
      this.open = true;
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.params={isReply: null};
      this.daterangeReplyTime = [];
      this.daterangePublishTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    submitForm(send){
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (send) {
            this.form.replyTime = this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}');
            this.$confirm("发送回复后无法修改，请确认", "警告", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }).then(() => {
              this.saveReply(true);
            });
          } else {
            this.saveReply(false);
          }

        }
      });
    },
    saveReply(send){
      this.form.replyAttachmentList = [];
      for (let attach of this.form.tempAttachmentList){
        this.form.replyAttachmentList.push({name: attach.name, url: attach.url.replace(process.env.VUE_APP_BASE_API,'')});
        // attach.url = attach.url.replace(process.env.VUE_APP_BASE_API,'');
      }
      updateReply(this.form).then(response=>{
        this.msgSuccess(send?"发送成功":"保存成功");
        this.open = false;
        this.getList();
      })
    },
    cancel(){
      this.open = false;
    }
  }
}
</script>

<style scoped>

</style>
