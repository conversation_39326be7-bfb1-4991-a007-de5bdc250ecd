<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblAttachmentMapper">

    <resultMap type="TblAttachment" id="TblAttachmentResult">
        <result property="attachmentId"    column="attachment_id"    />
        <result property="name"    column="name"    />
        <result property="url"    column="url"    />
        <result property="type"    column="type"    />
        <result property="parent"    column="parent"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectTblAttachmentVo">
        select attachment_id, name, url, type, parent, create_by, create_time, update_by, update_time, remark from tbl_attachment
    </sql>

    <select id="selectTblAttachmentList" parameterType="TblAttachment" resultMap="TblAttachmentResult">
        <include refid="selectTblAttachmentVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="parent != null "> and parent = #{parent}</if>
        </where>
    </select>

    <select id="selectTblAttachmentById" parameterType="Long" resultMap="TblAttachmentResult">
        <include refid="selectTblAttachmentVo"/>
        where attachment_id = #{attachmentId}
    </select>

    <select id="selectNoticeReplyTblAttachmentListByReplyId" parameterType="Long" resultMap="TblAttachmentResult">
        <include refid="selectTblAttachmentVo"/>
        where parent = #{replyId} and type = 'NoticeReply'
    </select>

    <insert id="insertTblAttachment" parameterType="TblAttachment" useGeneratedKeys="true" keyProperty="attachmentId">
        insert into tbl_attachment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="url != null">url,</if>
            <if test="type != null">type,</if>
            <if test="parent != null">parent,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="url != null">#{url},</if>
            <if test="type != null">#{type},</if>
            <if test="parent != null">#{parent},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="batchTblAttachments">
        insert into tbl_attachment( name, url, type, parent, create_time, create_by, update_time, update_by) values
        <foreach item="item" index="index" collection="list" separator=",">
            (  #{item.name}, #{item.url}, #{item.type}, #{item.parent}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <update id="updateTblAttachment" parameterType="TblAttachment">
        update tbl_attachment
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="url != null">url = #{url},</if>
            <if test="type != null">type = #{type},</if>
            <if test="parent != null">parent = #{parent},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where attachment_id = #{attachmentId}
    </update>

    <delete id="deleteTblAttachmentById" parameterType="Long">
        delete from tbl_attachment where attachment_id = #{attachmentId}
    </delete>

    <delete id="deleteTblAttachmentByIds" parameterType="String">
        delete from tbl_attachment where attachment_id in
        <foreach item="attachmentId" collection="array" open="(" separator="," close=")">
            #{attachmentId}
        </foreach>
    </delete>
    <delete id="deleteTblAttachmentByParentAndType">
        delete from tbl_attachment where parent=#{parent} and type=#{type}
    </delete>
</mapper>
