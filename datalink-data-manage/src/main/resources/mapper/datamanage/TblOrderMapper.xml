<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblOrderMapper">

    <resultMap type="TblOrder" id="TblOrderResult">
        <result property="orderId"    column="Order_ID"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="plannerNo"    column="Planner_No"    />
        <result property="plannerName"    column="Planner_Name"    />
        <result property="timeBegin"    column="Time_Begin"    />
        <result property="timeEnd"    column="Time_End"    />
        <result property="orderCode"    column="Order_Code"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
        <result property="isRead"     column="Is_Read"    />
        <result property="isComplete"     column="Is_Complete"    />
        <result property="customerCode"    column="Customer_Code"    />
        <result property="requester"    column="Requester"    />
        <result property="compName"    column="Comp_Name"    />
        <result property="status"    column="Status"    />
        <result property="purDocType"    column="Pur_Doc_Type"    />
        <result property="delIden"    column="Del_Iden"    />
        <result property="creator"    column="Creator"    />
        <result property="currencyCode"    column="Currency_Code"    />
        <result property="docDate"    column="Doc_Date"    />
    </resultMap>

    <resultMap id="TblOrderTblOrderItemResult" type="TblOrder" extends="TblOrderResult">
        <collection property="detail" notNullColumn="sub_Item_ID" javaType="java.util.List" resultMap="TblOrderItemResult" />
    </resultMap>

    <resultMap id="TblOrderTblOrderItemApiResult" type="TblApiOrder" extends="TblOrderResult">
        <collection property="detail" ofType="com.datalink.api.domain.TblApiOrderItem" select="selectTblApiOrderItemByOrderId" column="Order_ID"/>
    </resultMap>

    <resultMap type="TblOrderItem" id="TblOrderItemResult">
        <result property="itemId"    column="sub_Item_ID"    />
        <result property="itemNo"    column="sub_Item_No"    />
        <result property="purDocType"    column="sub_Pur_Doc_Type"    />
        <result property="itemType"    column="sub_Item_Type"    />
        <result property="text"    column="sub_Text"    />
        <result property="delIden"    column="sub_DeL_Iden"    />
        <result property="shortText"    column="sub_Short_Text"    />
        <result property="oldArticleNo"    column="sub_Old_Article_No"    />
        <result property="articleNo"    column="sub_Article_No"    />
        <result property="articleName"    column="sub_Article_Name"    />
        <result property="deliveryDate"    column="sub_Delivery_Date"    />
        <result property="quantity"    column="sub_Quantity"    />
        <result property="unit"    column="sub_Unit"    />
        <result property="workbinNo"    column="sub_Workbin_No"    />
        <result property="workbinName"    column="sub_Workbin_Name"    />
        <result property="qtyPerPack"    column="sub_Qty_Per_Pack"    />
        <result property="unloadingNo"    column="sub_Unloading_No"    />
        <result property="unloadingName"    column="sub_Unloading_Name"    />
        <result property="state"    column="sub_State"    />
        <result property="netPrice"    column="sub_Net_Price"    />
        <result property="priceUnit"    column="sub_Price_Unit"    />
        <result property="orderNetWorth"    column="sub_Order_Net_Worth"    />
        <result property="currencyCode"    column="sub_Currency_Code"    />
        <result property="stockLoc"    column="sub_Stock_Loc"    />
        <result property="locDes"    column="sub_Loc_Des"    />
        <result property="locAdd"    column="sub_Loc_Add"    />
        <result property="rcvName"    column="sub_Rcv_Name"    />
        <result property="rcvTel"    column="sub_Rcv_Tel"    />
        <result property="inspeStrategy"    column="sub_Inspe_Strategy"    />
        <result property="zipCode"    column="sub_Zip_Code"    />
        <result property="city"    column="sub_City"    />
        <result property="countryCode"    column="sub_Country_Code"    />
        <result property="addTimeZone"    column="sub_Add_Time_Zone"    />
        <result property="street2"    column="sub_Street2"    />
        <result property="street3"    column="sub_Street3"    />
        <result property="street4"    column="sub_Street4"    />
        <result property="orderId"    column="sub_Order_ID"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTime"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
        <result property="customerOrderCode"    column="sub_Customer_Order_Code"    />
        <result property="customerOrderLineCode"    column="sub_Customer_Order_Line_Code"    />
        <result property="customerDeliveryDate"    column="sub_Customer_Delivery_Date"    />
        <result property="productType"    column="sub_Product_Type"    />
        <result property="rcvType"    column="sub_Rcv_Type"    />
        <result property="purchaseType"    column="sub_Purchase_Type"    />
        <result property="depot"    column="sub_Depot"    />
        <result property="customerArticleNo"    column="sub_Customer_Article_No"    />
        <result property="articleType"    column="sub_Article_Type"    />
        <result property="secure"    column="sub_Secure"    />
        <result property="deliverySplit"    column="sub_Delivery_Split"    />
        <result property="soOrderQuantity"    column="So_Order_Quantity"    />
        <result property="customerPoNo"    column="Customer_Po_No"    />
        <result property="rankNo"    column="Rank_No"    />
        <result property="boxPackageQuantity"    column="Box_Package_Quantity"    />
        <result property="supplierCode03"    column="Supplier_Code_03"    />
        <result property="supplierCode04"    column="Supplier_Code_04"    />
        <result property="poRan"    column="Po_Ran"    />
        <result property="soCreateDate"    column="So_Create_Date"    />
        <result property="shipmentPlant02"    column="Shipment_Plant_02"    />
        <result property="orderType"    column="order_type"    />
        <result property="deliveryScheduleNo"    column="sub_Delivery_Schedule_No"    />
        <result property="plantCode"    column="sub_Plant_Code"    />
    </resultMap>

    <resultMap id="TblApiOrderItemResult" type="TblApiOrderItem" extends="TblOrderItemResult">
    </resultMap>

    <sql id="selectTblOrderVo">
        select Order_ID, Comp_Code, Plant_Code, Plant_Name, Supp_Code, Supp_Name, Planner_No, Planner_Name, Time_Begin, Time_End, Order_Code, Create_Time, Create_By, Update_Time, Update_By, Direction, Kafka_Status, Is_Read, Is_Complete, Customer_Code, Requester, Comp_Name, Status, Pur_Doc_Type, Del_Iden, Creator, Currency_Code, Doc_Date from tbl_order a
    </sql>

    <sql id="selectTblOrderItemVo">
        select Item_ID as sub_Item_ID, Item_No as sub_Item_No, Pur_Doc_Type as sub_Pur_Doc_Type, Item_Type as sub_Item_Type, Text as sub_Text, DeL_Iden as sub_DeL_Iden, Short_Text as sub_Short_Text, Old_Article_No as sub_Old_Article_No, Article_No as sub_Article_No, Article_Name as sub_Article_Name, Delivery_Date as sub_Delivery_Date, Quantity as sub_Quantity, Unit as sub_Unit, Workbin_No as sub_Workbin_No, Workbin_Name as sub_Workbin_Name, Qty_Per_Pack as sub_Qty_Per_Pack, Unloading_No as sub_Unloading_No, Unloading_Name as sub_Unloading_Name, State as sub_State, Net_Price as sub_Net_Price, Price_Unit as sub_Price_Unit, Order_Net_Worth as sub_Order_Net_Worth, Currency_Code as sub_Currency_Code, Stock_Loc as sub_Stock_Loc, Loc_Des as sub_Loc_Des, Loc_Add as sub_Loc_Add, Rcv_Name as sub_Rcv_Name, Rcv_Tel as sub_Rcv_Tel, Inspe_Strategy as sub_Inspe_Strategy, Zip_Code as sub_Zip_Code, City as sub_City, Country_Code as sub_Country_Code, Add_Time_Zone as sub_Add_Time_Zone, Street2 as sub_Street2, Street3 as sub_Street3, Street4 as sub_Street4, Order_ID as sub_Order_ID, Delivery_Schedule_No as sub_Delivery_Schedule_No, Plant_Code as sub_Plant_Code, Create_Time as sub_Create_Time, Create_By as sub_Create_By, Update_Time as sub_Update_Time, Update_By as sub_Update_By , Customer_Order_Code as sub_Customer_Order_Code, Customer_Order_Line_Code as sub_Customer_Order_Line_Code, Customer_Delivery_Date as sub_Customer_Delivery_Date, Product_Type as sub_Product_Type, Rcv_Type as sub_Rcv_Type, Purchase_Type as sub_Purchase_Type, Depot as sub_Depot, Customer_Article_No as sub_Customer_Article_No, Secure as sub_Secure, Article_Type as sub_Article_Type, Delivery_Split as sub_Delivery_Split, So_Order_Quantity, Customer_Po_No, Rank_No, Box_Package_Quantity, Supplier_Code_03, Supplier_Code_04, Po_Ran, So_Create_Date, Shipment_Plant_02, order_type from tbl_order_item
    </sql>

    <select id="selectTblOrderList" parameterType="TblOrder" resultMap="TblOrderResult">
        <include refid="selectTblOrderVo"/>
        <where>
            <if test="compCode != null  and compCode != ''"> and Comp_Code = #{compCode}</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="plannerNo != null  and plannerNo != ''"> and Planner_No like concat('%', #{plannerNo}, '%')</if>
            <if test="plannerName != null  and plannerName != ''"> and Planner_Name like concat('%', #{plannerName}, '%')</if>
            <if test="params.beginTimeBegin != null and params.beginTimeBegin != '' and params.endTimeBegin != null and params.endTimeBegin != ''"> and Time_Begin between #{params.beginTimeBegin} and #{params.endTimeBegin}</if>
            <if test="params.beginTimeEnd != null and params.beginTimeEnd != '' and params.endTimeEnd != null and params.endTimeEnd != ''"> and Time_End between #{params.beginTimeEnd} and #{params.endTimeEnd}</if>
            <if test="params.createTimeBegin != null and params.createTimeBegin != '' and params.createTimeEnd != null and params.createTimeEnd != ''"> and Create_Time between #{params.createTimeBegin} and #{params.createTimeEnd}</if>
            <if test="orderCode != null  and orderCode != ''"> and Order_Code like concat('%', #{orderCode}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <if test="isRead != null  and isRead != ''"> and Is_Read = #{isRead}</if>
            <if test="isComplete != null  and isComplete != ''"> and Is_Complete = #{isComplete}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectTblOrderById" parameterType="Long" resultMap="TblOrderTblOrderItemResult">
        select a.Order_ID, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Planner_No, a.Planner_Name, a.Time_Begin, a.Time_End, a.Order_Code, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By,a.Is_Read,a.Is_Complete,a.Direction,a.Kafka_Status,a.Customer_Code, a.Requester, a.Comp_Name, a.Status,
            b.Item_ID as sub_Item_ID, b.Item_No as sub_Item_No, b.Pur_Doc_Type as sub_Pur_Doc_Type, b.Item_Type as sub_Item_Type, b.Text as sub_Text, b.DeL_Iden as sub_DeL_Iden, b.Short_Text as sub_Short_Text, b.Old_Article_No as sub_Old_Article_No, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Delivery_Date as sub_Delivery_Date, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Workbin_No as sub_Workbin_No, b.Workbin_Name as sub_Workbin_Name, b.Qty_Per_Pack as sub_Qty_Per_Pack, b.Unloading_No as sub_Unloading_No, b.Unloading_Name as sub_Unloading_Name, b.State as sub_State, b.Net_Price as sub_Net_Price, b.Price_Unit as sub_Price_Unit, b.Order_Net_Worth as sub_Order_Net_Worth, b.Currency_Code as sub_Currency_Code, b.Stock_Loc as sub_Stock_Loc, b.Loc_Des as sub_Loc_Des, b.Loc_Add as sub_Loc_Add, b.Rcv_Name as sub_Rcv_Name, b.Rcv_Tel as sub_Rcv_Tel, b.Inspe_Strategy as sub_Inspe_Strategy, b.Zip_Code as sub_Zip_Code, b.City as sub_City, b.Country_Code as sub_Country_Code, b.Add_Time_Zone as sub_Add_Time_Zone, b.Street2 as sub_Street2, b.Street3 as sub_Street3, b.Street4 as sub_Street4, b.Order_ID as sub_Order_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Customer_Order_Code as sub_Customer_Order_Code, b.Customer_Order_Line_Code as sub_Customer_Order_Line_Code, b.Customer_Delivery_Date as sub_Customer_Delivery_Date, b.Product_Type as sub_Product_Type, b.Rcv_Type as sub_Rcv_Type, b.Purchase_Type as sub_Purchase_Type, b.Depot as sub_Depot, b.Customer_Article_No as sub_Customer_Article_No, b.Secure as sub_Secure, b.Article_Type as sub_Article_Type, b.Delivery_Split as sub_Delivery_Split, b.So_Order_Quantity, b.Customer_Po_No, b.Rank_No, b.Box_Package_Quantity, b.Supplier_Code_03, b.Supplier_Code_04, b.Po_Ran, b.So_Create_Date, b.Shipment_Plant_02, b.order_type
        from tbl_order a
        left join tbl_order_item b on b.Order_ID = a.Order_ID
        where a.Order_ID = #{orderId}
    </select>

    <select id="selectTblOrderOnlyById" parameterType="TblOrder" resultMap="TblOrderResult">
        <include refid="selectTblOrderVo"/>
        where Order_ID = #{orderId}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectTblOrderWithItemList" parameterType="TblOrder" resultMap="TblOrderTblOrderItemResult">
        select a.Order_ID, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Planner_No, a.Planner_Name, a.Time_Begin, a.Time_End, a.Order_Code, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By,a.Direction,a.Kafka_Status,a.Is_Read,a.Is_Complete,a.Direction,a.Kafka_Status,a.Customer_Code, a.Requester, a.Comp_Name, a.Status,
               b.Item_ID as sub_Item_ID, b.Item_No as sub_Item_No, b.Pur_Doc_Type as sub_Pur_Doc_Type, b.Item_Type as sub_Item_Type, b.Text as sub_Text, b.DeL_Iden as sub_DeL_Iden, b.Short_Text as sub_Short_Text, b.Old_Article_No as sub_Old_Article_No, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Delivery_Date as sub_Delivery_Date, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Workbin_No as sub_Workbin_No, b.Workbin_Name as sub_Workbin_Name, b.Qty_Per_Pack as sub_Qty_Per_Pack, b.Unloading_No as sub_Unloading_No, b.Unloading_Name as sub_Unloading_Name, b.State as sub_State, b.Net_Price as sub_Net_Price, b.Price_Unit as sub_Price_Unit, b.Order_Net_Worth as sub_Order_Net_Worth, b.Currency_Code as sub_Currency_Code, b.Stock_Loc as sub_Stock_Loc, b.Loc_Des as sub_Loc_Des, b.Loc_Add as sub_Loc_Add, b.Rcv_Name as sub_Rcv_Name, b.Rcv_Tel as sub_Rcv_Tel, b.Inspe_Strategy as sub_Inspe_Strategy, b.Zip_Code as sub_Zip_Code, b.City as sub_City, b.Country_Code as sub_Country_Code, b.Add_Time_Zone as sub_Add_Time_Zone, b.Street2 as sub_Street2, b.Street3 as sub_Street3, b.Street4 as sub_Street4, b.Order_ID as sub_Order_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By, b.Customer_Order_Code as sub_Customer_Order_Code, b.Customer_Order_Line_Code as sub_Customer_Order_Line_Code, b.Customer_Delivery_Date as sub_Customer_Delivery_Date, b.Product_Type as sub_Product_Type, b.Rcv_Type as sub_Rcv_Type, b.Purchase_Type as sub_Purchase_Type, b.Depot as sub_Depot, b.Customer_Article_No as sub_Customer_Article_No, b.Secure as sub_Secure, b.Article_Type as sub_Article_Type, b.Delivery_Split as sub_Delivery_Split, b.So_Order_Quantity, b.Customer_Po_No, b.Rank_No, b.Box_Package_Quantity, b.Supplier_Code_03, b.Supplier_Code_04, b.Po_Ran, b.So_Create_Date, b.Shipment_Plant_02
        from tbl_order a
                 left join tbl_order_item b on b.Order_ID = a.Order_ID
        <where>
            <if test="compCode != null  and compCode != ''"> and a.Comp_Code = #{compCode}</if>
            <if test="plantCode != null  and plantCode != ''"> and a.Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and a.Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="plannerNo != null  and plannerNo != ''"> and a.Planner_No like concat('%', #{plannerNo}, '%')</if>
            <if test="plannerName != null  and plannerName != ''"> and a.Planner_Name like concat('%', #{plannerName}, '%')</if>
            <if test="params.beginTimeBegin != null and params.beginTimeBegin != '' and params.endTimeBegin != null and params.endTimeBegin != ''"> and Time_Begin between #{params.beginTimeBegin} and #{params.endTimeBegin}</if>
            <if test="params.beginTimeEnd != null and params.beginTimeEnd != '' and params.endTimeEnd != null and params.endTimeEnd != ''"> and Time_End between #{params.beginTimeEnd} and #{params.endTimeEnd}</if>
            <if test="orderCode != null  and orderCode != ''"> and a.Order_Code like concat('%', #{orderCode}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="direction != null  and direction != ''"> and a.Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and a.Kafka_Status = #{kafkaStatus}</if>
        </where>
    </select>

    <select id="selectTblApiOrderItemByOrderId" parameterType="Long" resultMap="TblApiOrderItemResult">
        <include refid="selectTblOrderItemVo"/>
        where Order_ID = #{orderId}
    </select>

    <select id="selectTblOrderItemList" parameterType="TblOrderItem" resultMap="TblOrderItemResult">
        <include refid="selectTblOrderItemVo"/>
        <where>
            <if test="itemNo != null  and itemNo != ''"> and Item_No like concat('%', #{itemNo}, '%')</if>
            <if test="purDocType != null  and purDocType != ''"> and Pur_Doc_Type like concat('%', #{purDocType}, '%')</if>
            <if test="itemType != null  and itemType != ''"> and Item_Type like concat('%', #{itemType}, '%')</if>
            <if test="text != null  and text != ''"> and Text like concat('%', #{text}, '%')</if>
            <if test="delIden != null  and delIden != ''"> and DeL_Iden like concat('%', #{delIden}, '%')</if>
            <if test="shortText != null  and shortText != ''"> and Short_Text like concat('%', #{shortText}, '%')</if>
            <if test="oldArticleNo != null  and oldArticleNo != ''"> and Old_Article_No like concat('%', #{oldArticleNo}, '%')</if>
            <if test="articleNo != null  and articleNo != ''"> and Article_No like concat('%', #{articleNo}, '%')</if>
            <if test="articleName != null  and articleName != ''"> and Article_Name like concat('%', #{articleName}, '%')</if>
            <if test="params.beginDeliveryDate != null and params.beginDeliveryDate != '' and params.endDeliveryDate != null and params.endDeliveryDate != ''"> and Delivery_Date between #{params.beginDeliveryDate} and #{params.endDeliveryDate}</if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and Unit like concat('%', #{unit}, '%')</if>
            <if test="workbinNo != null  and workbinNo != ''"> and Workbin_No like concat('%', #{workbinNo}, '%')</if>
            <if test="workbinName != null  and workbinName != ''"> and Workbin_Name like concat('%', #{workbinName}, '%')</if>
            <if test="qtyPerPack != null "> and Qty_Per_Pack = #{qtyPerPack}</if>
            <if test="unloadingNo != null  and unloadingNo != ''"> and Unloading_No = #{unloadingNo}</if>
            <if test="unloadingName != null  and unloadingName != ''"> and Unloading_Name like concat('%', #{unloadingName}, '%')</if>
            <if test="state != null  and state != ''"> and State = #{state}</if>
            <if test="netPrice != null "> and Net_Price = #{netPrice}</if>
            <if test="priceUnit != null  and priceUnit != ''"> and Price_Unit = #{priceUnit}</if>
            <if test="orderNetWorth != null "> and Order_Net_Worth = #{orderNetWorth}</if>
            <if test="currencyCode != null  and currencyCode != ''"> and Currency_Code = #{currencyCode}</if>
            <if test="stockLoc != null  and stockLoc != ''"> and Stock_Loc = #{stockLoc}</if>
            <if test="locDes != null  and locDes != ''"> and Loc_Des = #{locDes}</if>
            <if test="locAdd != null  and locAdd != ''"> and Loc_Add = #{locAdd}</if>
            <if test="rcvName != null  and rcvName != ''"> and Rcv_Name like concat('%', #{rcvName}, '%')</if>
            <if test="rcvTel != null  and rcvTel != ''"> and Rcv_Tel = #{rcvTel}</if>
            <if test="inspeStrategy != null  and inspeStrategy != ''"> and Inspe_Strategy = #{inspeStrategy}</if>
            <if test="zipCode != null  and zipCode != ''"> and Zip_Code = #{zipCode}</if>
            <if test="city != null  and city != ''"> and City = #{city}</if>
            <if test="countryCode != null  and countryCode != ''"> and Country_Code = #{countryCode}</if>
            <if test="addTimeZone != null  and addTimeZone != ''"> and Add_Time_Zone = #{addTimeZone}</if>
            <if test="street2 != null  and street2 != ''"> and Street2 = #{street2}</if>
            <if test="street3 != null  and street3 != ''"> and Street3 = #{street3}</if>
            <if test="street4 != null  and street4 != ''"> and Street4 = #{street4}</if>
            <if test="orderId != null "> and Order_ID = #{orderId}</if>
        </where>
    </select>

    <select id="selectTblOrderFullList" parameterType="TblOrder" resultMap="TblOrderTblOrderItemApiResult">
        <include refid="selectTblOrderVo"/>
        <where>
            Direction = 'I'
            <if test="params.cursor !=null">and Order_ID > #{params.cursor}</if>
            <if test="params.cursorInclude !=null">and Order_ID >= #{params.cursorInclude}</if>
            <if test="params.time !=null">and Create_Time > #{params.time}</if>
            <if test="params.timeInclude !=null">and Create_Time >= #{params.timeInclude}</if>
        </where>
        order by Order_ID asc
        <if test="params.limit !=null and params.limit !=''">limit #{params.limit}</if>
    </select>

    <insert id="insertTblOrder" parameterType="TblOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into tbl_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code,</if>
            <if test="plantName != null and plantName != ''">Plant_Name,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="suppName != null and suppName != ''">Supp_Name,</if>
            <if test="plannerNo != null and plannerNo != ''">Planner_No,</if>
            <if test="plannerName != null and plannerName != ''">Planner_Name,</if>
            <if test="timeBegin != null">Time_Begin,</if>
            <if test="timeEnd != null">Time_End,</if>
            <if test="orderCode != null and orderCode != ''">Order_Code,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="direction != null">Direction,</if>
            <if test="kafkaStatus != null">Kafka_Status,</if>
            <if test="customerCode != null and customerCode !=''">Customer_Code,</if>
            <if test="requester != null and requester != '' ">Requester,</if>
            <if test="compName != null and compName!=''">Comp_Name,</if>
            <if test="sapUpdateTime != null">Sap_Update_Time,</if>
            <if test="status != null and status != ''">Status,</if>
            <if test="purDocType != null and purDocType != ''">Pur_Doc_Type,</if>
            <if test="delIden != null and delIden != ''">Del_Iden,</if>
            <if test="creator != null and creator != ''">Creator,</if>
            <if test="currencyCode != null and currencyCode != ''">Currency_Code,</if>
            <if test="docDate != null">Doc_Date,</if>
            Receive_Time, First_Receive_Time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="plantCode != null and plantCode != ''">#{plantCode},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="plannerNo != null and plannerNo != ''">#{plannerNo},</if>
            <if test="plannerName != null and plannerName != ''">#{plannerName},</if>
            <if test="timeBegin != null">#{timeBegin},</if>
            <if test="timeEnd != null">#{timeEnd},</if>
            <if test="orderCode != null and orderCode != ''">#{orderCode},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="direction != null">#{direction},</if>
            <if test="kafkaStatus != null">#{kafkaStatus},</if>
            <if test="customerCode != null and customerCode !=''">#{customerCode},</if>
            <if test="requester != null and requester != '' ">#{requester},</if>
            <if test="compName != null and compName!=''">#{compName},</if>
            <if test="sapUpdateTime != null">#{sapUpdateTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="purDocType != null and purDocType != ''">#{purDocType},</if>
            <if test="delIden != null and delIden != ''">#{delIden},</if>
            <if test="creator != null and creator != ''">#{creator},</if>
            <if test="currencyCode != null and currencyCode != ''">#{currencyCode},</if>
            <if test="docDate != null">#{docDate},</if>
            current_timestamp(), current_timestamp()
         </trim>
    </insert>

    <update id="updateTblOrder" parameterType="TblOrder">
        update tbl_order o
        <trim prefix="SET" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">Plant_Name = #{plantName},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="plannerNo != null and plannerNo != ''">Planner_No = #{plannerNo},</if>
            <if test="plannerName != null and plannerName != ''">Planner_Name = #{plannerName},</if>
            <if test="timeBegin != null">Time_Begin = #{timeBegin},</if>
            <if test="timeEnd != null">Time_End = #{timeEnd},</if>
            <if test="orderCode != null and orderCode != ''">Order_Code = #{orderCode},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="direction != null">Direction = #{direction},</if>
            <if test="kafkaStatus != null">Kafka_Status = #{kafkaStatus},</if>
            <if test="customerCode != null and customerCode !=''">Customer_Code = #{customerCode},</if>
            <if test="requester != null and requester != '' ">Requester = #{requester},</if>
            <if test="compName != null and compName!=''">Comp_Name = #{compName},</if>
            <if test="sapUpdateTime != null">Sap_Update_Time = #{sapUpdateTime},</if>
            <if test="status != null and status != ''">Status = #{status},</if>
            <if test="purDocType != null and purDocType != ''">Pur_Doc_Type = #{purDocType},</if>
            <if test="delIden != null and delIden != ''">Del_Iden = #{delIden},</if>
            <if test="creator != null and creator != ''">Creator = #{creator},</if>
            <if test="currencyCode != null and currencyCode != ''">Currency_Code = #{currencyCode},</if>
            <if test="docDate != null">Doc_Date = #{docDate},</if>
            Is_Complete = (select if(count(1)=0, 'Y', 'N') from tbl_order_asn_quantity t where t.Order_Code = o.Order_Code and t.Unsent_Quantity > 0),
            Receive_Time = current_timestamp()
        </trim>
        where Order_ID = #{orderId}
    </update>

    <delete id="deleteTblOrderById" parameterType="Long">
        delete from tbl_order where Order_ID = #{orderId}
    </delete>

    <delete id="deleteTblOrderByIds" parameterType="String">
        delete from tbl_order where Order_ID in
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <delete id="deleteTblOrderItemByOrderIds" parameterType="String">
        delete from tbl_order_item where Order_ID in
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>

    <delete id="deleteTblOrderItemByOrderId" parameterType="Long">
        delete from tbl_order_item where Order_ID = #{orderId}
    </delete>

    <insert id="batchTblOrderItem">
        insert into tbl_order_item (
            Item_No, Pur_Doc_Type, Item_Type, Text, DeL_Iden, Short_Text, Old_Article_No, Article_No, Article_Name, Delivery_Date, Quantity, Unit, Workbin_No, Workbin_Name, Qty_Per_Pack, Unloading_No, Unloading_Name, State, Net_Price, Price_Unit, Order_Net_Worth, Currency_Code, Stock_Loc, Loc_Des, Loc_Add, Rcv_Name, Rcv_Tel, Inspe_Strategy, Zip_Code, City, Country_Code, Add_Time_Zone, Street2, Street3, Street4, Order_ID, Create_Time, Create_By, Update_Time, Update_By, Customer_Order_Code, Customer_Order_Line_Code, Purchase_Type, Product_Type, Rcv_Type, Customer_Delivery_Date, Depot, Secure, Article_Type, Customer_Article_No, Delivery_Split,
            So_Order_Quantity, Customer_Po_No, Rank_No, Box_Package_Quantity,
            Supplier_Code_03, Supplier_Code_04, Po_Ran, So_Create_Date,
            Shipment_Plant_02, order_type
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
            (
                #{item.itemNo}, #{item.purDocType}, #{item.itemType}, #{item.text}, #{item.delIden}, #{item.shortText}, #{item.oldArticleNo}, #{item.articleNo}, #{item.articleName}, #{item.deliveryDate}, #{item.quantity}, #{item.unit}, #{item.workbinNo}, #{item.workbinName}, #{item.qtyPerPack}, #{item.unloadingNo}, #{item.unloadingName}, #{item.state}, #{item.netPrice}, #{item.priceUnit}, #{item.orderNetWorth}, #{item.currencyCode}, #{item.stockLoc}, #{item.locDes}, #{item.locAdd}, #{item.rcvName}, #{item.rcvTel}, #{item.inspeStrategy}, #{item.zipCode}, #{item.city}, #{item.countryCode}, #{item.addTimeZone}, #{item.street2}, #{item.street3}, #{item.street4}, #{item.orderId}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy}, #{item.customerOrderCode},#{item.customerOrderLineCode},#{item.purchaseType},#{item.productType},#{item.rcvType},#{item.customerDeliveryDate},#{item.depot},#{item.secure},#{item.articleType},#{item.customerArticleNo},#{item.deliverySplit},
                #{item.soOrderQuantity}, #{item.customerPoNo}, #{item.rankNo},
                #{item.boxPackageQuantity}, #{item.supplierCode03}, #{item.supplierCode04},
                #{item.poRan}, #{item.soCreateDate}, #{item.shipmentPlant02}, #{item.orderType}
            )
        </foreach>
    </insert>

    <select id="selectLastId" resultType="Long">
        select max(Order_ID) from tbl_order;
    </select>

    <insert id="batchTblOrderAsnQuantity">
        insert into tbl_order_asn_quantity( Order_Code, Order_Line_No, Comp_Code, Quantity, Unsent_Quantity) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.orderCode}, #{item.orderLineNo}, #{item.compCode}, #{item.quantity}, #{item.unsentQuantity})
        </foreach>
    </insert>

    <update id="updateOrderCompleteStatus" parameterType="String">
        update tbl_order set Is_Complete = (select if(count(1)=0, 'Y', 'N') from tbl_order_asn_quantity where Order_Code = #{orderCode} and Unsent_Quantity!=0) where Order_Code = #{orderCode}
    </update>
    <update id="updateOrderByCodeMap" parameterType="java.util.Map">
        update tbl_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">Plant_Name = #{plantName},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="plannerNo != null and plannerNo != ''">Planner_No = #{plannerNo},</if>
            <if test="plannerName != null and plannerName != ''">Planner_Name = #{plannerName},</if>
            <if test="timeBegin != null">Time_Begin = #{timeBegin},</if>
            <if test="timeEnd != null">Time_End = #{timeEnd},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="direction != null">Direction = #{direction},</if>
            <if test="kafkaStatus != null">Kafka_Status = #{kafkaStatus},</if>
            <if test="isRead != null">Is_Read = #{isRead},</if>
            <if test="isComplete != null">Is_Complete = #{isComplete},</if>
        </trim>
        where Order_Code = #{orderCode}
    </update>
    <update id="markRead" parameterType="Long">
        update tbl_order set Is_Read = 'Y' where Order_ID = #{orderId}
    </update>

    <resultMap type="FlatOrder" id="FlatOrderResult">
        <result property="orderId"    column="Order_ID"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="plannerNo"    column="Planner_No"    />
        <result property="plannerName"    column="Planner_Name"    />
        <result property="timeBegin"    column="Time_Begin"    />
        <result property="timeEnd"    column="Time_End"    />
        <result property="orderCode"    column="Order_Code"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="isRead"    column="Is_Read"    />
        <result property="isComplete"    column="Is_Complete"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
        <result property="customerCode"    column="Customer_Code"    />
        <result property="requester"    column="Requester"    />
        <result property="compName"    column="Comp_Name"    />
        <result property="itemId"    column="Item_ID"    />
        <result property="itemNo"    column="Item_No"    />
        <result property="purDocType"    column="Pur_Doc_Type"    />
        <result property="itemType"    column="Item_Type"    />
        <result property="text"    column="Text"    />
        <result property="delIden"    column="DeL_Iden"    />
        <result property="shortText"    column="Short_Text"    />
        <result property="oldArticleNo"    column="Old_Article_No"    />
        <result property="articleNo"    column="Article_No"    />
        <result property="articleName"    column="Article_Name"    />
        <result property="deliveryDate"    column="Delivery_Date"    />
        <result property="quantity"    column="Quantity"    />
        <result property="unit"    column="Unit"    />
        <result property="workbinNo"    column="Workbin_No"    />
        <result property="workbinName"    column="Workbin_Name"    />
        <result property="qtyPerPack"    column="Qty_Per_Pack"    />
        <result property="unloadingNo"    column="Unloading_No"    />
        <result property="unloadingName"    column="Unloading_Name"    />
        <result property="state"    column="State"    />
        <result property="netPrice"    column="Net_Price"    />
        <result property="priceUnit"    column="Price_Unit"    />
        <result property="orderNetWorth"    column="Order_Net_Worth"    />
        <result property="currencyCode"    column="Currency_Code"    />
        <result property="stockLoc"    column="Stock_Loc"    />
        <result property="locDes"    column="Loc_Des"    />
        <result property="locAdd"    column="Loc_Add"    />
        <result property="rcvName"    column="Rcv_Name"    />
        <result property="rcvTel"    column="Rcv_Tel"    />
        <result property="inspeStrategy"    column="Inspe_Strategy"    />
        <result property="zipCode"    column="Zip_Code"    />
        <result property="city"    column="City"    />
        <result property="countryCode"    column="Country_Code"    />
        <result property="addTimeZone"    column="Add_Time_Zone"    />
        <result property="street2"    column="Street2"    />
        <result property="street3"    column="Street3"    />
        <result property="street4"    column="Street4"    />
        <result property="customerOrderCode"    column="Customer_Order_Code"    />
        <result property="customerOrderLineCode"    column="Customer_Order_Line_Code"    />
        <result property="customerDeliveryDate"    column="Customer_Delivery_Date"    />
        <result property="productType"    column="Product_Type"    />
        <result property="rcvType"    column="Rcv_Type"    />
        <result property="purchaseType"    column="Purchase_Type"    />
        <result property="depot"    column="Depot"    />
        <result property="customerArticleNo"    column="Customer_Article_No"    />
        <result property="secure"    column="Secure"    />
        <result property="articleType"    column="Article_Type"    />
        <result property="sapUpdateTime"    column="Sap_Update_Time"    />
        <result property="receiveTime"    column="Receive_Time"    />
    </resultMap>

    <sql id="selectFlatOrderVo">
        select Order_ID, Comp_Code, Plant_Code, Plant_Name, Supp_Code, Supp_Name, Planner_No, Planner_Name, Time_Begin, Time_End, Order_Code, Create_Time, Create_By, Update_Time, Update_By, Is_Read, Is_Complete, Direction, Kafka_Status, Customer_Code, Requester, Comp_Name, Item_ID, Item_No, Pur_Doc_Type, Item_Type, Text, DeL_Iden, Short_Text, Old_Article_No, Article_No, Article_Name, date_format(Delivery_Date, '%Y-%m-%d') as Delivery_Date, Quantity, Unit, Workbin_No, Workbin_Name, Qty_Per_Pack, Unloading_No, Unloading_Name, State, Net_Price, Price_Unit, Order_Net_Worth, Currency_Code, Stock_Loc, Loc_Des, Loc_Add, Rcv_Name, Rcv_Tel, Inspe_Strategy, Zip_Code, City, Country_Code, Add_Time_Zone, Street2, Street3, Street4, Customer_Order_Code, Customer_Order_Line_Code, Customer_Delivery_Date, Product_Type, Rcv_Type, Purchase_Type, Depot, Customer_Article_No, Secure, Article_Type, Sap_Update_Time, Receive_Time from view_order_with_item a
    </sql>

    <select id="selectFlatOrderList" parameterType="FlatOrder" resultMap="FlatOrderResult">
        <include refid="selectFlatOrderVo"/>
        <where>
            <if test="orderId != null "> and Order_ID = #{orderId}</if>
            <if test="compCode != null  and compCode != ''"> and Comp_Code = #{compCode}</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code = #{plantCode}</if>
            <if test="is223XUser != null and is223XUser != '' and is223XUser == 'N'.toString()"> and Plant_Code != '223X'</if>
            <if test="is223XUser != null and is223XUser != '' and is223XUser == 'Y'.toString()"> and Plant_Code = '223X'</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code = #{suppCode}</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="plannerNo != null  and plannerNo != ''"> and Planner_No = #{plannerNo}</if>
            <if test="plannerName != null  and plannerName != ''"> and Planner_Name like concat('%', #{plannerName}, '%')</if>
            <if test="timeBegin != null "> and Time_Begin = #{timeBegin}</if>
            <if test="timeEnd != null "> and Time_End = #{timeEnd}</if>
            <if test="orderCode != null  and orderCode != ''"> and Order_Code = #{orderCode}</if>
            <if test="params.createTimeBegin != null and params.createTimeBegin != '' and params.createTimeEnd != null and params.createTimeEnd != ''"> and Create_Time between #{params.createTimeBegin} and #{params.createTimeEnd}</if>
            <if test="params.sapUpdateTimeBegin != null and params.sapUpdateTimeBegin != '' and params.sapUpdateTimeEnd != null and params.sapUpdateTimeEnd != ''"> and Sap_Update_Time between #{params.sapUpdateTimeBegin} and #{params.sapUpdateTimeEnd}</if>
            <if test="params.receiveTimeBegin != null and params.receiveTimeBegin != '' and params.receiveTimeEnd != null and params.receiveTimeEnd != ''"> and Receive_Time between #{params.receiveTimeBegin} and #{params.receiveTimeEnd}</if>
            <if test="createBy != null  and createBy != ''"> and Create_By = #{createBy}</if>
            <if test="updateTime != null "> and Update_Time = #{updateTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and Update_By = #{updateBy}</if>
            <if test="isRead != null  and isRead != ''"> and Is_Read = #{isRead}</if>
            <if test="isComplete != null  and isComplete != ''"> and Is_Complete = #{isComplete}</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <if test="customerCode != null  and customerCode != ''"> and Customer_Code = #{customerCode}</if>
            <if test="requester != null  and requester != ''"> and Requester = #{requester}</if>
            <if test="compName != null  and compName != ''"> and Comp_Name like concat('%', #{compName}, '%')</if>
            <if test="itemId != null "> and Item_ID = #{itemId}</if>
            <if test="itemNo != null  and itemNo != ''"> and Item_No = #{itemNo}</if>
            <if test="purDocType != null  and purDocType != ''"> and Pur_Doc_Type = #{purDocType}</if>
            <if test="itemType != null  and itemType != ''"> and Item_Type = #{itemType}</if>
            <if test="text != null  and text != ''"> and Text = #{text}</if>
            <if test="delIden != null  and delIden != ''"> and DeL_Iden = #{delIden}</if>
            <if test="shortText != null  and shortText != ''"> and Short_Text = #{shortText}</if>
            <if test="oldArticleNo != null  and oldArticleNo != ''"> and Old_Article_No = #{oldArticleNo}</if>
            <if test="articleNo != null  and articleNo != ''"> and Article_No like concat('%', #{articleNo}, '%')</if>
            <if test="articleName != null  and articleName != ''"> and Article_Name like concat('%', #{articleName}, '%')</if>
<!--            <if test="params.deliveryDateBegin != null and params.deliveryDateBegin != '' and params.deliveryDateEnd != null and params.deliveryDateEnd != ''"> and Delivery_Date between #{params.deliveryDateBegin} and #{params.deliveryDateEnd}</if>-->
            <if test="deliveryDate != null"> and date_format(Delivery_Date, '%Y-%m-%d') = #{deliveryDate} </if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and Unit = #{unit}</if>
            <if test="workbinNo != null  and workbinNo != ''"> and Workbin_No = #{workbinNo}</if>
            <if test="workbinName != null  and workbinName != ''"> and Workbin_Name like concat('%', #{workbinName}, '%')</if>
            <if test="qtyPerPack != null "> and Qty_Per_Pack = #{qtyPerPack}</if>
            <if test="unloadingNo != null  and unloadingNo != ''"> and Unloading_No = #{unloadingNo}</if>
            <if test="unloadingName != null  and unloadingName != ''"> and Unloading_Name like concat('%', #{unloadingName}, '%')</if>
            <if test="state != null  and state != ''"> and State = #{state}</if>
            <if test="netPrice != null "> and Net_Price = #{netPrice}</if>
            <if test="priceUnit != null  and priceUnit != ''"> and Price_Unit = #{priceUnit}</if>
            <if test="orderNetWorth != null "> and Order_Net_Worth = #{orderNetWorth}</if>
            <if test="currencyCode != null  and currencyCode != ''"> and Currency_Code = #{currencyCode}</if>
            <if test="stockLoc != null  and stockLoc != ''"> and Stock_Loc = #{stockLoc}</if>
            <if test="locDes != null  and locDes != ''"> and Loc_Des = #{locDes}</if>
            <if test="locAdd != null  and locAdd != ''"> and Loc_Add = #{locAdd}</if>
            <if test="rcvName != null  and rcvName != ''"> and Rcv_Name like concat('%', #{rcvName}, '%')</if>
            <if test="rcvTel != null  and rcvTel != ''"> and Rcv_Tel = #{rcvTel}</if>
            <if test="inspeStrategy != null  and inspeStrategy != ''"> and Inspe_Strategy = #{inspeStrategy}</if>
            <if test="zipCode != null  and zipCode != ''"> and Zip_Code = #{zipCode}</if>
            <if test="city != null  and city != ''"> and City = #{city}</if>
            <if test="countryCode != null  and countryCode != ''"> and Country_Code = #{countryCode}</if>
            <if test="addTimeZone != null  and addTimeZone != ''"> and Add_Time_Zone = #{addTimeZone}</if>
            <if test="street2 != null  and street2 != ''"> and Street2 = #{street2}</if>
            <if test="street3 != null  and street3 != ''"> and Street3 = #{street3}</if>
            <if test="street4 != null  and street4 != ''"> and Street4 = #{street4}</if>
            <if test="customerOrderCode != null  and customerOrderCode != ''"> and Customer_Order_Code = #{customerOrderCode}</if>
            <if test="customerOrderLineCode != null  and customerOrderLineCode != ''"> and Customer_Order_Line_Code = #{customerOrderLineCode}</if>
            <if test="customerDeliveryDate != null "> and Customer_Delivery_Date = #{customerDeliveryDate}</if>
            <if test="productType != null  and productType != ''"> and Product_Type = #{productType}</if>
            <if test="rcvType != null  and rcvType != ''"> and Rcv_Type = #{rcvType}</if>
            <if test="purchaseType != null  and purchaseType != ''"> and Purchase_Type = #{purchaseType}</if>
            <if test="depot != null  and depot != ''"> and Depot = #{depot}</if>
            <if test="customerArticleNo != null  and customerArticleNo != ''"> and Customer_Article_No = #{customerArticleNo}</if>
            <if test="secure != null  and secure != ''"> and Secure = #{secure}</if>
            <if test="articleType != null  and articleType != ''"> and Article_Type = #{articleType}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by Receive_Time desc, Order_Code desc
    </select>

    <update id="updateTblOrderAsnQuantity" parameterType="TblOrderAsnQuantity">
        update tbl_order_asn_quantity set Unsent_Quantity = Unsent_Quantity - Quantity + #{quantity}, Quantity = #{quantity}
        where Order_Code = #{orderCode} and Order_Line_No = #{orderLineNo} and Comp_Code = #{compCode}
    </update>

    <update id="batchUpdateOrderStatus">
        update tbl_order set Status = #{status}, Update_Time = now()
        where Order_ID in
        <foreach item="orderId" collection="orderIds" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </update>
</mapper>
