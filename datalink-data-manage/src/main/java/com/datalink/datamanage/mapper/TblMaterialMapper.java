package com.datalink.datamanage.mapper;

import com.datalink.datamanage.domain.TblMaterial;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 物料信息Mapper接口
 */
public interface TblMaterialMapper {
    /**
     * 查询物料信息
     *
     * @param materialId 物料信息ID
     * @return 物料信息
     */
    public TblMaterial selectTblMaterialById(Long materialId);

    /**
     * 查询物料信息列表
     *
     * @param tblMaterial 物料信息
     * @return 物料信息集合
     */
    public List<TblMaterial> selectTblMaterialList(TblMaterial tblMaterial);

    /**
     * 根据物料编号查询物料信息
     *
     * @param materialCode 物料编号
     * @return 物料信息
     */
    public TblMaterial selectTblMaterialByCode(String materialCode);

    /**
     * 新增物料信息
     *
     * @param tblMaterial 物料信息
     * @return 结果
     */
    public int insertTblMaterial(TblMaterial tblMaterial);

    /**
     * 修改物料信息
     *
     * @param tblMaterial 物料信息
     * @return 结果
     */
    public int updateTblMaterial(TblMaterial tblMaterial);

    /**
     * 删除物料信息
     *
     * @param materialId 物料信息ID
     * @return 结果
     */
    public int deleteTblMaterialById(Long materialId);

    /**
     * 批量删除物料信息
     *
     * @param materialIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblMaterialByIds(Long[] materialIds);
} 