package com.datalink.datamanage.service.impl;

import com.datalink.common.utils.DateUtils;
import com.datalink.datamanage.domain.TblEvent;
import com.datalink.datamanage.mapper.TblEventMapper;
import com.datalink.datamanage.service.ITblEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 事件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-04-20
 */
@Service
public class TblEventServiceImpl implements ITblEventService 
{
    @Autowired
    private TblEventMapper tblEventMapper;

    /**
     * 查询事件
     * 
     * @param eventId 事件ID
     * @return 事件
     */
    @Override
    public TblEvent selectTblEventById(Long eventId)
    {
        return tblEventMapper.selectTblEventById(eventId);
    }

    /**
     * 查询事件列表
     * 
     * @param tblEvent 事件
     * @return 事件
     */
    @Override
    public List<TblEvent> selectTblEventList(TblEvent tblEvent)
    {
        return tblEventMapper.selectTblEventList(tblEvent);
    }

    /**
     * 新增事件
     * 
     * @param tblEvent 事件
     * @return 结果
     */
    @Override
    public int insertTblEvent(TblEvent tblEvent)
    {
        tblEvent.setCreateTime(DateUtils.getNowDate());
        return tblEventMapper.insertTblEvent(tblEvent);
    }

    /**
     * 修改事件
     * 
     * @param tblEvent 事件
     * @return 结果
     */
    @Override
    public int updateTblEvent(TblEvent tblEvent)
    {
        tblEvent.setUpdateTime(DateUtils.getNowDate());
        return tblEventMapper.updateTblEvent(tblEvent);
    }

    /**
     * 批量删除事件
     * 
     * @param eventIds 需要删除的事件ID
     * @return 结果
     */
    @Override
    public int deleteTblEventByIds(Long[] eventIds)
    {
        return tblEventMapper.deleteTblEventByIds(eventIds);
    }

    /**
     * 删除事件信息
     * 
     * @param eventId 事件ID
     * @return 结果
     */
    @Override
    public int deleteTblEventById(Long eventId)
    {
        return tblEventMapper.deleteTblEventById(eventId);
    }
}
