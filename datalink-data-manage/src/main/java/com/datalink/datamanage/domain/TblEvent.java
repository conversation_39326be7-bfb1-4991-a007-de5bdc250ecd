package com.datalink.datamanage.domain;

import com.datalink.common.DataConstants;
import com.datalink.common.DataType;
import com.datalink.common.ServiceEnum;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.datalink.common.utils.spring.SpringUtils;
import com.datalink.json.JacksonUtil;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * 事件对象 tbl_event
 *
 * <AUTHOR>
 * @date 2022-04-20
 */
public class TblEvent extends BaseEntity implements KafkaData {
    private static final long serialVersionUID = 1L;

    private static final Logger logger = LoggerFactory.getLogger(TblEvent.class);

    /** 事件ID */
    private Long eventId;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String eventType;

    /** 事件内容 */
    @Excel(name = "事件内容")
    private JsonNode content;

    /** 调用服务名 */
    @Excel(name = "调用服务名")
    private String serviceName;

    /** 调用方法名 */
    @Excel(name = "调用方法名")
    private String methodName;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String compCode;

    /** 供应商代码 */
    @Excel(name = "供应商代码")
    private String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String suppName;

    /** 收发标志 */
    @Excel(name = "收发标志")
    private String direction;

    /** KafKa发送状态 */
    @Excel(name = "KafKa发送状态")
    private String kafkaStatus;

    public TblEvent(){

    }

    public TblEvent(ServiceEnum service, String method, Map<String, Object> content, String compCode, String suppCode, String suppName){
        this.eventType = "Update";
        this.serviceName = service.toString();
        this.methodName = method;
        this.content = JacksonUtil.valueToTree(content);
        this.compCode = compCode;
        this.suppCode = suppCode;
        this.suppName = suppName;
        this.direction = DataConstants.DIRECTION_OUT;
        this.kafkaStatus = DataConstants.KAFKA_STATUS_TO_SEND;
    }

    public void setEventId(Long eventId)
    {
        this.eventId = eventId;
    }

    public Long getEventId()
    {
        return eventId;
    }
    public void setEventType(String eventType)
    {
        this.eventType = eventType;
    }

    public String getEventType()
    {
        return eventType;
    }
    public void setContent(JsonNode content)
    {
        this.content = content;
    }

    public JsonNode getContent()
    {
        return content;
    }
    public void setServiceName(String serviceName)
    {
        this.serviceName = serviceName;
    }

    public String getServiceName()
    {
        return serviceName;
    }
    public void setMethodName(String methodName)
    {
        this.methodName = methodName;
    }

    public String getMethodName()
    {
        return methodName;
    }
    public void setCompCode(String compCode)
    {
        this.compCode = compCode;
    }

    public String getCompCode()
    {
        return compCode;
    }
    public void setSuppCode(String suppCode)
    {
        this.suppCode = suppCode;
    }

    @Override
    public DataType getObjectType() {
        return DataType.EVENT_TYPE;
    }

    public String getSuppCode()
    {
        return suppCode;
    }
    public void setSuppName(String suppName)
    {
        this.suppName = suppName;
    }

    public String getSuppName()
    {
        return suppName;
    }
    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("eventId", getEventId())
            .append("eventType", getEventType())
            .append("content", getContent())
            .append("serviceName", getServiceName())
            .append("createTime", getCreateTime())
            .append("methodName", getMethodName())
            .append("createBy", getCreateBy())
            .append("compCode", getCompCode())
            .append("updateTime", getUpdateTime())
            .append("suppCode", getSuppCode())
            .append("updateBy", getUpdateBy())
            .append("suppName", getSuppName())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .toString();
    }

    public Object execute(){
        if (null != getServiceName() && null != getMethodName()){
            try {
                ServiceEnum service = ServiceEnum.valueOf(getServiceName());
                Object bean = SpringUtils.getBean(service.getService());
                Method method = bean.getClass().getMethod(getMethodName(), Map.class);
                return method.invoke(bean, JacksonUtil.parseMap(getContent()));
            } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException | IllegalArgumentException e) {
                logger.error("execute "+getServiceName()+"."+getMethodName()+"("+getContent().toString()+") fail", e);
            }
        }
        return null;
    }
}
