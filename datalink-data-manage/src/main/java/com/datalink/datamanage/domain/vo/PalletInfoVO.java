package com.datalink.datamanage.domain.vo;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 托盘信息视图对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PalletInfoVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 托盘ID */
    private Long palletId;

    /** 物料工厂ID */
    private Long materialPlantId;

    /** 物料ID */
    private Long materialId;

    /** 物料编号 */
    @Excel(name = "物料编号")
    private String materialCode;

    /** 物料描述 */
    @Excel(name = "物料描述")
    private String materialName;

    /** 基本计量单位 */
    @Excel(name = "基本计量单位")
    private String baseUnit;

    /** 工厂代码 */
    @Excel(name = "工厂代码")
    private String plantCode;
    
    /** 包装数量 */
    @Excel(name = "包装数量")
    private Long packQuantity;

    /** 整托包含SNP数量 */
    @Excel(name = "整托包含SNP数量")
    private Integer snpQuantity;

    /** 托长(mm) */
    @Excel(name = "托长(mm)")
    private Integer palletLength;

    /** 托宽(mm) */
    @Excel(name = "托宽(mm)")
    private Integer palletWidth;

    /** 托高(mm) */
    @Excel(name = "托高(mm)")
    private Integer palletHeight;

    /** 容器种类 */
    @Excel(name = "容器种类")
    private String containerType;
} 