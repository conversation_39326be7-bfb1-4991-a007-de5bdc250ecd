package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 公告对象 tbl_notice
 *
 * <AUTHOR>
 * @date 2022-05-02
 */
public class TblNotice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 公告ID */
    private Long noticeId;

    /** 公告编号 */
    @Excel(name = "公告编号")
    private String noticeCode;

    /** 公告标题 */
    @Excel(name = "公告标题")
    private String noticeTitle;

    /** 公告类型（1通知 2公告） */
    private String noticeType;

    /** 公告内容 */
    @Excel(name = "公告内容")
    private String noticeContent;

    /** 公告状态（0草稿 1已发布） */
    private String status;

    /** 公司代码 */
    private String compCode;

    /** 工厂 */
    private String plantCode;

    /** 工厂名称 */
    private String plantName;

    private String needReply;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 收发标志 */
    private String direction;

    /** 公告回复信息 */
    private List<TblNoticeReply> tblNoticeReplyList;

    private List<TblAttachment> attachmentList;

    public void setNoticeId(Long noticeId)
    {
        this.noticeId = noticeId;
    }

    public Long getNoticeId()
    {
        return noticeId;
    }
    public void setNoticeCode(String noticeCode)
    {
        this.noticeCode = noticeCode;
    }

    public String getNoticeCode()
    {
        return noticeCode;
    }
    public void setNoticeTitle(String noticeTitle)
    {
        this.noticeTitle = noticeTitle;
    }

    public String getNoticeTitle()
    {
        return noticeTitle;
    }
    public void setNoticeType(String noticeType)
    {
        this.noticeType = noticeType;
    }

    public String getNoticeType()
    {
        return noticeType;
    }
    public void setNoticeContent(String noticeContent)
    {
        this.noticeContent = noticeContent;
    }

    public String getNoticeContent()
    {
        return noticeContent;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCompCode(String compCode)
    {
        this.compCode = compCode;
    }

    public String getCompCode()
    {
        return compCode;
    }
    public void setPlantCode(String plantCode)
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode()
    {
        return plantCode;
    }
    public void setPlantName(String plantName)
    {
        this.plantName = plantName;
    }

    public String getPlantName()
    {
        return plantName;
    }
    public void setPublishTime(Date publishTime)
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime()
    {
        return publishTime;
    }

    public List<TblNoticeReply> getTblNoticeReplyList()
    {
        return tblNoticeReplyList;
    }

    public void setTblNoticeReplyList(List<TblNoticeReply> tblNoticeReplyList)
    {
        this.tblNoticeReplyList = tblNoticeReplyList;
    }

    public String getNeedReply() {
        return needReply;
    }

    public void setNeedReply(String needReply) {
        this.needReply = needReply;
    }

    public List<TblAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<TblAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("noticeId", getNoticeId())
            .append("noticeCode", getNoticeCode())
            .append("noticeTitle", getNoticeTitle())
            .append("noticeType", getNoticeType())
            .append("noticeContent", getNoticeContent())
            .append("status", getStatus())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("createBy", getCreateBy())
            .append("publishTime", getPublishTime())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("tblNoticeReplyList", getTblNoticeReplyList())
            .toString();
    }

    public TblNotice(){

    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public TblNotice(NoticeReply noticeReply){
        this.noticeCode = noticeReply.getNoticeCode();
        this.noticeContent = noticeReply.getNoticeContent();
        this.noticeTitle = noticeReply.getNoticeTitle();
        this.noticeType = noticeReply.getNoticeType();
        this.compCode = noticeReply.getCompCode();
        this.plantCode = noticeReply.getPlantCode();
        this.plantName = noticeReply.getPlantName();
        this.publishTime = noticeReply.getPublishTime();
        this.status = noticeReply.getStatus();
        this.setCreateTime(noticeReply.getCreateTime());
        this.setCreateBy(noticeReply.getCreateBy());
        this.setUpdateTime(noticeReply.getUpdateTime());
        this.setUpdateBy(noticeReply.getUpdateBy());
        noticeReply.setReplyId(null);
        this.setTblNoticeReplyList(Collections.singletonList(noticeReply));
        this.setAttachmentList(noticeReply.getAttachmentList());
        this.setNeedReply(noticeReply.getNeedReply());
    }
}
