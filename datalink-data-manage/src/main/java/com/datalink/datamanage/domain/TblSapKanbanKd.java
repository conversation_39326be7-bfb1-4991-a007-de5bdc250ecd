package com.datalink.datamanage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 看板KD对象 tbl_sap_kanban_kd
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
public class TblSapKanbanKd extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** ZFILE_NO - Ｆ／＃ */
    @JsonProperty("ZFILE_NO")
    private String fileNo;

    /** ZRESERVE1 - 予備 */
    @JsonProperty("ZRESERVE1")
    private String reserve1;

    /** ZDMND_CD - 要元 */
    @JsonProperty("ZDMND_CD")
    private String demandCode;

    /** ZISSU_NO - 発行ＮＯ */
    @JsonProperty("ZISSU_NO")
    private String issueNo;

    /** ZDELI_INSTR_YMD - 納入指示日年月日 */
    @JsonProperty("ZDELI_INSTR_YMD")
    private String deliveryInstructionDate;

    /** ZDELI_INSTR_HM - 納入指示日時分 */
    @JsonProperty("ZDELI_INSTR_HM")
    private String deliveryInstructionTime;

    /** ZMAKER - メーカー */
    @JsonProperty("ZMAKER")
    private String maker;

    /** ZDEPO - デポ */
    @JsonProperty("ZDEPO")
    private String depot;

    /** ZCLASS - 区分 */
    @JsonProperty("ZCLASS")
    private String classification;

    /** ZDELI_LOC - 納入場所 */
    @JsonProperty("ZDELI_LOC")
    private String deliveryLocation;

    /** ZUNLOAD_UN - 荷卸単位 */
    @JsonProperty("ZUNLOAD_UN")
    private String unloadingUnit;

    /** ZCUST_PARTS_NO - 部品番号 */
    @JsonProperty("ZCUST_PARTS_NO")
    private String customerPartsNo;

    /** ZCUR_PRC_CLS - 当工順 */
    @JsonProperty("ZCUR_PRC_CLS")
    private String currentProcessClass;

    /** ZPARTS_NAM - 部品名称 */
    @JsonProperty("ZPARTS_NAM")
    private String partsName;

    /** ZDEL_INSTR_NO - 納入指示数 */
    @JsonProperty("ZDEL_INSTR_NO")
    private String deliveryInstructionNumber;

    /** ZSUPP_LOC - 供給場所 */
    @JsonProperty("ZSUPP_LOC")
    private String supplyLocation;

    /** ZSNEP - ＳＮＥＰ（荷姿用） */
    @JsonProperty("ZSNEP")
    private String snep;

    /** ZSAFE_FLG - 重保マーク */
    @JsonProperty("ZSAFE_FLG")
    private String safetyFlag;

    /** ZLBL_CNT - 容器数・ラベル枚数 */
    @JsonProperty("ZLBL_CNT")
    private String labelCount;

    /** ZMANAGE_PIC - 生担 */
    @JsonProperty("ZMANAGE_PIC")
    private String managementPic;

    /** ZISSU_RSN - 発行理由 */
    @JsonProperty("ZISSU_RSN")
    private String issueReason;

    /** ZVIP_FLG - 特便マーク */
    @JsonProperty("ZVIP_FLG")
    private String vipFlag;

    /** ZPCKG_CD - 荷姿コード */
    @JsonProperty("ZPCKG_CD")
    private String packageCode;

    /** ZIO_CLS - 内外区分 */
    @JsonProperty("ZIO_CLS")
    private String internalExternalClass;

    /** ZDELI_INSTR_CNT - 納入指示端数 */
    @JsonProperty("ZDELI_INSTR_CNT")
    private String deliveryInstructionCount;

    /** ZDELI_METHD - 納入方式 */
    @JsonProperty("ZDELI_METHD")
    private String deliveryMethod;

    /** ZORD_CLS - 発注区分 */
    @JsonProperty("ZORD_CLS")
    private String orderClass;

    /** ZLBL_ID - ラベル識別 */
    @JsonProperty("ZLBL_ID")
    private String labelId;

    /** ZDELAY_ANTI_MAK_SYM - 遅防法メーカーシンボル */
    @JsonProperty("ZDELAY_ANTI_MAK_SYM")
    private String delayPreventionMakerSymbol;

    /** ZSHIPTO_CLS - 送り先区分 */
    @JsonProperty("ZSHIPTO_CLS")
    private String shiptoClass;

    /** ZSHIPTO_CD - 送り先コード */
    @JsonProperty("ZSHIPTO_CD")
    private String shiptoCode;

    /** ZLBL_OUTPUT - ラベル出力先 */
    @JsonProperty("ZLBL_OUTPUT")
    private String labelOutput;

    /** ZRESERVE2 - 予備 */
    @JsonProperty("ZRESERVE2")
    private String reserve2;

    /** ZLBL_NOCHG_MARK - ラベル変更不可マーク */
    @JsonProperty("ZLBL_NOCHG_MARK")
    private String labelNoChangeMark;

    /** ZSEL_PROCU_LINE - 自己調達区分 */
    @JsonProperty("ZSEL_PROCU_LINE")
    private String selfProcurementLine;

    /** ZLBL_SPEC - ラベル仕様 */
    @JsonProperty("ZLBL_SPEC")
    private String labelSpecification;

    /** ZSEL_PROCU_KD - 自己調達区分 */
    @JsonProperty("ZSEL_PROCU_KD")
    private String selfProcurementKd;

    /** ZRESERVE3 - 予備 */
    @JsonProperty("ZRESERVE3")
    private String reserve3;

    /** ZKD_ELEMENTS - KD諸元 */
    @JsonProperty("ZKD_ELEMENTS")
    private String kdElements;

    /** ZTRANSIT_CODE - 経由地コード */
    @JsonProperty("ZTRANSIT_CODE")
    private String transitCode;

    /** ZTRANSIT_DELI_YMD - 経由地納入指示年月日 */
    @JsonProperty("ZTRANSIT_DELI_YMD")
    private String transitDeliveryDate;

    /** ZPARTS_ID - 部品識別 */
    @JsonProperty("ZPARTS_ID")
    private String partsId;

    /** ZDELI_TICKET_NO - 納品チケットＮＯ */
    @JsonProperty("ZDELI_TICKET_NO")
    private String deliveryTicketNo;

    /** ZWD_CLS - 引取区分 */
    @JsonProperty("ZWD_CLS")
    private String withdrawalClass;

    /** ZPART_PRICE - 部品価格 */
    @JsonProperty("ZPART_PRICE")
    private String partPrice;

    /** ZPROT_SYM - 試作シンボル */
    @JsonProperty("ZPROT_SYM")
    private String prototypeSymbol;

    /** ZRESEND_SYM - 再送シンボル */
    @JsonProperty("ZRESEND_SYM")
    private String resendSymbol;

    /** ZORDER_YMD - 発注年月日 */
    @JsonProperty("ZORDER_YMD")
    private String orderDate;

    /** ZLBL_SIZE - ラベルサイズ */
    @JsonProperty("ZLBL_SIZE")
    private String labelSize;

    /** ZRESERVE4 - 予備 */
    @JsonProperty("ZRESERVE4")
    private String reserve4;

    /** ZRESERVE5 - 予備 */
    @JsonProperty("ZRESERVE5")
    private String reserve5;

    /** ZMAKER_PIC - メーカー担当 */
    @JsonProperty("ZMAKER_PIC")
    private String makerPic;

    /** ZMAKER_SNP - メーカーＳＮＰ */
    @JsonProperty("ZMAKER_SNP")
    private String makerSnp;

    /** ZRESEND_DEPO - 再配信デポ */
    @JsonProperty("ZRESEND_DEPO")
    private String resendDepot;

    /** ZSHIP_PIC - 出荷担当 */
    @JsonProperty("ZSHIP_PIC")
    private String shippingPic;

    /** ZPARTS_NO_ID_CD - 部品番号識別コード */
    @JsonProperty("ZPARTS_NO_ID_CD")
    private String partsNoIdCode;

    /** ZPLANT_CD - 工場コード */
    @JsonProperty("ZPLANT_CD")
    private String plantCode;

    /** ZSHIP_LOC - 出荷場所 */
    @JsonProperty("ZSHIP_LOC")
    private String shippingLocation;

    /** ZSHIP_PORT - 出荷ポート */
    @JsonProperty("ZSHIP_PORT")
    private String shippingPort;

    /** ZRESERVE6 - 予備 */
    @JsonProperty("ZRESERVE6")
    private String reserve6;

    /** ZDIV_DEPO - 振分デポ */
    @JsonProperty("ZDIV_DEPO")
    private String divisionDepot;

    /** ZSHIP_LOC_CD2 - 出荷場所コード２ */
    @JsonProperty("ZSHIP_LOC_CD2")
    private String shippingLocationCode2;

    /** ZMOV_LT - 移動リードタイム */
    @JsonProperty("ZMOV_LT")
    private String movementLeadTime;

    /** ZRE_HANDL_FLG - 再処理フラグ */
    @JsonProperty("ZRE_HANDL_FLG")
    private String rehandlingFlag;

    /** ZNODATA_FLG - ノーマスターフラグ */
    @JsonProperty("ZNODATA_FLG")
    private String noMasterFlag;

    /** ZRESERVE7 - 予備 */
    @JsonProperty("ZRESERVE7")
    private String reserve7;

    /** ZDIV_DEPO2 - 振分デポ */
    @JsonProperty("ZDIV_DEPO2")
    private String divisionDepot2;

    /** ZLBL_TERM_NO - ラベル端末ＮＯ */
    @JsonProperty("ZLBL_TERM_NO")
    private String labelTerminalNo;

    /** ZLIST_TERM_NO - リスト端末ＮＯ */
    @JsonProperty("ZLIST_TERM_NO") 
    private String listTerminalNo;

    /** ZPARTS_NO - 部品番号 */
    @JsonProperty("ZPARTS_NO")
    private String partsNo;

    /** ZPRDCT_CLS - 製品区分 */
    @JsonProperty("ZPRDCT_CLS")
    private String productClass;

    /** ZPRD_METHD - 生産方式 */
    @JsonProperty("ZPRD_METHD")
    private String productionMethod;

    /** ZDELI_METHD2 - 納入方式2 */
    @JsonProperty("ZDELI_METHD2")
    private String deliveryMethod2;

    /** ZFROM_PRC - ＦＲＯＭ工順 */
    @JsonProperty("ZFROM_PRC")
    private String fromProcess;

    /** ZFROM_W_AREA - ＦＲＯＭ工程 */
    @JsonProperty("ZFROM_W_AREA")
    private String fromWorkArea;

    /** ZINSTR_YMD - 指示年月日 */
    @JsonProperty("ZINSTR_YMD")
    private String instructionDate;

    /** ZINSTR_HM - 指示時分 */
    @JsonProperty("ZINSTR_HM")
    private String instructionTime;

    /** ZINSTR_NO - 指示ＮＯ */
    @JsonProperty("ZINSTR_NO")
    private String instructionNo;

    /** ZPRD_HANDL_METHD - 生産対応方式 */
    @JsonProperty("ZPRD_HANDL_METHD")
    private String productionHandlingMethod;

    /** ZSHIP_CHK_FLAG - 出荷チェックフラグ */
    @JsonProperty("ZSHIP_CHK_FLAG")
    private String shippingCheckFlag;

    /** ZLBL_ISSU_ASG - ラベル発行指定 */
    @JsonProperty("ZLBL_ISSU_ASG")
    private String labelIssueAssignment;

    /** ZCUST_CD - 得意先コード */
    @JsonProperty("ZCUST_CD")
    private String customerCode;

    /** ZDMND_CD2 - 要求元コード */
    @JsonProperty("ZDMND_CD2")
    private String demandCode2;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFileNo(String fileNo) 
    {
        this.fileNo = fileNo;
    }

    public String getFileNo() 
    {
        return fileNo;
    }
    public void setReserve1(String reserve1) 
    {
        this.reserve1 = reserve1;
    }

    public String getReserve1() 
    {
        return reserve1;
    }
    public void setDemandCode(String demandCode) 
    {
        this.demandCode = demandCode;
    }

    public String getDemandCode() 
    {
        return demandCode;
    }
    public void setIssueNo(String issueNo) 
    {
        this.issueNo = issueNo;
    }

    public String getIssueNo() 
    {
        return issueNo;
    }
    public void setDeliveryInstructionDate(String deliveryInstructionDate) 
    {
        this.deliveryInstructionDate = deliveryInstructionDate;
    }

    public String getDeliveryInstructionDate() 
    {
        return deliveryInstructionDate;
    }
    public void setDeliveryInstructionTime(String deliveryInstructionTime) 
    {
        this.deliveryInstructionTime = deliveryInstructionTime;
    }

    public String getDeliveryInstructionTime() 
    {
        return deliveryInstructionTime;
    }
    public void setMaker(String maker) 
    {
        this.maker = maker;
    }

    public String getMaker() 
    {
        return maker;
    }
    public void setDepot(String depot) 
    {
        this.depot = depot;
    }

    public String getDepot() 
    {
        return depot;
    }
    public void setClassification(String classification) 
    {
        this.classification = classification;
    }

    public String getClassification() 
    {
        return classification;
    }
    public void setDeliveryLocation(String deliveryLocation) 
    {
        this.deliveryLocation = deliveryLocation;
    }

    public String getDeliveryLocation() 
    {
        return deliveryLocation;
    }
    public void setUnloadingUnit(String unloadingUnit) 
    {
        this.unloadingUnit = unloadingUnit;
    }

    public String getUnloadingUnit() 
    {
        return unloadingUnit;
    }
    public void setCustomerPartsNo(String customerPartsNo) 
    {
        this.customerPartsNo = customerPartsNo;
    }

    public String getCustomerPartsNo() 
    {
        return customerPartsNo;
    }
    public void setCurrentProcessClass(String currentProcessClass) 
    {
        this.currentProcessClass = currentProcessClass;
    }

    public String getCurrentProcessClass() 
    {
        return currentProcessClass;
    }
    public void setPartsName(String partsName) 
    {
        this.partsName = partsName;
    }

    public String getPartsName() 
    {
        return partsName;
    }
    public void setDeliveryInstructionNumber(String deliveryInstructionNumber) 
    {
        this.deliveryInstructionNumber = deliveryInstructionNumber;
    }

    public String getDeliveryInstructionNumber() 
    {
        return deliveryInstructionNumber;
    }
    public void setSupplyLocation(String supplyLocation) 
    {
        this.supplyLocation = supplyLocation;
    }

    public String getSupplyLocation() 
    {
        return supplyLocation;
    }
    public void setSnep(String snep) 
    {
        this.snep = snep;
    }

    public String getSnep() 
    {
        return snep;
    }
    public void setSafetyFlag(String safetyFlag) 
    {
        this.safetyFlag = safetyFlag;
    }

    public String getSafetyFlag() 
    {
        return safetyFlag;
    }
    public void setLabelCount(String labelCount) 
    {
        this.labelCount = labelCount;
    }

    public String getLabelCount() 
    {
        return labelCount;
    }
    public void setManagementPic(String managementPic) 
    {
        this.managementPic = managementPic;
    }

    public String getManagementPic() 
    {
        return managementPic;
    }
    public void setIssueReason(String issueReason) 
    {
        this.issueReason = issueReason;
    }

    public String getIssueReason() 
    {
        return issueReason;
    }
    public void setVipFlag(String vipFlag) 
    {
        this.vipFlag = vipFlag;
    }

    public String getVipFlag() 
    {
        return vipFlag;
    }
    public void setPackageCode(String packageCode) 
    {
        this.packageCode = packageCode;
    }

    public String getPackageCode() 
    {
        return packageCode;
    }
    public void setInternalExternalClass(String internalExternalClass) 
    {
        this.internalExternalClass = internalExternalClass;
    }

    public String getInternalExternalClass() 
    {
        return internalExternalClass;
    }
    public void setDeliveryInstructionCount(String deliveryInstructionCount) 
    {
        this.deliveryInstructionCount = deliveryInstructionCount;
    }

    public String getDeliveryInstructionCount() 
    {
        return deliveryInstructionCount;
    }
    public void setDeliveryMethod(String deliveryMethod) 
    {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryMethod() 
    {
        return deliveryMethod;
    }
    public void setOrderClass(String orderClass) 
    {
        this.orderClass = orderClass;
    }

    public String getOrderClass() 
    {
        return orderClass;
    }
    public void setLabelId(String labelId) 
    {
        this.labelId = labelId;
    }

    public String getLabelId() 
    {
        return labelId;
    }
    public void setDelayPreventionMakerSymbol(String delayPreventionMakerSymbol) 
    {
        this.delayPreventionMakerSymbol = delayPreventionMakerSymbol;
    }

    public String getDelayPreventionMakerSymbol() 
    {
        return delayPreventionMakerSymbol;
    }
    public void setShiptoClass(String shiptoClass) 
    {
        this.shiptoClass = shiptoClass;
    }

    public String getShiptoClass() 
    {
        return shiptoClass;
    }
    public void setShiptoCode(String shiptoCode) 
    {
        this.shiptoCode = shiptoCode;
    }

    public String getShiptoCode() 
    {
        return shiptoCode;
    }
    public void setLabelOutput(String labelOutput) 
    {
        this.labelOutput = labelOutput;
    }

    public String getLabelOutput() 
    {
        return labelOutput;
    }
    public void setReserve2(String reserve2) 
    {
        this.reserve2 = reserve2;
    }

    public String getReserve2() 
    {
        return reserve2;
    }
    public void setLabelNoChangeMark(String labelNoChangeMark) 
    {
        this.labelNoChangeMark = labelNoChangeMark;
    }

    public String getLabelNoChangeMark() 
    {
        return labelNoChangeMark;
    }
    public void setSelfProcurementLine(String selfProcurementLine) 
    {
        this.selfProcurementLine = selfProcurementLine;
    }

    public String getSelfProcurementLine() 
    {
        return selfProcurementLine;
    }
    public void setLabelSpecification(String labelSpecification) 
    {
        this.labelSpecification = labelSpecification;
    }

    public String getLabelSpecification() 
    {
        return labelSpecification;
    }
    public void setSelfProcurementKd(String selfProcurementKd) 
    {
        this.selfProcurementKd = selfProcurementKd;
    }

    public String getSelfProcurementKd() 
    {
        return selfProcurementKd;
    }
    public void setReserve3(String reserve3) 
    {
        this.reserve3 = reserve3;
    }

    public String getReserve3() 
    {
        return reserve3;
    }
    public void setKdElements(String kdElements) 
    {
        this.kdElements = kdElements;
    }

    public String getKdElements() 
    {
        return kdElements;
    }
    public void setTransitCode(String transitCode) 
    {
        this.transitCode = transitCode;
    }

    public String getTransitCode() 
    {
        return transitCode;
    }
    public void setTransitDeliveryDate(String transitDeliveryDate) 
    {
        this.transitDeliveryDate = transitDeliveryDate;
    }

    public String getTransitDeliveryDate() 
    {
        return transitDeliveryDate;
    }
    public void setPartsId(String partsId) 
    {
        this.partsId = partsId;
    }

    public String getPartsId() 
    {
        return partsId;
    }
    public void setDeliveryTicketNo(String deliveryTicketNo) 
    {
        this.deliveryTicketNo = deliveryTicketNo;
    }

    public String getDeliveryTicketNo() 
    {
        return deliveryTicketNo;
    }
    public void setWithdrawalClass(String withdrawalClass) 
    {
        this.withdrawalClass = withdrawalClass;
    }

    public String getWithdrawalClass() 
    {
        return withdrawalClass;
    }
    public void setPartPrice(String partPrice) 
    {
        this.partPrice = partPrice;
    }

    public String getPartPrice() 
    {
        return partPrice;
    }
    public void setPrototypeSymbol(String prototypeSymbol) 
    {
        this.prototypeSymbol = prototypeSymbol;
    }

    public String getPrototypeSymbol() 
    {
        return prototypeSymbol;
    }
    public void setResendSymbol(String resendSymbol) 
    {
        this.resendSymbol = resendSymbol;
    }

    public String getResendSymbol() 
    {
        return resendSymbol;
    }
    public void setOrderDate(String orderDate) 
    {
        this.orderDate = orderDate;
    }

    public String getOrderDate() 
    {
        return orderDate;
    }
    public void setLabelSize(String labelSize) 
    {
        this.labelSize = labelSize;
    }

    public String getLabelSize() 
    {
        return labelSize;
    }
    public void setReserve4(String reserve4) 
    {
        this.reserve4 = reserve4;
    }

    public String getReserve4() 
    {
        return reserve4;
    }
    public void setReserve5(String reserve5) 
    {
        this.reserve5 = reserve5;
    }

    public String getReserve5() 
    {
        return reserve5;
    }
    public void setMakerPic(String makerPic) 
    {
        this.makerPic = makerPic;
    }

    public String getMakerPic() 
    {
        return makerPic;
    }
    public void setMakerSnp(String makerSnp) 
    {
        this.makerSnp = makerSnp;
    }

    public String getMakerSnp() 
    {
        return makerSnp;
    }
    public void setResendDepot(String resendDepot) 
    {
        this.resendDepot = resendDepot;
    }

    public String getResendDepot() 
    {
        return resendDepot;
    }
    public void setShippingPic(String shippingPic) 
    {
        this.shippingPic = shippingPic;
    }

    public String getShippingPic() 
    {
        return shippingPic;
    }
    public void setPartsNoIdCode(String partsNoIdCode) 
    {
        this.partsNoIdCode = partsNoIdCode;
    }

    public String getPartsNoIdCode() 
    {
        return partsNoIdCode;
    }
    public void setPlantCode(String plantCode) 
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode() 
    {
        return plantCode;
    }
    public void setShippingLocation(String shippingLocation) 
    {
        this.shippingLocation = shippingLocation;
    }

    public String getShippingLocation() 
    {
        return shippingLocation;
    }
    public void setShippingPort(String shippingPort) 
    {
        this.shippingPort = shippingPort;
    }

    public String getShippingPort() 
    {
        return shippingPort;
    }
    public void setReserve6(String reserve6) 
    {
        this.reserve6 = reserve6;
    }

    public String getReserve6() 
    {
        return reserve6;
    }
    public void setDivisionDepot(String divisionDepot) 
    {
        this.divisionDepot = divisionDepot;
    }

    public String getDivisionDepot() 
    {
        return divisionDepot;
    }
    public void setShippingLocationCode2(String shippingLocationCode2) 
    {
        this.shippingLocationCode2 = shippingLocationCode2;
    }

    public String getShippingLocationCode2() 
    {
        return shippingLocationCode2;
    }
    public void setMovementLeadTime(String movementLeadTime) 
    {
        this.movementLeadTime = movementLeadTime;
    }

    public String getMovementLeadTime() 
    {
        return movementLeadTime;
    }
    public void setRehandlingFlag(String rehandlingFlag) 
    {
        this.rehandlingFlag = rehandlingFlag;
    }

    public String getRehandlingFlag() 
    {
        return rehandlingFlag;
    }
    public void setNoMasterFlag(String noMasterFlag) 
    {
        this.noMasterFlag = noMasterFlag;
    }

    public String getNoMasterFlag() 
    {
        return noMasterFlag;
    }
    public void setReserve7(String reserve7) 
    {
        this.reserve7 = reserve7;
    }

    public String getReserve7() 
    {
        return reserve7;
    }
    public void setDivisionDepot2(String divisionDepot2) 
    {
        this.divisionDepot2 = divisionDepot2;
    }

    public String getDivisionDepot2() 
    {
        return divisionDepot2;
    }

    public String getLabelTerminalNo() {
        return labelTerminalNo;
    }

    public void setLabelTerminalNo(String labelTerminalNo) {
        this.labelTerminalNo = labelTerminalNo;
    }

    public String getListTerminalNo() {
        return listTerminalNo;
    }

    public void setListTerminalNo(String listTerminalNo) {
        this.listTerminalNo = listTerminalNo;
    }

    public String getPartsNo() {
        return partsNo;
    }

    public void setPartsNo(String partsNo) {
        this.partsNo = partsNo;
    }

    public String getProductClass() {
        return productClass;
    }

    public void setProductClass(String productClass) {
        this.productClass = productClass;
    }

    public String getProductionMethod() {
        return productionMethod;
    }

    public void setProductionMethod(String productionMethod) {
        this.productionMethod = productionMethod;
    }

    public String getDeliveryMethod2() {
        return deliveryMethod2;
    }

    public void setDeliveryMethod2(String deliveryMethod2) {
        this.deliveryMethod2 = deliveryMethod2;
    }

    public String getFromProcess() {
        return fromProcess;
    }

    public void setFromProcess(String fromProcess) {
        this.fromProcess = fromProcess;
    }

    public String getFromWorkArea() {
        return fromWorkArea;
    }

    public void setFromWorkArea(String fromWorkArea) {
        this.fromWorkArea = fromWorkArea;
    }

    public String getInstructionDate() {
        return instructionDate;
    }

    public void setInstructionDate(String instructionDate) {
        this.instructionDate = instructionDate;
    }

    public String getInstructionTime() {
        return instructionTime;
    }

    public void setInstructionTime(String instructionTime) {
        this.instructionTime = instructionTime;
    }

    public String getInstructionNo() {
        return instructionNo;
    }

    public void setInstructionNo(String instructionNo) {
        this.instructionNo = instructionNo;
    }

    public String getProductionHandlingMethod() {
        return productionHandlingMethod;
    }

    public void setProductionHandlingMethod(String productionHandlingMethod) {
        this.productionHandlingMethod = productionHandlingMethod;
    }

    public String getShippingCheckFlag() {
        return shippingCheckFlag;
    }

    public void setShippingCheckFlag(String shippingCheckFlag) {
        this.shippingCheckFlag = shippingCheckFlag;
    }

    public String getLabelIssueAssignment() {
        return labelIssueAssignment;
    }

    public void setLabelIssueAssignment(String labelIssueAssignment) {
        this.labelIssueAssignment = labelIssueAssignment;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getDemandCode2() {
        return demandCode2;
    }

    public void setDemandCode2(String demandCode2) {
        this.demandCode2 = demandCode2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fileNo", getFileNo())
            .append("reserve1", getReserve1())
            .append("demandCode", getDemandCode())
            .append("issueNo", getIssueNo())
            .append("deliveryInstructionDate", getDeliveryInstructionDate())
            .append("deliveryInstructionTime", getDeliveryInstructionTime())
            .append("maker", getMaker())
            .append("depot", getDepot())
            .append("classification", getClassification())
            .append("deliveryLocation", getDeliveryLocation())
            .append("unloadingUnit", getUnloadingUnit())
            .append("customerPartsNo", getCustomerPartsNo())
            .append("currentProcessClass", getCurrentProcessClass())
            .append("partsName", getPartsName())
            .append("deliveryInstructionNumber", getDeliveryInstructionNumber())
            .append("supplyLocation", getSupplyLocation())
            .append("snep", getSnep())
            .append("safetyFlag", getSafetyFlag())
            .append("labelCount", getLabelCount())
            .append("managementPic", getManagementPic())
            .append("issueReason", getIssueReason())
            .append("vipFlag", getVipFlag())
            .append("packageCode", getPackageCode())
            .append("internalExternalClass", getInternalExternalClass())
            .append("deliveryInstructionCount", getDeliveryInstructionCount())
            .append("deliveryMethod", getDeliveryMethod())
            .append("orderClass", getOrderClass())
            .append("labelId", getLabelId())
            .append("delayPreventionMakerSymbol", getDelayPreventionMakerSymbol())
            .append("shiptoClass", getShiptoClass())
            .append("shiptoCode", getShiptoCode())
            .append("labelOutput", getLabelOutput())
            .append("reserve2", getReserve2())
            .append("labelNoChangeMark", getLabelNoChangeMark())
            .append("selfProcurementLine", getSelfProcurementLine())
            .append("labelSpecification", getLabelSpecification())
            .append("selfProcurementKd", getSelfProcurementKd())
            .append("reserve3", getReserve3())
            .append("kdElements", getKdElements())
            .append("transitCode", getTransitCode())
            .append("transitDeliveryDate", getTransitDeliveryDate())
            .append("partsId", getPartsId())
            .append("deliveryTicketNo", getDeliveryTicketNo())
            .append("withdrawalClass", getWithdrawalClass())
            .append("partPrice", getPartPrice())
            .append("prototypeSymbol", getPrototypeSymbol())
            .append("resendSymbol", getResendSymbol())
            .append("orderDate", getOrderDate())
            .append("labelSize", getLabelSize())
            .append("reserve4", getReserve4())
            .append("reserve5", getReserve5())
            .append("makerPic", getMakerPic())
            .append("makerSnp", getMakerSnp())
            .append("resendDepot", getResendDepot())
            .append("shippingPic", getShippingPic())
            .append("partsNoIdCode", getPartsNoIdCode())
            .append("plantCode", getPlantCode())
            .append("shippingLocation", getShippingLocation())
            .append("shippingPort", getShippingPort())
            .append("reserve6", getReserve6())
            .append("divisionDepot", getDivisionDepot())
            .append("shippingLocationCode2", getShippingLocationCode2())
            .append("movementLeadTime", getMovementLeadTime())
            .append("rehandlingFlag", getRehandlingFlag())
            .append("noMasterFlag", getNoMasterFlag())
            .append("reserve7", getReserve7())
            .append("divisionDepot2", getDivisionDepot2())
            .append("labelTerminalNo", getLabelTerminalNo())
            .append("listTerminalNo", getListTerminalNo())
            .append("partsNo", getPartsNo())
            .append("productClass", getProductClass())
            .append("productionMethod", getProductionMethod())
            .append("deliveryMethod2", getDeliveryMethod2())
            .append("fromProcess", getFromProcess())
            .append("fromWorkArea", getFromWorkArea())
            .append("shippingPic", getShippingPic())
            .append("partsNoIdCode", getPartsNoIdCode())
            .append("instructionDate", getInstructionDate())
            .append("instructionTime", getInstructionTime())
            .append("instructionNo", getInstructionNo())
            .append("productionHandlingMethod", getProductionHandlingMethod())
            .append("plantCode", getPlantCode())
            .append("shippingLocation", getShippingLocation())
            .append("shippingPort", getShippingPort())
            .append("shippingCheckFlag", getShippingCheckFlag())
            .append("reserve6", getReserve6())
            .append("labelIssueAssignment", getLabelIssueAssignment())
            .append("divisionDepot", getDivisionDepot())
            .append("customerCode", getCustomerCode())
            .append("demandCode2", getDemandCode2())
            .append("shippingLocationCode2", getShippingLocationCode2())
            .append("movementLeadTime", getMovementLeadTime())
            .append("rehandlingFlag", getRehandlingFlag())
            .append("noMasterFlag", getNoMasterFlag())
            .append("reserve7", getReserve7())
            .append("divisionDepot2", getDivisionDepot2())
            .toString();
    }
}
