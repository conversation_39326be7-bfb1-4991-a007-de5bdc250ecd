package com.datalink.api.controller;

import com.datalink.api.common.SapApiResult;
import com.datalink.api.domain.SapDeliveryPlan;
import com.datalink.api.domain.dto.SapDeliveryPlanDTO;
import com.datalink.api.domain.dto.SapRequestDTO;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.exception.BaseException;
import com.datalink.common.utils.MessageUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.service.ISapDeliveryPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Api("SAP支给计划相关接口")
@Validated
@RestController
@RequestMapping("/api/sapDeliveryPlan")
public class SapDeliveryPlanApi extends BaseController {

    @Autowired
    private ISapDeliveryPlanService sapDeliveryPlanService;

    /**
     * 查询支给计划列表
     */
//    @PreAuthorize("@ss.hasPermi('datamanage:plan:list')")
    @GetMapping("/list")
    public TableDataInfo list(SapDeliveryPlan requestPlan) {
        // 参数校验
        if (StringUtils.isEmpty(requestPlan.getSelectedYearMonth())) {
            throw new BaseException(MessageUtils.message("supply.plan.yearmonth.empty"));
        }
        try {
            YearMonth.parse(requestPlan.getSelectedYearMonth());
        } catch (Exception e) {
            throw new BaseException("selectedYearMonth格式不正确，应为yyyy-MM格式");
        }
        
        // 先查询原始数据（会自动处理分页）
        startPage();
        List<SapDeliveryPlan> originalList = sapDeliveryPlanService.selectTblSapDeliveryPlanList(requestPlan);
        TableDataInfo tableDataInfo = getDataTable(originalList);
        
        // 转换数据格式
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (SapDeliveryPlan plan : originalList) {
            Map<String, Object> result = sapDeliveryPlanService.convertToWeekData(plan, requestPlan.getSelectedYearMonth());
            resultList.add(result);
        }
        
        // 使用原始查询的分页信息，但返回转换后的数据
        tableDataInfo.setRows(resultList);
        return tableDataInfo;
    }

    /**
     * 接收支给计划
     */
//    @PreAuthorize("@ss.hasPermi('datamanage:plan:add')")
    @Log(title = "接收支给计划", businessType = BusinessType.INSERT)
    @PostMapping("/receiveDeliveryPlan")
    public SapApiResult receiveDeliveryPlan(@RequestBody SapRequestDTO<SapDeliveryPlanDTO> request)
    {
        try {
            sapDeliveryPlanService.insertTblSapDeliveryPlan(request);
            return SapApiResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return SapApiResult.error("处理支给计划数据失败：" + e.getMessage());
        }
    }

    /**
     * 接收支给实际
     */
//    @PreAuthorize("@ss.hasPermi('datamanage:plan:add')")
    @Log(title = "接收支给实际", businessType = BusinessType.INSERT)
    @PostMapping("/receiveDeliveryActual")
    public SapApiResult receiveDeliveryActual(@RequestBody SapRequestDTO<SapDeliveryPlanDTO> request)
    {
        try {
            sapDeliveryPlanService.insertTblSapDeliveryPlan(request);
            return SapApiResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return SapApiResult.error("处理支给实际数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("下载支给计划txt")
    @GetMapping("/downloadDeliveryPlanTxt")
    public AjaxResult downloadDeliveryPlanTxt(SapDeliveryPlan requestPlan) {
        // 参数校验
        if (StringUtils.isEmpty(requestPlan.getSelectedYearMonth())) {
            throw new BaseException(MessageUtils.message("supply.plan.yearmonth.empty"));
        }
        try {
            YearMonth.parse(requestPlan.getSelectedYearMonth());
        } catch (Exception e) {
            throw new BaseException("selectedYearMonth格式不正确，应为yyyy-MM格式");
        }

        List<SapDeliveryPlan> originalList = sapDeliveryPlanService.selectTblSapDeliveryPlanList(requestPlan);
        TableDataInfo tableDataInfo = getDataTable(originalList);

        // 转换数据格式
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (SapDeliveryPlan plan : originalList) {
            Map<String, Object> result = sapDeliveryPlanService.convertToWeekData(plan, requestPlan.getSelectedYearMonth());
            resultList.add(result);
        }

        // 使用原始查询的分页信息，但返回转换后的数据
        tableDataInfo.setRows(resultList);
        return sapDeliveryPlanService.downloadDeliveryPlanTxt(tableDataInfo);
    }

}
