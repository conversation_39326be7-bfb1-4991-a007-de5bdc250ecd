package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 供应商接口数据 tbl_supplier_api
 * 
 * <AUTHOR>
 */
public class SapSupplierRequest
{
    /** 供应商编码 */
    @JsonProperty("LIFNR")
    private String lifnr;

    /** 供应商名称 */
    @JsonProperty("NAME1")
    private String name1;

    /** 供应商类型 */
    @JsonProperty("KTOKK")
    private String ktokk;

    /** 国家 */
    @JsonProperty("LAND1")
    private String land1;

    /** 地区 */
    @JsonProperty("REGIO")
    private String regio;

    /** 地址 */
    @JsonProperty("ADDR2_STREET")
    private String addr2Street;

    /** 邮编 */
    @JsonProperty("ADDR2_POST")
    private String addr2Post;

    /** 传真 */
    @JsonProperty("TELFX")
    private String telfx;

    /** 电话 */
    @JsonProperty("TELX1")
    private String telx1;

    @NotBlank(message = "供应商编码不能为空")
    @Size(min = 0, max = 10, message = "供应商编码长度不能超过10个字符")
    public String getLifnr() {
        return lifnr;
    }

    public void setLifnr(String LIFNR) {
        this.lifnr = LIFNR;
    }

    @Size(min = 0, max = 35, message = "供应商名称长度不能超过35个字符")
    public String getName1() {
        return name1;
    }

    public void setName1(String NAME1) {
        this.name1 = NAME1;
    }

    @Size(min = 0, max = 4, message = "供应商类型长度不能超过4个字符")
    public String getKtokk() {
        return ktokk;
    }

    public void setKtokk(String KTOKK) {
        this.ktokk = KTOKK;
    }

    @Size(min = 0, max = 3, message = "国家长度不能超过3个字符")
    public String getLand1() {
        return land1;
    }

    public void setLand1(String LAND1) {
        this.land1 = LAND1;
    }

    @Size(min = 0, max = 3, message = "地区长度不能超过3个字符")
    public String getRegio() {
        return regio;
    }

    public void setRegio(String REGIO) {
        this.regio = REGIO;
    }

    @Size(min = 0, max = 60, message = "地址长度不能超过60个字符")
    public String getAddr2Street() {
        return addr2Street;
    }

    public void setAddr2Street(String ADDR2_STREET) {
        this.addr2Street = ADDR2_STREET;
    }

    @Size(min = 0, max = 10, message = "邮编长度不能超过10个字符")
    public String getAddr2Post() {
        return addr2Post;
    }

    public void setAddr2Post(String ADDR2_POST) {
        this.addr2Post = ADDR2_POST;
    }

    @Size(min = 0, max = 31, message = "传真长度不能超过31个字符")
    public String getTelfx() {
        return telfx;
    }

    public void setTelfx(String TELFX) {
        this.telfx = TELFX;
    }

    @Size(min = 0, max = 30, message = "电话长度不能超过30个字符")
    public String getTelx1() {
        return telx1;
    }

    public void setTelx1(String TELX1) {
        this.telx1 = TELX1;
    }
} 