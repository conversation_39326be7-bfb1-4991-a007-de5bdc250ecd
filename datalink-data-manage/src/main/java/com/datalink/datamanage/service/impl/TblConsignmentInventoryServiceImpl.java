package com.datalink.datamanage.service.impl;

import java.util.List;
import com.datalink.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import com.datalink.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import com.datalink.datamanage.domain.TblConsignmentInventoryItem;
import com.datalink.datamanage.mapper.TblConsignmentInventoryMapper;
import com.datalink.datamanage.domain.TblConsignmentInventory;
import com.datalink.datamanage.service.ITblConsignmentInventoryService;

/**
 * 寄售库存Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
@Service
public class TblConsignmentInventoryServiceImpl implements ITblConsignmentInventoryService 
{
    @Autowired
    private TblConsignmentInventoryMapper tblConsignmentInventoryMapper;

    /**
     * 查询寄售库存
     * 
     * @param consignmentId 寄售库存ID
     * @return 寄售库存
     */
    @Override
    public TblConsignmentInventory selectTblConsignmentInventoryById(Long consignmentId)
    {
        return tblConsignmentInventoryMapper.selectTblConsignmentInventoryById(consignmentId);
    }

    /**
     * 查询寄售库存列表
     * 
     * @param tblConsignmentInventory 寄售库存
     * @return 寄售库存
     */
    @Override
    public List<TblConsignmentInventory> selectTblConsignmentInventoryList(TblConsignmentInventory tblConsignmentInventory)
    {
        return tblConsignmentInventoryMapper.selectTblConsignmentInventoryList(tblConsignmentInventory);
    }

    /**
     * 新增寄售库存
     * 
     * @param tblConsignmentInventory 寄售库存
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblConsignmentInventory(TblConsignmentInventory tblConsignmentInventory)
    {
        tblConsignmentInventory.setCreateTime(DateUtils.getNowDate());
        int rows = tblConsignmentInventoryMapper.insertTblConsignmentInventory(tblConsignmentInventory);
        insertTblConsignmentInventoryItem(tblConsignmentInventory);
        return rows;
    }

    /**
     * 修改寄售库存
     * 
     * @param tblConsignmentInventory 寄售库存
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblConsignmentInventory(TblConsignmentInventory tblConsignmentInventory)
    {
        tblConsignmentInventory.setUpdateTimeSys(DateUtils.getNowDate());
        tblConsignmentInventoryMapper.deleteTblConsignmentInventoryItemByConsignmentId(tblConsignmentInventory.getConsignmentId());
        insertTblConsignmentInventoryItem(tblConsignmentInventory);
        return tblConsignmentInventoryMapper.updateTblConsignmentInventory(tblConsignmentInventory);
    }

    @Override
    public int updateTblConsignmentInventoryOnly(TblConsignmentInventory tblConsignmentInventory) {
        tblConsignmentInventory.setUpdateTimeSys(DateUtils.getNowDate());
        return tblConsignmentInventoryMapper.updateTblConsignmentInventory(tblConsignmentInventory);
    }

    /**
     * 批量删除寄售库存
     * 
     * @param consignmentIds 需要删除的寄售库存ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblConsignmentInventoryByIds(Long[] consignmentIds)
    {
        tblConsignmentInventoryMapper.deleteTblConsignmentInventoryItemByConsignmentIds(consignmentIds);
        return tblConsignmentInventoryMapper.deleteTblConsignmentInventoryByIds(consignmentIds);
    }

    /**
     * 删除寄售库存信息
     * 
     * @param consignmentId 寄售库存ID
     * @return 结果
     */
    @Override
    public int deleteTblConsignmentInventoryById(Long consignmentId)
    {
        tblConsignmentInventoryMapper.deleteTblConsignmentInventoryItemByConsignmentId(consignmentId);
        return tblConsignmentInventoryMapper.deleteTblConsignmentInventoryById(consignmentId);
    }

    /**
     * 新增寄售行项目信息
     * 
     * @param tblConsignmentInventory 寄售库存对象
     */
    public void insertTblConsignmentInventoryItem(TblConsignmentInventory tblConsignmentInventory)
    {
        List<TblConsignmentInventoryItem> tblConsignmentInventoryItemList = tblConsignmentInventory.getDetail();
        Long consignmentId = tblConsignmentInventory.getConsignmentId();
        if (StringUtils.isNotNull(tblConsignmentInventoryItemList))
        {
            List<TblConsignmentInventoryItem> list = new ArrayList<TblConsignmentInventoryItem>();
            for (TblConsignmentInventoryItem tblConsignmentInventoryItem : tblConsignmentInventoryItemList)
            {
                tblConsignmentInventoryItem.setConsignmentId(consignmentId);
                list.add(tblConsignmentInventoryItem);
            }
            if (list.size() > 0)
            {
                tblConsignmentInventoryMapper.batchTblConsignmentInventoryItem(list);
            }
        }
    }

    /**
     * 查询寄售库存列表(接口专用)
     *
     * @param consignmentInventory 寄售库存
     * @return 寄售库存集合
     */
    @Override
    public List<TblConsignmentInventory> selectTblConsignmentInventoryFullList(TblConsignmentInventory consignmentInventory){
        return tblConsignmentInventoryMapper.selectTblConsignmentInventoryFullList(consignmentInventory);
    }

    @Override
    public List<TblConsignmentInventory> selectTblConsignmentInventoryWithItemList(TblConsignmentInventory consignmentInventory) {
        return tblConsignmentInventoryMapper.selectTblConsignmentInventoryWithItemList(consignmentInventory);
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId(){
        return tblConsignmentInventoryMapper.selectLastId();
    }

    @Override
    public List<TblConsignmentInventoryItem> selectTblConsignmentInventoryItemList(TblConsignmentInventoryItem consignmentInventoryItem) {
        return tblConsignmentInventoryMapper.selectTblConsignmentInventoryItemList(consignmentInventoryItem);
    }

    /**
     * 查询寄售库存(不包含行项目)
     *
     * @param consignmentId 寄售库存ID
     * @return 寄售库存
     */
    @Override
    public TblConsignmentInventory selectTblConsignmentInventoryOnlyById(Long consignmentId) {
        TblConsignmentInventory param = new TblConsignmentInventory();
        param.setConsignmentId(consignmentId);
        return tblConsignmentInventoryMapper.selectTblConsignmentInventoryOnlyById(param);
    }
}
