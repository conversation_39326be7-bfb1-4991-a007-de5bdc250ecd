package com.datalink.datamanage.service.impl;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.List;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Date;
import java.util.TimeZone;

import com.datalink.common.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.datalink.common.annotation.DataScope;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.ServletUtils;
import com.datalink.framework.web.service.PermissionService;
import com.datalink.framework.web.service.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.datalink.datamanage.mapper.TransportPlanMapper;
import com.datalink.datamanage.domain.TransportPlan;
import com.datalink.datamanage.service.ITransportPlanService;

/**
 * 货量提示Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class TransportPlanServiceImpl implements ITransportPlanService 
{
    private static final Logger log = LoggerFactory.getLogger(TransportPlanServiceImpl.class);

    @Autowired
    private TransportPlanMapper transportPlanMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private PermissionService permissionService;

    /**
     * 查询货量提示
     *
     * @param transportPlanId 货量提示ID
     * @param tz
     * @return 货量提示
     */
    @Override
    public TransportPlan selectTransportPlanById(Long transportPlanId, String tz)
    {
        TransportPlan plan = transportPlanMapper.selectTransportPlanById(transportPlanId);
        // 转换时间到用户时区
        convertToUserTimeZone(plan, tz);
        return plan;
    }

    /**
     * 查询货量提示列表
     *
     * @param transportPlan 货量提示
     * @param tz
     * @return 货量提示
     */
    @Override
    @DataScope(supplierAlias = "a", isNoControl = true)
    public List<TransportPlan> selectTransportPlanList(TransportPlan transportPlan, String tz)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        
        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的岗位编码
            Long userId = loginUser.getUser().getUserId();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            transportPlan.setUserId(userId);
        }
        // 查询条件中的时间需要转换为 UTC
        convertSearchTimeToUTC(transportPlan, tz);
        List<TransportPlan> list = transportPlanMapper.selectTransportPlanList(transportPlan);
        // 结果中的时间需要转换为用户时区
        for (TransportPlan plan : list) {
            convertToUserTimeZone(plan, tz);
        }
        return list;
    }

    /**
     * 新增货量提示
     *
     * @param transportPlan 货量提示
     * @param tz
     * @return 结果
     */
    @Override
    public int insertTransportPlan(TransportPlan transportPlan, String tz)
    {
        // 转换时间到 UTC
        convertToUTC(transportPlan, tz);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        transportPlan.setCreateBy(loginUser.getUsername());
        return transportPlanMapper.insertTransportPlan(transportPlan);
    }

    /**
     * 修改货量提示
     *
     * @param transportPlan 货量提示
     * @param tz
     * @return 结果
     */
    @Override
    public int updateTransportPlan(TransportPlan transportPlan, String tz)
    {
        // 转换时间到 UTC
        convertToUTC(transportPlan, tz);
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        transportPlan.setUpdateBy(loginUser.getUsername());
        return transportPlanMapper.updateTransportPlan(transportPlan);
    }

    /**
     * 批量删除货量提示
     * 
     * @param transportPlanIds 需要删除的货量提示ID
     * @return 结果
     */
    @Override
    public int deleteTransportPlanByIds(Long[] transportPlanIds)
    {
        return transportPlanMapper.deleteTransportPlanByIds(transportPlanIds);
    }

    /**
     * 删除货量提示信息
     * 
     * @param transportPlanId 货量提示ID
     * @return 结果
     */
    @Override
    public int deleteTransportPlanById(Long transportPlanId)
    {
        return transportPlanMapper.deleteTransportPlanById(transportPlanId);
    }

    // 转换时间到 UTC
    private void convertToUTC(TransportPlan plan, String tz) {
        if (plan == null) return;
        if (tz == null) tz = "Asia/Tokyo";
        
        ZoneId userZone = ZoneId.of(tz);
        
        // 合并处理 pickupDate 和 pickupTime
        if (plan.getPickupDate() != null && plan.getPickupTime() != null) {
            // 1. 先合并日期和时间，得到用户时区的完整时间
            LocalDateTime userDateTime = combineDateAndTime(plan.getPickupDate(), plan.getPickupTime(), userZone);
            
            // 2. 将用户时区时间转换为 UTC 时间
            ZonedDateTime utcDateTime = userDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            
            // 3. 分别设置日期和时间
            plan.setPickupDate(Date.from(utcDateTime.toLocalDate().atStartOfDay(ZoneOffset.UTC).toInstant()));
            plan.setPickupTime(Date.from(utcDateTime.toInstant()));
        }
        
        // 合并处理 deliveryDate 和 deliveryTime
        if (plan.getDeliveryDate() != null && plan.getDeliveryTime() != null) {
            LocalDateTime userDateTime = combineDateAndTime(plan.getDeliveryDate(), plan.getDeliveryTime(), userZone);
            ZonedDateTime utcDateTime = userDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            plan.setDeliveryDate(Date.from(utcDateTime.toLocalDate().atStartOfDay(ZoneOffset.UTC).toInstant()));
            plan.setDeliveryTime(Date.from(utcDateTime.toInstant()));
        }

        // 其他单独时间字段的处理
        if (plan.getCarrierPickupTime() != null) {
            plan.setCarrierPickupTime(convertDateToUTC(plan.getCarrierPickupTime(), userZone));
        }
        if (plan.getCreateTime() != null) {
            plan.setCreateTime(convertDateToUTC(plan.getCreateTime(), userZone));
        }
        if (plan.getUpdateTime() != null) {
            plan.setUpdateTime(convertDateToUTC(plan.getUpdateTime(), userZone));
        }
    }

    // 转换时间到用户时区
    private void convertToUserTimeZone(TransportPlan plan, String tz) {
        if (plan == null) return;
        if (tz == null) tz = "Asia/Tokyo";

        ZoneId userZone = ZoneId.of(tz);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone(userZone));
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf1.setTimeZone(TimeZone.getTimeZone(userZone));

        // 合并处理 pickupDate 和 pickupTime
        if (plan.getPickupDate() != null && plan.getPickupTime() != null) {
            // 1. 合并日期和时间，得到 UTC 的完整时间
            LocalDateTime utcDateTime = combineDateAndTime(plan.getPickupDate(), plan.getPickupTime(), ZoneOffset.UTC);
            
            // 2. 将 UTC 时间转换为用户时区时间
            ZonedDateTime userDateTime = utcDateTime.atZone(ZoneOffset.UTC).withZoneSameInstant(userZone);
            
            // 3. 分别设置日期和时间
            plan.setPickupDate(DateUtils.parseDate(sdf.format(Date.from(userDateTime.toLocalDate().atStartOfDay(userZone).toInstant()))));
            plan.setPickupTime(DateUtils.parseDate(sdf1.format(Date.from(userDateTime.toInstant()))));
        }
        
        // 合并处理 deliveryDate 和 deliveryTime
        if (plan.getDeliveryDate() != null && plan.getDeliveryTime() != null) {
            LocalDateTime utcDateTime = combineDateAndTime(plan.getDeliveryDate(), plan.getDeliveryTime(), ZoneOffset.UTC);
            ZonedDateTime userDateTime = utcDateTime.atZone(ZoneOffset.UTC).withZoneSameInstant(userZone);
            plan.setDeliveryDate(DateUtils.parseDate(sdf.format(Date.from(userDateTime.toLocalDate().atStartOfDay(userZone).toInstant()))));
            plan.setDeliveryTime(DateUtils.parseDate(sdf1.format(Date.from(userDateTime.toInstant()))));
        }

        // 其他单独时间字段的处理
        if (plan.getCarrierPickupTime() != null) {
            plan.setCarrierPickupTime(DateUtils.parseDate(sdf1.format(convertDateToUserTimeZone(plan.getCarrierPickupTime(), userZone))));
        }
        if (plan.getCreateTime() != null) {
            plan.setCreateTime(DateUtils.parseDate(sdf1.format(convertDateToUserTimeZone(plan.getCreateTime(), userZone))));
        }
        if (plan.getUpdateTime() != null) {
            plan.setUpdateTime(DateUtils.parseDate(sdf1.format(convertDateToUserTimeZone(plan.getUpdateTime(), userZone))));
        }
    }

    // 合并日期和时间
    private LocalDateTime combineDateAndTime(Date date, Date time, ZoneId userZone) {
        // 从日期中获取年月日
        LocalDate datePart = date.toInstant().atZone(userZone).withZoneSameInstant(ZoneOffset.UTC).toLocalDate();
        // 从时间中获取时分秒
        LocalTime timePart = time.toInstant().atZone(userZone).withZoneSameInstant(ZoneOffset.UTC).toLocalTime();
        
        // 合并日期和时间
        return LocalDateTime.of(datePart, timePart);
    }

    // 转换查询条件中的时间为 UTC
    private void convertSearchTimeToUTC(TransportPlan transportPlan, String tz) {
        if (transportPlan == null || transportPlan.getParams() == null) return;
        if (tz == null) tz = "Asia/Tokyo";
        
        Map<String, Object> params = transportPlan.getParams();
        ZoneId userZone = ZoneId.of(tz);

        // 处理时间范围查询
        convertDateRangeToUTC(params, "pickupDateBegin", "pickupDateEnd", userZone);
        convertDateRangeToUTC(params, "deliveryDateBegin", "deliveryDateEnd", userZone);
        convertDateRangeToUTC(params, "createTimeBegin", "createTimeEnd", userZone);
        convertDateRangeToUTC(params, "updateTimeBegin", "updateTimeEnd", userZone);
    }

    // 时间转换工具方法
    private Date convertDateToUTC(Date date, ZoneId fromZone) {
        if (date == null) return null;
        return Date.from(date.toInstant()
            .atZone(fromZone)
            .withZoneSameInstant(ZoneOffset.UTC)
            .toInstant());
    }

    private Date convertDateToUserTimeZone(Date date, ZoneId toZone) {
        if (date == null) return null;
        return Date.from(date.toInstant()
                .atZone(ZoneOffset.UTC)
            .withZoneSameInstant(toZone)
            .toInstant());
    }

    private void convertDateRangeToUTC(Map<String, Object> params, String beginKey, String endKey, ZoneId fromZone) {
        String beginStr = (String) params.get(beginKey);
        String endStr = (String) params.get(endKey);
        
        if (beginStr != null && endStr != null) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime beginDateTime = LocalDateTime.parse(beginStr, formatter);
                LocalDateTime endDateTime = LocalDateTime.parse(endStr, formatter);

                ZonedDateTime utcBegin = beginDateTime.atZone(fromZone).withZoneSameInstant(ZoneOffset.UTC);
                ZonedDateTime utcEnd = endDateTime.atZone(fromZone).withZoneSameInstant(ZoneOffset.UTC);

                params.put(beginKey, utcBegin.format(formatter));
                params.put(endKey, utcEnd.format(formatter));
            } catch (Exception e) {
                // 处理日期解析异常
                log.error("Date conversion error", e);
            }
        }
    }

}
