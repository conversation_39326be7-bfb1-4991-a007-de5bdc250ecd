package com.datalink.datamanage.domain;

import com.datalink.common.DataType;
import com.datalink.common.annotation.Excel;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

public class NoticeReply extends TblNoticeReply implements KafkaData {

    /** 公告编号 */
    @Excel(name = "公告编号")
    private String noticeCode;

    /** 公告标题 */
    @Excel(name = "公告标题")
    private String noticeTitle;

    /** 公告类型（1通知 2公告） */
    private String noticeType;

    /** 公告内容 */
    @Excel(name = "公告内容")
    private String noticeContent;

    /** 公告状态（0草稿 1已发布） */
    private String status;

    /** 公司代码 */
    private String compCode;

    /** 工厂 */
    private String plantCode;

    /** 工厂名称 */
    private String plantName;

    /** 收发标志 */
    @Excel(name = "收发标志")
    private String direction;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    private List<TblAttachment> attachmentList;

    private String needReply;

    @Override
    public DataType getObjectType() {
        return DataType.NOTICE_TYPE;
    }

    public String getNoticeCode() {
        return noticeCode;
    }

    public void setNoticeCode(String noticeCode) {
        this.noticeCode = noticeCode;
    }

    public String getNoticeTitle() {
        return noticeTitle;
    }

    public void setNoticeTitle(String noticeTitle) {
        this.noticeTitle = noticeTitle;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String getCompCode() {
        return compCode;
    }

    public void setCompCode(String compCode) {
        this.compCode = compCode;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getPlantName() {
        return plantName;
    }

    public void setPlantName(String plantName) {
        this.plantName = plantName;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getNeedReply() {
        return needReply;
    }

    public void setNeedReply(String needReply) {
        this.needReply = needReply;
    }

    public List<TblAttachment> getAttachmentList() {
        return attachmentList;
    }

    public void setAttachmentList(List<TblAttachment> attachmentList) {
        this.attachmentList = attachmentList;
    }
}
