-- 添加结算单确认所需的字段
ALTER TABLE tbl_feedback ADD COLUMN Settlement_No VARCHAR(50) COMMENT '结算单号';
ALTER TABLE tbl_feedback ADD COLUMN Invoice_Date VARCHAR(20) COMMENT '开票日期';
ALTER TABLE tbl_feedback ADD COLUMN Invoice_Amount DECIMAL(18,2) COMMENT '开票总金额';
ALTER TABLE tbl_feedback ADD COLUMN Invoice_Tax DECIMAL(18,2) COMMENT '开票总税额';
ALTER TABLE tbl_feedback ADD COLUMN Invoice_No VARCHAR(50) COMMENT '金税发票号';

-- 更新已有记录的状态为New
UPDATE tbl_feedback SET Status = 'New' WHERE Status IS NULL OR Status = '';

-- 添加结算单确认权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('结算单确认', (SELECT menu_id FROM (SELECT menu_id FROM sys_menu WHERE perms = 'datamanage:feedback:list') AS temp), 5, '', NULL, 1, 0, 'F', '0', '0', 'datamanage:feedback:confirm', '#', 'admin', NOW(), '', NULL, ''); 