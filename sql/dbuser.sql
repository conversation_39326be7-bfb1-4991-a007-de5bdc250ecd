CREATE DATABASE `datalink_BHSS`;

CREATE USER `BHSS`@`%` IDENTIFIED BY 'P@ss123456';

<PERSON><PERSON><PERSON>er, <PERSON><PERSON>ine, <PERSON>reate, Create Routine, Create Temporary Tables, Create View, Delete, Drop, Event, Execute, Grant Option, Index, Insert, Lock Tables, References, Select, Show View, Trigger, Update ON `datalink_BHSS`.* TO `BHSS`@`%`;

GRANT Select ON `datalink_base`.* TO `BHSS`@`%`;