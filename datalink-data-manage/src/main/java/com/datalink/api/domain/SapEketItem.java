package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * SAP交货计划DTO
 */
@Setter
@Getter
public class SapEketItem {

    /** 交货计划行号 */
    @JsonProperty("ETENR")
    private String ETENR;

    /** 交货日期 */
    @JsonProperty("EINDT")
    private String EINDT;

    /** 交货数量 */
    @JsonProperty("MENGE")
    private String MENGE;

    @Override
    public String toString() {
        return "SapEketItem{" +
                "ETENR='" + ETENR + '\'' +
                ", EINDT='" + EINDT + '\'' +
                ", MENGE='" + MENGE + '\'' +
                '}';
    }
}
