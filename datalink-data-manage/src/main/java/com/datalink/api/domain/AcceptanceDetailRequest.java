package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 验收明细批量接收请求对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class AcceptanceDetailRequest {

    /** 结算单号 */
    @JsonProperty("ZSETT")
    @NotEmpty(message = "结算单号不能为空")
    private String zsett;

    /** 删除标识 */
    @JsonProperty("LOEKZ")
    private String loekz;

    /** 供应商编号 */
    @JsonProperty("LIFNR")
    @NotEmpty(message = "供应商编号不能为空")
    private String lifnr;

    /** 供应商名称 */
    @JsonProperty("NAME1")
    @NotEmpty(message = "供应商名称不能为空")
    private String name1;

    /** 凭证日期 */
    @JsonProperty("BLDAT")
    private String bldat;

    /** 金额 */
    @JsonProperty("WRBTR")
    private String wrbtr;

    /** 公司代码 */
    @JsonProperty("BUKRS")
    @NotEmpty(message = "公司代码不能为空")
    private String bukrs;

    /** 公司名称 */
    @JsonProperty("BUTXT")
    private String butxt;

    /** 货币 */
    @JsonProperty("WAERS")
    private String waers;

    /** 明细项目 */
    @JsonProperty("ITEMS")
    @Valid
    @NotNull(message = "明细项目不能为空")
    private List<AcceptanceDetailItem> items;

    // Getters and Setters
    public String getZsett() {
        return zsett;
    }

    public void setZsett(String zsett) {
        this.zsett = zsett;
    }

    public String getLoekz() {
        return loekz;
    }

    public void setLoekz(String loekz) {
        this.loekz = loekz;
    }

    public String getLifnr() {
        return lifnr;
    }

    public void setLifnr(String lifnr) {
        this.lifnr = lifnr;
    }

    public String getName1() {
        return name1;
    }

    public void setName1(String name1) {
        this.name1 = name1;
    }

    public String getBldat() {
        return bldat;
    }

    public void setBldat(String bldat) {
        this.bldat = bldat;
    }

    public String getWrbtr() {
        return wrbtr;
    }

    public void setWrbtr(String wrbtr) {
        this.wrbtr = wrbtr;
    }

    public String getBukrs() {
        return bukrs;
    }

    public void setBukrs(String bukrs) {
        this.bukrs = bukrs;
    }

    public String getButxt() {
        return butxt;
    }

    public void setButxt(String butxt) {
        this.butxt = butxt;
    }

    public String getWaers() {
        return waers;
    }

    public void setWaers(String waers) {
        this.waers = waers;
    }

    public List<AcceptanceDetailItem> getItems() {
        return items;
    }

    public void setItems(List<AcceptanceDetailItem> items) {
        this.items = items;
    }
}
