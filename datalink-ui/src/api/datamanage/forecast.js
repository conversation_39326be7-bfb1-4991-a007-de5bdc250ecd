import request from '@/utils/request'

// 查询预测列表
export function listForecast(query) {
  return request({
    url: '/datamanage/forecast/list',
    method: 'get',
    params: query
  })
}

// 查询预测详细
export function getForecast(forecastid) {
  return request({
    url: '/datamanage/forecast/' + forecastid,
    method: 'get'
  })
}

// 查询预测详细(不包含行项目)
export function getForecastOnly(forecastid) {
  return request({
    url: '/datamanage/forecast/head/' + forecastid,
    method: 'get'
  })
}

// 新增预测
export function addForecast(data) {
  return request({
    url: '/datamanage/forecast',
    method: 'post',
    data: data
  })
}

// 修改预测
export function updateForecast(data) {
  return request({
    url: '/datamanage/forecast',
    method: 'put',
    data: data
  })
}

// 删除预测
export function delForecast(forecastid) {
  return request({
    url: '/datamanage/forecast/' + forecastid,
    method: 'delete'
  })
}

// 导出预测
export function exportForecast(query) {
  return request({
    url: '/datamanage/forecast/export',
    method: 'get',
    params: query
  })
}

// 查询行项目
export function listForecastItems(query) {
  return request({
    url: '/datamanage/forecast/listItems',
    method: 'get',
    params: query
  })
}

export function getLatestYearForecast(query) {
  return request({
    url: '/api/sapForecast/getLatestYearForecast',
    method: 'get',
    params: query
  })
}
export function getLatestMonthForecast(query) {
  return request({
    url: '/api/sapForecast/getLatestMonthForecast',
    method: 'get',
    params: query
  })
}
export function getLatestWeekForecast(query) {
  return request({
    url: '/api/sapForecast/getLatestWeekForecast',
    method: 'get',
    params: query
  })
}
