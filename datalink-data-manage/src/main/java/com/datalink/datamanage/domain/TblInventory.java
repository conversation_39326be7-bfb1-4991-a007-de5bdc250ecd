package com.datalink.datamanage.domain;

import com.datalink.common.DataType;
import com.datalink.common.annotation.Excel;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存对象 tbl_inventory
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
//TODO: 校验
public class TblInventory extends BaseHeadEntity implements Serializable, KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 库存ID */
    private Long inventoryId;

    /** 公司代码 */
    @Excel(name = "公司代码")
    @JsonAlias("compcode")
    @NotEmpty(message = "公司代码不能为空")
    @Size(max = 10, message = "公司代码超长")
    private String compCode;

    /** 工厂 */
    @Excel(name = "工厂")
    @JsonAlias("plantcode")
    @NotEmpty(message = "工厂代码不能为空")
    @Size(max = 40, message = "工厂代码超长")
    private String plantCode;

    /** 工厂名称 */
    @Excel(name = "工厂名称")
    @JsonAlias("plantname")
    @NotEmpty(message = "工厂名称不能为空")
    @Size(max = 40, message = "工厂名称超长")
    private String plantName;

    /** 供应商 */
    @Excel(name = "供应商")
    @JsonAlias("suppcode")
    @NotEmpty(message = "供应商代码不能为空")
    @Size(max = 40, message = "供应商代码超长")
    private String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    @JsonAlias("suppname")
    @NotEmpty(message = "供应商名称不能为空")
    @Size(max = 40, message = "供应商名称超长")
    private String suppName;

    /** 更新日期 */
    @JsonFormat(pattern = "yyyyMMdd", timezone = "GMT+8")
    @Excel(name = "更新日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonAlias("updatedate")
    @NotNull(message = "更新日期不能为空")
    private Date updateDate;

    /** 更新时间 */
    @JsonFormat(pattern = "HHmmss", timezone = "GMT+8")
    @Excel(name = "更新时间", width = 30, dateFormat = "HH:mm:ss")
    @JsonAlias("updatetime")
    @NotNull(message = "更新时间不能为空")
    private Date updateTime;

    /** 系统更新时间 */
    private Date updateTimeSys;

    /** 收发标志 */
    private String direction;

    /** KafKa发送状态 */
    private String kafkaStatus;

    /** 搜索值 */
    private String searchValue;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 请求参数 */
    private Map<String, Object> params;

    /** 库存行项目信息 */
    private List<TblInventoryItem> detail;

    public void setInventoryId(Long inventoryId) 
    {
        this.inventoryId = inventoryId;
    }

    public Long getInventoryId() 
    {
        return inventoryId;
    }
    public void setCompCode(String compCode) 
    {
        this.compCode = compCode;
    }

    public String getCompCode() 
    {
        return compCode;
    }
    public void setPlantCode(String plantCode) 
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode() 
    {
        return plantCode;
    }
    public void setPlantName(String plantName) 
    {
        this.plantName = plantName;
    }

    public String getPlantName() 
    {
        return plantName;
    }
    public void setSuppCode(String suppCode) 
    {
        this.suppCode = suppCode;
    }

    public String getSuppCode() 
    {
        return suppCode;
    }
    public void setSuppName(String suppName) 
    {
        this.suppName = suppName;
    }

    public String getSuppName() 
    {
        return suppName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setUpdateTimeSys(Date updateTimeSys) 
    {
        this.updateTimeSys = updateTimeSys;
    }

    public Date getUpdateTimeSys() 
    {
        return updateTimeSys;
    }
    public void setDirection(String direction) 
    {
        this.direction = direction;
    }

    public String getDirection() 
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus) 
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus() 
    {
        return kafkaStatus;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<TblInventoryItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblInventoryItem> detail)
    {
        this.detail = detail;
    }

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Map<String, Object> getParams() {
        if (params == null)
        {
            params = new HashMap<>();
        }
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("inventoryId", getInventoryId())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("updateDate", getUpdateDate())
            .append("updateTime", getUpdateTime())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTimeSys", getUpdateTimeSys())
            .append("updateBy", getUpdateBy())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("detail", getDetail())
            .toString();
    }

    @Override
    public DataType getObjectType() {
        return DataType.INVENTORY_TYPE;
    }
}
