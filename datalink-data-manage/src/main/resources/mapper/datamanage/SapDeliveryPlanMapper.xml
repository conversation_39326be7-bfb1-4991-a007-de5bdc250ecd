<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.SapDeliveryPlanMapper">
    
    <resultMap type="SapDeliveryPlan" id="TblSapDeliveryPlanResult">
        <result property="deliveryPlanId"    column="delivery_plan_id"    />
        <result property="client"    column="supp_code"    />
        <result property="depot"    column="depot"    />
        <result property="factory"    column="factory"    />
        <result property="partNumber"    column="part_number"    />
        <result property="productCategory"    column="product_category"    />
        <result property="deliveryMethod"    column="delivery_method"    />
        <result property="deliveryLocation"    column="delivery_location"    />
        <result property="allocationLot"    column="allocation_lot"    />
        <result property="supplyType"    column="supply_type"    />
        <result property="basicUnit"    column="basic_unit"    />
        <result property="snep"    column="snep"    />
        <result property="supplyMethod"    column="supply_method"    />
        <result property="orderUnit"    column="order_unit"    />
        <result property="unitCondition"    column="unit_condition"    />
        <result property="creationDate"    column="creation_date"    />
        <result property="clientInventory"    column="client_inventory"    />
        <result property="confirmedDate"    column="confirmed_date"    />
        <result property="updatedDateTime"    column="updated_date_time"    />
    </resultMap>

    <resultMap id="TblSapDeliveryPlanDetailResult" type="SapDeliveryPlanDetail">
        <id property="deliveryPlanDetailId" column="sub_delivery_plan_detail_id"/>
        <result property="deliveryPlanId" column="sub_delivery_plan_id"/>
        <result property="supplyPlanDate" column="sub_supply_plan_date"/>
        <result property="supplyPlanQuantity" column="sub_supply_plan_quantity"/>
        <result property="supplyActualQuantity" column="sub_supply_actual_quantity"/>
        <result property="supplyActualWeight" column="sub_supply_actual_weight"/>
        <result property="lastMonthRemainingActual" column="last_month_remaining_actual"/>
        <result property="lastMonthRemainingPlan" column="last_month_remaining_plan"/>
    </resultMap>

    <sql id="selectTblSapDeliveryPlanVo">
        select delivery_plan_id, supp_code, depot, factory, part_number, product_category, delivery_method, delivery_location, allocation_lot, supply_type, basic_unit, snep, supply_method, order_unit, unit_condition, creation_date, client_inventory, confirmed_date, updated_date_time, created_time, updated_time from tbl_sap_delivery_plan a
    </sql>

    <select id="selectTblSapDeliveryPlanList" parameterType="SapDeliveryPlan" resultMap="TblSapDeliveryPlanResult">
        <include refid="selectTblSapDeliveryPlanVo"/>
        <where>
            <if test="client != null  and client != ''"> and supp_code = #{client}</if>
            <if test="depot != null"> and depot = #{depot}</if>
            <if test="depot == null"> and depot = ''</if>
            <if test="factory != null  and factory != ''"> and factory = #{factory}</if>
            <if test="partNumber != null  and partNumber != ''"> and part_number = #{partNumber}</if>
            <if test="productCategory != null  and productCategory != ''"> and product_category = #{productCategory}</if>
            <if test="deliveryMethod != null  and deliveryMethod != ''"> and delivery_method = #{deliveryMethod}</if>
            <if test="deliveryLocation != null  and deliveryLocation != ''"> and delivery_location = #{deliveryLocation}</if>
            <if test="allocationLot != null  and allocationLot != ''"> and allocation_lot = #{allocationLot}</if>
            <if test="supplyType != null  and supplyType != ''"> and supply_type = #{supplyType}</if>
            <if test="basicUnit != null  and basicUnit != ''"> and basic_unit = #{basicUnit}</if>
            <if test="snep != null "> and snep = #{snep}</if>
            <if test="supplyMethod != null  and supplyMethod != ''"> and supply_method = #{supplyMethod}</if>
            <if test="orderUnit != null  and orderUnit != ''"> and order_unit = #{orderUnit}</if>
            <if test="unitCondition != null  and unitCondition != ''"> and unit_condition = #{unitCondition}</if>
            <if test="creationDate != null  and creationDate != ''"> and creation_date = #{creationDate}</if>
            <if test="clientInventory != null  and clientInventory != ''"> and client_inventory = #{clientInventory}</if>
            <if test="confirmedDate != null  and confirmedDate != ''"> and confirmed_date = #{confirmedDate}</if>
            <if test="updatedDateTime != null  and updatedDateTime != ''"> and updated_date_time = #{updatedDateTime}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by delivery_plan_id desc
    </select>
    
    <select id="selectTblSapDeliveryPlanDetail" parameterType="SapDeliveryPlan" resultMap="TblSapDeliveryPlanDetailResult">
        SELECT 
            MAX(sub_delivery_plan_detail_id) as sub_delivery_plan_detail_id,
            sub_delivery_plan_id,
            sub_supply_plan_date,
            SUM(sub_supply_plan_quantity) as sub_supply_plan_quantity,
            SUM(sub_supply_actual_quantity) as sub_supply_actual_quantity,
            MAX(sub_supply_actual_weight) as sub_supply_actual_weight,
            MAX(last_month_remaining_actual) as last_month_remaining_actual,
            MAX(last_month_remaining_plan) as last_month_remaining_plan
        FROM (
            SELECT
                a.delivery_plan_detail_id as sub_delivery_plan_detail_id,
                a.delivery_plan_id as sub_delivery_plan_id,
                a.supply_plan_date as sub_supply_plan_date,
                a.supply_plan_quantity as sub_supply_plan_quantity,
                0 as sub_supply_actual_quantity,
                NULL as sub_supply_actual_weight,
                0 as last_month_remaining_actual,
                a.last_month_remaining_plan as last_month_remaining_plan
            FROM tbl_sap_delivery_plan_detail a
            WHERE a.delivery_plan_id = #{deliveryPlanId}
                AND a.delete_flag is null
                <if test="selectedYearMonth != null and selectedYearMonth != ''">
                    AND DATE_FORMAT(a.supply_plan_date, '%Y-%m') = #{selectedYearMonth}
                </if>
            
            UNION ALL
            
            SELECT
                b.delivery_actual_detail_id as sub_delivery_plan_detail_id,
                b.delivery_plan_id as sub_delivery_plan_id,
                b.supply_actual_date as sub_supply_plan_date,
                0 as sub_supply_plan_quantity,
                b.supply_actual_quantity as sub_supply_actual_quantity,
                b.supply_actual_weight as sub_supply_actual_weight,
                b.last_month_remaining_actual as last_month_remaining_actual,
                0 as last_month_remaining_plan
            FROM tbl_sap_delivery_actual_detail b
            WHERE b.delivery_plan_id = #{deliveryPlanId}
                <if test="selectedYearMonth != null and selectedYearMonth != ''">
                    AND DATE_FORMAT(b.supply_actual_date, '%Y-%m') = #{selectedYearMonth}
                </if>
        ) combined
        GROUP BY sub_delivery_plan_id, sub_supply_plan_date
        ORDER BY sub_supply_plan_date
    </select>

    <insert id="insertTblSapDeliveryPlan" parameterType="SapDeliveryPlan" useGeneratedKeys="true" keyProperty="deliveryPlanId">
        insert into tbl_sap_delivery_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="client != null">supp_code,</if>
            <if test="depot != null">depot,</if>
            <if test="factory != null">factory,</if>
            <if test="partNumber != null">part_number,</if>
            <if test="productCategory != null">product_category,</if>
            <if test="deliveryMethod != null">delivery_method,</if>
            <if test="deliveryLocation != null">delivery_location,</if>
            <if test="allocationLot != null">allocation_lot,</if>
            <if test="supplyType != null">supply_type,</if>
            <if test="basicUnit != null">basic_unit,</if>
            <if test="snep != null">snep,</if>
            <if test="supplyMethod != null">supply_method,</if>
            <if test="orderUnit != null">order_unit,</if>
            <if test="unitCondition != null">unit_condition,</if>
            <if test="creationDate != null">creation_date,</if>
            <if test="clientInventory != null">client_inventory,</if>
            <if test="confirmedDate != null">confirmed_date,</if>
            <if test="updatedDateTime != null">updated_date_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="client != null">#{client},</if>
            <if test="depot != null">#{depot},</if>
            <if test="factory != null">#{factory},</if>
            <if test="partNumber != null">#{partNumber},</if>
            <if test="productCategory != null">#{productCategory},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="deliveryLocation != null">#{deliveryLocation},</if>
            <if test="allocationLot != null">#{allocationLot},</if>
            <if test="supplyType != null">#{supplyType},</if>
            <if test="basicUnit != null">#{basicUnit},</if>
            <if test="snep != null">#{snep},</if>
            <if test="supplyMethod != null">#{supplyMethod},</if>
            <if test="orderUnit != null">#{orderUnit},</if>
            <if test="unitCondition != null">#{unitCondition},</if>
            <if test="creationDate != null">#{creationDate},</if>
            <if test="clientInventory != null">#{clientInventory},</if>
            <if test="confirmedDate != null">#{confirmedDate},</if>
            <if test="updatedDateTime != null">#{updatedDateTime},</if>
         </trim>
    </insert>

    <update id="updateTblSapDeliveryPlan" parameterType="SapDeliveryPlan">
        update tbl_sap_delivery_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="client != null">supp_code = #{client},</if>
            <if test="depot != null">depot = #{depot},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="partNumber != null">part_number = #{partNumber},</if>
            <if test="productCategory != null">product_category = #{productCategory},</if>
            <if test="deliveryMethod != null">delivery_method = #{deliveryMethod},</if>
            <if test="deliveryLocation != null">delivery_location = #{deliveryLocation},</if>
            <if test="allocationLot != null">allocation_lot = #{allocationLot},</if>
            <if test="supplyType != null">supply_type = #{supplyType},</if>
            <if test="basicUnit != null">basic_unit = #{basicUnit},</if>
            <if test="snep != null">snep = #{snep},</if>
            <if test="supplyMethod != null">supply_method = #{supplyMethod},</if>
            <if test="orderUnit != null">order_unit = #{orderUnit},</if>
            <if test="unitCondition != null">unit_condition = #{unitCondition},</if>
            <if test="creationDate != null">creation_date = #{creationDate},</if>
            <if test="clientInventory != null">client_inventory = #{clientInventory},</if>
            <if test="confirmedDate != null">confirmed_date = #{confirmedDate},</if>
            <if test="updatedDateTime != null">updated_date_time = #{updatedDateTime},</if>
        </trim>
        where delivery_plan_id = #{deliveryPlanId}
    </update>

    <delete id="deleteTblSapDeliveryPlanById" parameterType="Long">
        delete from tbl_sap_delivery_plan where delivery_plan_id = #{deliveryPlanId}
    </delete>

    <delete id="deleteTblSapDeliveryPlanByIds" parameterType="String">
        delete from tbl_sap_delivery_plan where delivery_plan_id in 
        <foreach item="deliveryPlanId" collection="array" open="(" separator="," close=")">
            #{deliveryPlanId}
        </foreach>
    </delete>
    
    <delete id="deleteTblSapDeliveryPlanDetailByDeliveryPlanIds" parameterType="String">
        delete from tbl_sap_delivery_plan_detail where delivery_plan_id in 
        <foreach item="deliveryPlanId" collection="array" open="(" separator="," close=")">
            #{deliveryPlanId}
        </foreach>
    </delete>

    <delete id="deleteTblSapDeliveryPlanDetailByDeliveryPlanId" parameterType="Long">
        delete from tbl_sap_delivery_plan_detail where delivery_plan_id = #{deliveryPlanId}
    </delete>

    <insert id="batchTblSapDeliveryPlanDetail">
        insert into tbl_sap_delivery_plan_detail( delivery_plan_detail_id, delivery_plan_id, supply_plan_date, supply_plan_quantity) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.deliveryPlanDetailId}, #{item.deliveryPlanId}, #{item.supplyPlanDate}, #{item.supplyPlanQuantity})
        </foreach>
    </insert>

    <!-- 新增或更新主表记录 -->
    <insert id="insertOrUpdateDeliveryPlan" parameterType="SapDeliveryPlan" useGeneratedKeys="true" keyProperty="deliveryPlanId">
        INSERT INTO tbl_sap_delivery_plan (
            supp_code, depot, factory, part_number,
            product_category, delivery_method, delivery_location,
            allocation_lot, supply_type, basic_unit, snep, 
            supply_method, order_unit, unit_condition, 
            creation_date, client_inventory,
            confirmed_date, updated_date_time
        ) 
        VALUES (
            #{client}, #{depot}, #{factory}, #{partNumber},
            #{productCategory}, #{deliveryMethod}, #{deliveryLocation},
            #{allocationLot}, #{supplyType}, #{basicUnit}, #{snep},
            #{supplyMethod}, #{orderUnit}, #{unitCondition},
            #{creationDate}, #{clientInventory},
            #{confirmedDate}, #{updatedDateTime}
        )
        ON DUPLICATE KEY UPDATE
            product_category = VALUES(product_category),
            delivery_method = VALUES(delivery_method),
            delivery_location = VALUES(delivery_location),
            allocation_lot = VALUES(allocation_lot),
            supply_type = VALUES(supply_type),
            basic_unit = VALUES(basic_unit),
            snep = VALUES(snep),
            supply_method = VALUES(supply_method),
            order_unit = VALUES(order_unit),
            unit_condition = VALUES(unit_condition),
            creation_date = VALUES(creation_date),
            client_inventory = VALUES(client_inventory),
            confirmed_date = VALUES(confirmed_date),
            updated_date_time = VALUES(updated_date_time),
            updated_time = CURRENT_TIMESTAMP;
        
        <!-- 如果是更新，需要额外查询获取ID -->
        <selectKey keyProperty="deliveryPlanId" resultType="Long" order="AFTER">
                SELECT delivery_plan_id
                FROM tbl_sap_delivery_plan
                WHERE supp_code = #{client}
                    <if test="depot != null">AND depot = #{depot}</if>
                    AND factory = #{factory}
                    AND part_number = #{partNumber}
        </selectKey>
    </insert>

    <!-- 新增或更新明细表记录 -->
    <insert id="insertOrUpdateDeliveryPlanDetail" parameterType="SapDeliveryPlanDetail">
        INSERT INTO tbl_sap_delivery_plan_detail (
            delivery_plan_id,
            supply_plan_date,
            supply_plan_quantity,
            delete_flag,
            rsnum,
            rspos,
            last_month_remaining_plan,
            created_time,
            updated_time
        )
        SELECT
            #{deliveryPlanId},
            #{supplyPlanDate},
            #{supplyPlanQuantity},
            #{deleteFlag},
            #{rsnum},
            #{rspos},
            #{lastMonthRemainingPlan},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        FROM tbl_sap_delivery_plan
        WHERE delivery_plan_id = #{deliveryPlanId}
        ON DUPLICATE KEY UPDATE
            supply_plan_quantity = VALUES(supply_plan_quantity),
            delete_flag = VALUES(delete_flag),
            last_month_remaining_plan = VALUES(last_month_remaining_plan),
            updated_time = CURRENT_TIMESTAMP
    </insert>

    <!-- 新增或更新明细表记录 -->
    <insert id="insertOrUpdateDeliveryActualDetail" parameterType="SapDeliveryPlanDetail">
        INSERT INTO tbl_sap_delivery_actual_detail (
            delivery_plan_id,
            supply_actual_date,
            supply_actual_quantity,
            delivery_note,
            delivery_note_item_number,
            supply_actual_weight,
            last_month_remaining_actual
        )
        SELECT
            #{deliveryPlanId},
            #{supplyActualDate},
            #{supplyActualQuantity},
            #{deliveryNote},
            #{deliveryNoteItemNumber},
            #{supplyActualWeight},
            #{lastMonthRemainingActual}
        FROM tbl_sap_delivery_plan
        WHERE delivery_plan_id = #{deliveryPlanId}
        ON DUPLICATE KEY UPDATE
            delivery_note = VALUES(delivery_note),
            delivery_note_item_number = VALUES(delivery_note_item_number),
            supply_actual_quantity = VALUES(supply_actual_quantity),
            supply_actual_weight = VALUES(supply_actual_weight),
            last_month_remaining_actual = VALUES(last_month_remaining_actual),
            updated_time = CURRENT_TIMESTAMP
    </insert>
</mapper>