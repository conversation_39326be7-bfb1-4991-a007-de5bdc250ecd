<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('feedback.detail.receiptFeedbackInfo') }}
      </div>
      <el-table
        border
        :show-header="false"
        :data="baseTableData">

        <el-table-column
          prop="title1"
          :label="$t('feedback.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('feedback.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          :label="$t('feedback.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('feedback.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <p></p>
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('feedback.detail.lineItemInfo') }}
      </div>
      <el-table v-loading="loading" :data="feedbackitemList">
        <el-table-column :label="$t('feedback.detail.orderCode')" align="center" prop="orderCode" />
        <el-table-column :label="$t('feedback.detail.orderLineNo')" align="center" prop="orderLineNo" />
        <el-table-column :label="$t('feedback.detail.articleNo')" align="center" prop="articleNo" />
        <el-table-column :label="$t('feedback.detail.articleName')" align="center" prop="articleName" />
        <el-table-column :label="$t('feedback.detail.rcvDate')" align="center" prop="rcvDate" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.rcvDate.slice(0, 4) + '-' + scope.row.rcvDate.slice(4, 6) + '-' +  scope.row.rcvDate.slice(6, 8) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column :label="$t('feedback.detail.rcvTime')" align="center" prop="rcvTime" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.rcvTime.slice(0, 2) + ':' + scope.row.rcvTime.slice(2, 4) + ':' +  scope.row.rcvTime.slice(4, 6) }}</span>
          </template>
        </el-table-column> -->
        <el-table-column :label="$t('feedback.detail.quantity')" align="center" prop="quantity" />
        <el-table-column :label="$t('feedback.detail.unit')" align="center" prop="unit" />
        <el-table-column :label="$t('feedback.detail.rcvDocNo')" align="center" prop="rcvDocNo" />
        <!-- <el-table-column :label="$t('feedback.detail.articleDocAnnual')" align="center" prop="articleDocAnnual" /> -->
        <el-table-column :label="$t('feedback.detail.rcvDocItemNo')" align="center" prop="rcvDocItemNo" />

      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { getFeedbackOnly, listFeedbackItems} from "@/api/datamanage/feedback";

export default {
  name: "FeedbackDetail",
  data(){
    return {
      feedback: {},
      feedbackitemList: [],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        feedId: null
      }
    };
  },
  created() {
    const feedbackId = this.$route.params.feedbackId;
    this.queryParams.feedId = feedbackId;
    this.getFeedback(feedbackId);
    this.getList();
  },
  methods: {
    getFeedback(feedbackId) {
      getFeedbackOnly(feedbackId).then(res => {
        this.feedback = res.data;
      });
    },
    /** 查询订单行项目列表 */
    getList() {
      this.loading = true;
      listFeedbackItems(this.queryParams).then(response => {
        this.feedbackitemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    }
  },
  computed: {
    // 基本信息-表格数据
    baseTableData: function () {

      return [
          {
            title1: this.$t('feedback.detail.dnNo'),
            content1: this.feedback.dnNo,
            title2: this.$t('feedback.detail.compCode'),
            content2: this.feedback.compCode
          },
          {
            title1: this.$t('feedback.detail.suppCode'),
            content1: this.feedback.suppCode,
            title2: this.$t('feedback.detail.plantCode'),
            content2: this.feedback.plantCode,
          }
        ];
    }
  }
};
</script>

<style scoped>

</style>
