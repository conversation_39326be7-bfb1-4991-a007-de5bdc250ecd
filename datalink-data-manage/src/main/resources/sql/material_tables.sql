-- ----------------------------
-- Table structure for tbl_material
-- ----------------------------
DROP TABLE IF EXISTS `tbl_material`;
CREATE TABLE `tbl_material` (
  `Material_ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '物料ID',
  `Material_Code` varchar(50) NOT NULL COMMENT '物料编号',
  `Material_Name` varchar(200) DEFAULT NULL COMMENT '物料描述',
  `Base_Unit` varchar(20) DEFAULT NULL COMMENT '基本计量单位',
  `Create_Time` datetime DEFAULT NULL COMMENT '创建时间',
  `Create_By` varchar(64) DEFAULT NULL COMMENT '创建者',
  `Update_Time` datetime DEFAULT NULL COMMENT '更新时间',
  `Update_By` varchar(64) DEFAULT NULL COMMENT '更新者',
  `Del_Flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`Material_ID`),
  UNIQUE KEY `idx_material_code` (`Material_Code`) COMMENT '物料编号唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物料信息表';

-- ----------------------------
-- Table structure for tbl_material_plant
-- ----------------------------
DROP TABLE IF EXISTS `tbl_material_plant`;
CREATE TABLE `tbl_material_plant` (
  `Material_Plant_ID` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '物料工厂ID',
  `Material_ID` bigint(20) NOT NULL COMMENT '物料ID',
  `Plant_Code` varchar(50) NOT NULL COMMENT '工厂代码',
  `Pack_Quantity` bigint(20) DEFAULT NULL COMMENT '包装数量',
  `Create_Time` datetime DEFAULT NULL COMMENT '创建时间',
  `Create_By` varchar(64) DEFAULT NULL COMMENT '创建者',
  `Update_Time` datetime DEFAULT NULL COMMENT '更新时间',
  `Update_By` varchar(64) DEFAULT NULL COMMENT '更新者',
  `Del_Flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`Material_Plant_ID`),
  KEY `idx_material_id` (`Material_ID`) COMMENT '物料ID索引',
  KEY `idx_plant_code` (`Plant_Code`) COMMENT '工厂代码索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='物料工厂信息表'; 