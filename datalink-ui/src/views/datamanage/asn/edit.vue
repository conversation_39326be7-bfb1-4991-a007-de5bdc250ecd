<template>
  <div class="app-container">
    <el-form ref="asn" :model="asn" :rules="rules" label-width="auto">
      <el-card shadow="never" class="border-none margin-t24">
        <div slot="header">
          {{ $t('asn.edit.asnInfo') }}
        </div>

        <el-row :gutter="20">
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.asnCode')" prop="asnCode">
              <el-input v-model="asn.asnCode" :placeholder="$t('asn.edit.enterAsnCode')" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.compCode')" prop="compCode">
              <el-input v-model="asn.compCode" :placeholder="$t('asn.edit.enterCompCode')" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.suppCode')" prop="suppCode">
              <el-input v-model="asn.suppCode" :placeholder="$t('asn.edit.enterSuppCode')" disabled />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.suppName')" prop="suppName">
              <el-input v-model="asn.suppName" :placeholder="$t('asn.edit.enterSuppName')" disabled />
            </el-form-item>
          </el-col>
          <!-- <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.planDeliveryDate')" prop="planDeliveryDate">
              <el-date-picker clearable size="small" v-model="asn.planDeliveryDate" type="date"
                value-format="yyyy-MM-dd" :placeholder="$t('asn.edit.selectPlanDeliveryDate')">
              </el-date-picker>
            </el-form-item>
          </el-col> -->
          <el-col :lg="12" :sm="24">
            <el-form-item :label="$t('asn.edit.deliveryDate')" prop="deliveryDate">
              <el-date-picker clearable size="small" v-model="asn.deliveryDate" type="datetime"
              :placeholder="$t('asn.edit.selectDeliveryDate')" @change="setBatchNo">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <div style="margin-top: 10px;text-align: center">
        <el-button
          type="primary"
          @click="save(false)"
          :loading="loading"
          :disabled="hasInvalidQuantity">{{ $t('asn.edit.save') }}
        </el-button>
        <el-button
          type="primary"
          @click="save(true)"
          :loading="loading"
          :disabled="hasInvalidQuantity">{{ $t('asn.edit.send') }}
        </el-button>
        <el-button @click="cancel">{{ $t('asn.edit.cancel') }}
        </el-button>
      </div>
      <el-card shadow="never" class="border-none margin-t24" style="margin-top: 10px">
        <div slot="header">
          {{ $t('asn.edit.lineItemInfo') }}
        </div>
        <el-table :data="asn.detail" default-expand-all>
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-card shadow="never" class="border-none margin-t24">
                <div slot="header">
                  {{ $t('asn.edit.articleInfo') }}
                </div>
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addArticle(props.row)"
                      v-hasPermi="['datamanage:asn:edit']">{{ $t('asn.edit.addArticle') }}
                    </el-button>
                  </el-col>
                </el-row>
                <el-table :data="props.row.articles">
                  <el-table-column :label="$t('asn.edit.orderLineNo')" align="center" prop="orderLineNo" />
                  <el-table-column :label="$t('asn.edit.articleNo')" align="center" prop="articleNo" />
                  <el-table-column :label="$t('asn.edit.articleName')" align="center" prop="articleName" />
                  <el-table-column :label="$t('asn.edit.quantity')" align="center" prop="quantity">
                    <template slot-scope="scope">
                      <el-form-item :prop="'detail.' + props.$index + '.articles.' + scope.$index + '.quantity'"
                        label-width="0" :rules="rules.quantity"
                        class="mb-0">
                        <el-input-number v-model="scope.row.quantity" :min="0.0001" :max="scope.row.maxQty"
                          :controls="false" style="width: 100%" @blur="calcPackQty"></el-input-number>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('asn.edit.unit')" align="center" prop="unit" />
                  <!-- <el-table-column :label="$t('asn.edit.batchNo')" align="center" prop="batchNo" /> -->
                  <el-table-column :label="$t('asn.edit.qtyPerPack')" align="center" prop="qtyPerPack">
                    <!-- <template slot-scope="scope">
                      <el-form-item :prop="'detail.' + props.$index + '.articles.' + scope.$index + '.qtyPerPack'"
                        label-width="0" :rules="rules.qtyPerPack">
                        <el-input-number v-model="scope.row.qtyPerPack" :min="0.0001" :controls="false"
                          style="width: 100%" @blur="calcPackQty"></el-input-number>
                      </el-form-item>
                    </template> -->
                  </el-table-column>
                  <el-table-column :label="$t('asn.edit.packQty')" align="center" prop="packQty" />
                  <!-- <el-table-column :label="$t('asn.edit.nonStd')" align="center" prop="nonStd">
                    <template slot-scope="scope">
                      <el-form-item :prop="'detail.' + props.$index + '.articles.' + scope.$index + '.nonStd'"
                        label-width="0">
                        <el-input v-model="scope.row.nonStd"></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column> -->
                  <el-table-column :label="$t('asn.edit.actions')" align="center">
                    <template slot-scope="scope">
                      <el-button size="mini" type="danger" icon="el-icon-delete"
                        @click="deleteArticle(props.row, scope.row.orderLineNo)" v-hasPermi="['datamanage:asn:edit']">{{ $t('asn.edit.delete') }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </template>
          </el-table-column>
          <el-table-column :label="$t('asn.edit.dnNo')" align="center" prop="dnNo" />
          <el-table-column :label="$t('asn.edit.orderCode')" align="center" prop="orderCode" />
          <el-table-column :label="$t('asn.edit.plantCode')" align="center" prop="plantCode" />
          <el-table-column :label="$t('asn.edit.plantName')" align="center" prop="plantName" />
          <el-table-column :label="$t('asn.edit.unloadingNo')" align="center">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.unloadingNo'" label-width="0" class="mb-0">
                <el-input v-model="scope.row.unloadingNo"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column :label="$t('asn.edit.unloadingName')" align="center" prop="unloadingName">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.unloadingName'" label-width="0" class="mb-0">
                <el-input v-model="scope.row.unloadingName"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('asn.edit.sendLocNo')" align="center" prop="sendLocNo">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.sendLocNo'" label-width="0">
                <el-input v-model="scope.row.sendLocNo"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <!-- <el-table-column :label="$t('asn.edit.sendLocName')" align="center" prop="sendLocName">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.sendLocName'" label-width="0">
                <el-input v-model="scope.row.sendLocName"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
          <el-table-column :label="$t('asn.edit.rcvLocNo')" align="center" prop="rcvLocNo">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.rcvLocNo'" label-width="0" class="mb-0">
                <el-input v-model="scope.row.rcvLocNo"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column :label="$t('asn.edit.rcvLocName')" align="center" prop="rcvLocName">
            <template slot-scope="scope">
              <el-form-item :prop="'detail.' + scope.$index + '.rcvLocName'" label-width="0">
                <el-input v-model="scope.row.rcvLocName"></el-input>
              </el-form-item>
            </template>
          </el-table-column> -->
        </el-table>
      </el-card>
    </el-form>
    <el-dialog :title="$t('asn.edit.selectOrderItem')" :visible.sync="open" width="650px" append-to-body>
      <el-table :data="orderItems" @selection-change="orderItemSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column min-width="150" :label="$t('asn.edit.itemNo')" align="center" prop="itemNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.articleNo')" align="center" prop="articleNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.articleName')" align="center" prop="articleName" />
        <el-table-column min-width="150" :label="$t('asn.edit.quantity')" align="center" prop="quantity" />
        <el-table-column min-width="150" :label="$t('asn.edit.unit')" align="center" prop="unit" />
        <el-table-column min-width="150" :label="$t('asn.edit.netPrice')" align="center" prop="netPrice" />
        <el-table-column min-width="150" :label="$t('asn.edit.priceUnit')" align="center" prop="priceUnit" />
        <el-table-column min-width="150" :label="$t('asn.edit.currencyCode')" align="center" prop="currencyCode" />
        <el-table-column min-width="150" :label="$t('asn.edit.deliveryDate')" align="center" prop="deliveryDate"
          width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.qtyPerPack')" align="center" prop="qtyPerPack" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.workbinNo')" align="center" prop="workbinNo" />
        <!-- <el-table-column min-width="250" :label="$t('asn.edit.dialog.workbinName')" align="center" prop="workbinName" /> -->
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.state')" align="center" prop="state" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.purDocType')" align="center" prop="purDocType" />
        <!-- <el-table-column min-width="150" :label="$t('asn.edit.dialog.itemType')" align="center" prop="itemType" /> -->
        <el-table-column min-width="280" :label="$t('asn.edit.dialog.text')" align="center" prop="text" />
        <el-table-column min-width="320" :label="$t('asn.edit.dialog.orderNetWorth')" align="center"
          prop="orderNetWorth" />
        <el-table-column min-width="320" :label="$t('asn.edit.dialog.delIden')" align="center" prop="delIden" />
        <el-table-column min-width="280" :label="$t('asn.edit.dialog.shortText')" align="center" prop="shortText" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.oldArticleNo')" align="center"
          prop="oldArticleNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.unloadingNo')" align="center" prop="unloadingNo" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.unloadingName')" align="center"
          prop="unloadingName" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.stockLoc')" align="center" prop="stockLoc" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.locDes')" align="center" prop="locDes" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.locAdd')" align="center" prop="locAdd" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.rcvName')" align="center" prop="rcvName" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.rcvTel')" align="center" prop="rcvTel" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.inspeStrategy')" align="center"
          prop="inspeStrategy" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.zipCode')" align="center" prop="zipCode" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.city')" align="center" prop="city" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.countryCode')" align="center" prop="countryCode" />
        <el-table-column min-width="150" :label="$t('asn.edit.dialog.addTimeZone')" align="center" prop="addTimeZone" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.street2')" align="center" prop="street2" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.street3')" align="center" prop="street3" />
        <el-table-column min-width="250" :label="$t('asn.edit.dialog.street4')" align="center" prop="street4" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doAddAritcle()" :disabled="!canAddArticle">{{ $t('asn.edit.confirm')
          }}
        </el-button>
        <el-button @click="cancelAdd">{{ $t('asn.edit.cancel') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addAsn, listAsnArticles, listAsnItem, listOrderAsnQuantities, updateAsn } from "@/api/datamanage/asn"
import { listOrderItems } from "@/api/datamanage/order"
import dayjs from 'dayjs'
import { Message, MessageBox } from 'element-ui'
import { mapActions, mapState } from 'vuex'

export default {
  name: "AsnEdit",

  data() {
    return {
      asn: {
        asnCode: ' ',
        compCode: '',
        suppCode: '',
        suppName: '',
        deliveryDate: null,
        detail: []
      },
      rules: {
        deliveryDate: [
          { required: true, message: this.$t('asn.edit.validation.deliveryDateRequired'), trigger: "blur" }
        ],
        quantity: [
          { required: true, message: this.$t('asn.edit.validation.quantityRequired'), trigger: "blur" }
        ],
        qtyPerPack: [
          { required: true, message: this.$t('asn.edit.validation.qtyPerPackRequired'), trigger: "blur" }
        ]
      },
      orderInfo: {},
      open: false,
      orderItems: [],
      toAddArticles: [],
      toAddArticleItem: {},
      canAddArticle: false,
      orderAsnQuantityMap: {},
      asnMaxPackQty: 99999,
      hasInvalidQuantity: false,
      loading: false
    }
  },
  computed: {
    ...mapState('asn', ['asnEdit', 'asnCreate']),
    canSave() {
      if (!this.asn.detail || this.asn.detail.length === 0) return false

      for (let item of this.asn.detail) {
        if (!item.articles || item.articles.length === 0) continue
        for (let article of item.articles) {
          if (article.quantity <= 0) {
            return false
          }
        }
      }
      return true
    }
  },
  watch: {
    asnCreate: {
      handler(newVal) {
        if (newVal) {
          this.initializeFromCreate(newVal)
        }
      },
      immediate: true
    },
    asnEdit: {
      handler(newVal) {
        if (newVal) {
          this.initializeFromEdit(newVal)
        }
      },
      immediate: true
    }
  },
  created() {
    this.getConfigKey('asn.maxPackQty').then(response => {
      if (response.msg) {
        this.asnMaxPackQty = parseInt(response.msg)
      }
    })
  },

  methods: {
    calcPackQty() {
      for (let item of this.asn.detail) {
        for (let article of item.articles) {
          if (article.quantity && article.qtyPerPack) {
            article.packQty = Math.ceil(article.quantity / article.qtyPerPack)
          }
        }
      }
    },

    addArticle(item) {
      this.toAddArticleItem = item
      this.orderItems = []
      console.log(JSON.stringify(item))
      console.log(JSON.stringify(this.orderInfo))
      if (this.orderInfo[item.orderCode]) {
        for (let orderItem of this.orderInfo[item.orderCode]) {
          let exist = false
          for (let article of item.articles) {
            if (orderItem.itemNo === article.orderLineNo) {
              exist = true
              break
            }
          }
          if (!exist) {
            this.orderItems.push(orderItem)
          }
        }
        this.open = true
      }
    },

    doAddAritcle() {
      for (let article of this.toAddArticles) {
        this.toAddArticleItem.articles.push(article)
      }
      this.toAddArticles = []
      this.orderItems = []
      this.open = false
      this.toAddArticleItem = {}
      this.canAddArticle = false
    },

    cancelAdd() {
      this.toAddArticles = []
      this.orderItems = []
      this.open = false
      this.toAddArticleItem = {}
      this.canAddArticle = false
    },

    orderItemSelectionChange(selection) {
      this.toAddArticles = []
      let quantityInfoList = this.orderAsnQuantityMap[this.toAddArticleItem.orderCode]
      for (let orderItem of selection) {
        for (let quantityInfo of quantityInfoList) {
          if (quantityInfo.orderLineNo === orderItem.itemNo && quantityInfo.unsentQuantity > 0) {
            this.toAddArticles.push({
              orderLineNo: orderItem.itemNo,
              articleNo: orderItem.articleNo,
              articleName: orderItem.articleName,
              quantity: quantityInfo.unsentQuantity,
              qtyPerPack: orderItem.qtyPerPack ? orderItem.qtyPerPack : 1,
              unit: orderItem.unit,
              packQty: orderItem.qtyPerPack ? Math.ceil(quantityInfo.unsentQuantity / orderItem.qtyPerPack) : Math.ceil(quantityInfo.unsentQuantity),
              startWith: '',
              endWith: '',
              nonStd: 'N',
              maxQty: quantityInfo.unsentQuantity,
            })
            break
          }
        }
      }
      this.canAddArticle = this.toAddArticles.length > 0
    },

    deleteArticle(item, orderLineNo) {
      let index = -1
      for (let i = 0; i < item.articles.length; i++) {
        if (item.articles[i].orderLineNo === orderLineNo) {
          index = i
          break
        }
      }
      if (index >= 0) {
        item.articles.splice(index, 1)
        let quantityInfoList = this.orderAsnQuantityMap[item.orderCode]
        for (let quantityInfo of quantityInfoList) {
          if (quantityInfo.orderLineNo === orderLineNo) {
            quantityInfo.unsentQuantity = quantityInfo.quantity
            break
          }
        }
      }
    },

    closeTab() {
      this.$store.dispatch('tagsView/delView', this.$route)
      this.$router.go(-1)
    },

    cancel() {
      MessageBox.confirm(this.$t('asn.edit.confirmCancel')).then(() => {
        if (this.$refs.asn) {
          this.$refs.asn.clearValidate()
          this.$refs.asn.resetFields()
        }
        this.clearAsnData()
        this.closeTab()
      }).catch(() => { })
    },

    save(needSend) {
      if (this.loading) return

      if (this.asn.detail.length === 0) {
        Message.info(this.$t('asn.edit.atLeastOneLineItem'))
        return
      } else {
        for (let item of this.asn.detail) {
          if (item.articles.length === 0) {
            Message.info(this.$t('asn.edit.atLeastOneArticle'))
            return
          }
        }
      }

      this.calcPackQty()
      let totalPackQty = 0
      for (let item of this.asn.detail) {
        for (let article of item.articles) {
          if (article.packQty) {
            totalPackQty += article.packQty
          }
        }
      }

      if (totalPackQty > this.asnMaxPackQty) {
        Message.info(this.$t('asn.edit.maxPackQtyExceeded', { max: this.asnMaxPackQty }))
        return
      }

      // console.log(this.asn)
      // return;
      this.$refs['asn'].validate(valid => {
        if (valid) {
          this.loading = true
          if (!this.asn.asnId) {
            this.asn.asnCode = null
            this.asn.direction = 'O'
            this.asn.kafkaStatus = needSend ? '1' : '0'
            addAsn(this.asn).then(res => {
              if (needSend) {
                Message.success(this.$t('asn.edit.sendSuccess'))
              } else {
                Message.success(this.$t('asn.edit.createSuccess'))
              }
              this.closeTab()
            }).catch(() => {
              this.loading = false
            }).finally(() => {
              this.loading = false
            })
          } else {
            if (needSend) {
              this.asn.kafkaStatus = '1'
            }
            updateAsn(this.asn).then(res => {
              if (needSend) {
                Message.success(this.$t('asn.edit.sendSuccess'))
              } else {
                Message.success(this.$t('asn.edit.saveSuccess'))
              }
              this.closeTab()
            }).catch(() => {
              this.loading = false
            }).finally(() => {
              this.loading = false
            })
          }
        }
      })
    },


    setBatchNo() {
      if (this.asn.deliveryDate) {
        for (const item of this.asn.detail) {
          for (const article of item.articles) {
            article.batchNo = dayjs(this.asn.deliveryDate).format('YYYYMMDD')
          }
        }
      }
    },
    initializeFromCreate(createData) {
      const params = JSON.parse(JSON.stringify(createData))
      this.asn = {
        asnCode: ' ',
        deliveryDate: null,
        compCode: params.compCode,
        suppCode: params.suppCode,
        suppName: params.suppName,
        detail: []
      }
      const orders = params.orders
      const affectedOrders = []
      this.hasInvalidQuantity = false

      let processedOrders = 0
      const totalOrders = orders.length
      let globalEarliestDeliveryDate = null

      for (const order of orders) {
        listOrderAsnQuantities({ orderCode: order.orderCode, compCode: this.asn.compCode }).then(qres => {
          this.orderAsnQuantityMap[order.orderCode] = qres.data
          listOrderItems({ orderId: order.orderId }).then(res => {
            this.orderInfo[order.orderCode] = res.rows
            const articles = []

            for (const item of res.rows) {
              if (!item.deliveryDate) continue
              const fullDeliveryDate = item.deliveryDate
              // if (fullDeliveryDate.length === 10) {
              //   fullDeliveryDate = fullDeliveryDate + ' 00:00:00'
              // }
              const currentTimestamp = Date.parse(fullDeliveryDate)
              if (isNaN(currentTimestamp)) continue

              if (!globalEarliestDeliveryDate) {
                globalEarliestDeliveryDate = { ...item, deliveryDate: fullDeliveryDate }
              } else {
                const earliestFullDate = globalEarliestDeliveryDate.deliveryDate
                // if (earliestFullDate.length === 10) {
                //   earliestFullDate = earliestFullDate + ' 00:00:00'
                // }
                const earliestTimestamp = Date.parse(earliestFullDate)
                if (currentTimestamp < earliestTimestamp) {
                  globalEarliestDeliveryDate = { ...item, deliveryDate: fullDeliveryDate }
                }
              }
            }

            for (const orderItem of res.rows) {
              for (const orderAsnQuantity of qres.data) {
                if (orderAsnQuantity.orderLineNo === orderItem.itemNo && orderAsnQuantity.unsentQuantity > 0) {
                  articles.push({
                    orderLineNo: orderItem.itemNo,
                    articleNo: orderItem.articleNo,
                    articleName: orderItem.articleName,
                    quantity: orderAsnQuantity.unsentQuantity,
                    qtyPerPack: orderItem.qtyPerPack ? orderItem.qtyPerPack : 1,
                    unit: orderItem.unit,
                    packQty: orderItem.qtyPerPack ? Math.ceil(orderAsnQuantity.unsentQuantity / orderItem.qtyPerPack) : Math.ceil(orderAsnQuantity.unsentQuantity),
                    startWith: '',
                    endWith: '',
                    nonStd: 'N',
                    maxQty: orderAsnQuantity.unsentQuantity,
                  })
                  if (orderAsnQuantity.unsentQuantity <= 0) {
                    this.hasInvalidQuantity = true
                    if (!affectedOrders.includes(order.orderCode)) {
                      affectedOrders.push(order.orderCode)
                    }
                  }
                  break
                }
              }
            }

            this.asn.detail.push({
              orderCode: order.orderCode,
              plantCode: order.plantCode,
              plantName: order.plantName,
              unloadingNo: res.rows[0].unloadingNo,
              unloadingName: res.rows[0].unloadingName,
              sendLocNo: '',
              sendLocName: '',
              rcvLocNo: res.rows[0].stockLoc,
              rcvLocName: '',
              articles: articles
            })

            processedOrders++
            if (processedOrders === totalOrders && globalEarliestDeliveryDate) {
              const deliveryDate = globalEarliestDeliveryDate.deliveryDate

              this.asn.deliveryDate = dayjs(deliveryDate).format();
              // if (deliveryDate && deliveryDate.length === 10) { // 只有日期部分 YYYY-MM-DD
              //   // 创建一个带时区的日期对象
              //   const date = new Date(deliveryDate + 'T00:00:00')
              //   this.asn.deliveryDate = date
              // } else {
              //   this.asn.deliveryDate = new Date(deliveryDate)
              // }
            }

            if (processedOrders === totalOrders && this.hasInvalidQuantity) {
              MessageBox.alert(
                this.$t('asn.edit.validation.quantityUsedUp') + '\n' +
                this.$t('asn.edit.validation.affectedOrders') + ': ' + affectedOrders.join(', '),
                this.$t('asn.edit.warning'),
                {
                  confirmButtonText: this.$t('asn.edit.confirm')
                }
              )
            }
          })
        })
      }
    },
    initializeFromEdit(editData) {
      this.asn = {
        ...JSON.parse(JSON.stringify(editData)),
        detail: editData.detail || []
      }

      const loadAsnDetails = () => {
        listAsnItem({ asnId: this.asn.asnId }).then(res => {
          for (let item of res.rows) {
            item.articles = []
            listOrderAsnQuantities({ orderCode: item.orderCode, compCode: this.asn.compCode }).then(qres => {
              this.orderAsnQuantityMap[item.orderCode] = qres.data
              listAsnArticles({ itemId: item.itemId }).then(resp => {
                item.articles = resp.rows
                for (let article of item.articles) {
                  for (let orderAsnQuantity of qres.data) {
                    if (orderAsnQuantity.orderLineNo === article.orderLineNo) {
                      orderAsnQuantity.unsentQuantity += article.quantity
                      article.maxQty = orderAsnQuantity.unsentQuantity
                      break
                    }
                  }
                }
                this.asn.detail.push(item)
              })
            })
          }
        })
      }

      if (this.asn.asnId) {
        loadAsnDetails()
      }
    },
    beforeDestroy() {
      this.asn = {
        detail: []
      }
      this.clearAsnData()
    },
    ...mapActions('asn', ['clearAsnData'])
  }
}
</script>

<style scoped>
.mb-0 {
  margin-bottom: 0 !important
}
</style>
