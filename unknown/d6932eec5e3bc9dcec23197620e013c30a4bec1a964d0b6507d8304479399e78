package com.datalink.datamanage.controller;

import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblForecastItem;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.service.ITblForecastService;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.common.core.page.TableDataInfo;

import javax.validation.Valid;

/**
 * 预测Controller
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
@RestController
@RequestMapping("/datamanage/forecast")
public class TblForecastController extends BaseController
{
    @Autowired
    private ITblForecastService tblForecastService;

    /**
     * 查询预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(TblForecast tblForecast)
    {
        startPage();
        List<TblForecast> list = tblForecastService.selectTblForecastList(tblForecast);
        return getDataTable(list);
    }

    /**
     * 查询预测行项目列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:list')")
    @GetMapping("/listItems")
    public TableDataInfo listItems(TblForecastItem tblForecastItem)
    {
        startPage();
        List<TblForecastItem> list = tblForecastService.selectTblForecastItemList(tblForecastItem);
        return getDataTable(list);
    }

    /**
     * 导出预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:export')")
    @DataScope(supplierAlias = "a")
    @Log(title = "预测", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblForecast tblForecast)
    {
        List<TblForecast> list = tblForecastService.selectTblForecastList(tblForecast);
        ExcelUtil<TblForecast> util = new ExcelUtil<TblForecast>(TblForecast.class);
        return util.exportExcel(list, "预测数据");
    }

    /**
     * 获取预测详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:query')")
    @GetMapping(value = "/{forecastId}")
    public AjaxResult getInfo(@PathVariable("forecastId") Long forecastId)
    {
        return AjaxResult.success(tblForecastService.selectTblForecastById(forecastId));
    }

    /**
     * 新增预测
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:add')")
    @Log(title = "预测", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblForecast tblForecast)
    {
        return toAjax(tblForecastService.insertTblForecast(tblForecast));
    }

    /**
     * 修改预测
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:edit')")
    @Log(title = "预测", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblForecast tblForecast)
    {
        return toAjax(tblForecastService.updateTblForecast(tblForecast));
    }

    /**
     * 删除预测
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:remove')")
    @Log(title = "预测", businessType = BusinessType.DELETE)
	@DeleteMapping("/{forecastIds}")
    public AjaxResult remove(@PathVariable Long[] forecastIds)
    {
        return toAjax(tblForecastService.deleteTblForecastByIds(forecastIds));
    }

    /**
     * 获取预测详细信息(不抱含行项目)
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:query')")
    @GetMapping(value = "/head/{forecastId}")
    public AjaxResult getForecastOnly(@PathVariable("forecastId") Long forecastId)
    {
        return AjaxResult.success(tblForecastService.selectTblForecastOnlyById(forecastId));
    }

    /**
     * 查询每个预测编号的最新版本预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/latestVersionList")
    public TableDataInfo latestVersionList(TblForecast tblForecast)
    {
        startPage();
        List<TblForecast> list = tblForecastService.selectTblForecastLatestVersionList(tblForecast);
        return getDataTable(list);
    }

    /**
     * 导出每个预测编号的最新版本预测列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:export')")
    @DataScope(supplierAlias = "a")
    @Log(title = "最新版本预测", businessType = BusinessType.EXPORT)
    @GetMapping("/exportLatestVersion")
    public AjaxResult exportLatestVersion(TblForecast tblForecast)
    {
        List<TblForecast> list = tblForecastService.selectTblForecastLatestVersionList(tblForecast);
        ExcelUtil<TblForecast> util = new ExcelUtil<TblForecast>(TblForecast.class);
        return util.exportExcel(list, "最新版本预测数据");
    }

    /**
     * 预测确认
     */
    @PreAuthorize("@ss.hasPermi('datamanage:forecast:confirm')")
    @Log(title = "预测确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm")
    public AjaxResult confirm(@RequestBody List<Long> forecastIds)
    {
        return toAjax(tblForecastService.confirmForecasts(forecastIds));
    }

}
