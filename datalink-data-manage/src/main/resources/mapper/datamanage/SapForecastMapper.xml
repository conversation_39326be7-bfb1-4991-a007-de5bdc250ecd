<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.SapForecastMapper">

    <resultMap id="WeekForecastMap" type="SapWeekForecast">
        <id property="id" column="id"/>
        <result property="flief" column="Supp_code"/>
        <result property="zdepot" column="zdepot"/>
        <result property="name1" column="name1"/>
        <result property="isRead" column="is_read"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="subItems" ofType="SapWeekForecastItem">
            <id property="id" column="item_id"/>
            <result property="forecastId" column="forecast_id"/>
            <result property="matnr" column="matnr"/>
            <result property="werks" column="werks"/>
            <result property="zmethd" column="zmethd"/>
            <result property="beskz" column="beskz"/>
            <result property="ordCls" column="ord_cls"/>
            <result property="wanst" column="wanst"/>
            <result property="dispo" column="dispo"/>
            <result property="bstrf" column="bstrf"/>
            <result property="meins" column="meins"/>
            <result property="maabc" column="maabc"/>
            <result property="wmNo" column="wm_no"/>
            <result property="datsLm" column="dats_lm"/>
            <result property="datsM" column="dats_m"/>
            <result property="datsNm" column="dats_nm"/>
            <result property="datsNnm" column="dats_nnm"/>
            <result property="poutMk" column="pout_mk"/>
            <result property="poutYm" column="pout_ym"/>
            <collection property="details" ofType="SapWeekForecastItemDetail">
                <id property="id" column="detail_id"/>
                <result property="itemId" column="item_id"/>
                <result property="forecastDate" column="forecast_date"/>
                <result property="forecastQty" column="forecast_qty"/>
            </collection>
        </collection>
    </resultMap>

    <insert id="insertSapWeekForecast" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_week_forecast(
        Supp_code, zdepot, name1, create_by, create_time, update_by, update_time
        ) values (
            #{flief}, #{zdepot}, #{name1}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}
        )
    </insert>

    <insert id="batchInsertWeekForecastItemDetails">
        insert into tbl_sap_week_forecast_item_detail(
            item_id, forecast_date, forecast_qty,
            create_by, create_time, update_by, update_time
        ) values
        <foreach collection="details" item="detail" separator=",">
            (#{detail.itemId}, #{detail.forecastDate}, #{detail.forecastQty},
            #{detail.createBy}, #{detail.createTime}, #{detail.updateBy}, #{detail.updateTime})
        </foreach>
    </insert>

    <select id="selectLatestWeekForecast" parameterType="SapWeekForecast" resultMap="WeekForecastMap">
        SELECT 
            f.*, i.*, d.*,
            i.id as item_id,
            d.id as detail_id
        FROM tbl_sap_week_forecast f
        LEFT JOIN tbl_sap_week_forecast_item i ON f.id = i.forecast_id
        LEFT JOIN tbl_sap_week_forecast_item_detail d ON i.id = d.item_id
        WHERE f.id = (
            SELECT id FROM tbl_sap_week_forecast a
            where 1 = 1
            <if test="flief != null and flief != ''"> and a.supp_code = #{flief}</if>
            <if test="zdepot != null and zdepot != ''"> and a.zdepot = #{zdepot}</if>
            <if test="zdepot == null or zdepot == ''"> and a.zdepot is null</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
            ORDER BY create_time DESC LIMIT 1
        )
        ORDER BY d.id
    </select>

    <select id="selectWeekForecastById" resultMap="WeekForecastMap">
        SELECT 
            f.*, i.*, d.*,
            i.id as item_id,
            d.id as detail_id
        FROM tbl_sap_week_forecast f
        LEFT JOIN tbl_sap_week_forecast_item i ON f.id = i.forecast_id
        LEFT JOIN tbl_sap_week_forecast_item_detail d ON i.id = d.item_id
        WHERE f.id = #{id}
    </select>

    <select id="selectWeekForecastItemsByForecastId" resultType="SapWeekForecastItem">
        SELECT * FROM tbl_sap_week_forecast_item
        WHERE forecast_id = #{forecastId}
    </select>

    <select id="selectWeekForecastItemDetailsByItemId" resultType="SapWeekForecastItemDetail">
        SELECT * FROM tbl_sap_week_forecast_item_detail
        WHERE item_id = #{itemId}
    </select>

    <insert id="insertSapWeekForecastItem" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_week_forecast_item(
            forecast_id, matnr, werks, zmethd, beskz, ord_cls, wanst, dispo, bstrf, meins, maabc,
            wm_no, dats_lm, dats_m, dats_nm, dats_nnm, pout_mk, pout_ym,
            create_by, create_time, update_by, update_time
        ) values (
            #{forecastId}, #{matnr}, #{werks}, #{zmethd}, #{beskz}, #{ordCls}, #{wanst}, #{dispo},
            #{bstrf}, #{meins}, #{maabc}, #{wmNo}, #{datsLm}, #{datsM}, 
            #{datsNm}, #{datsNnm}, #{poutMk}, #{poutYm}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}
        )
    </insert>

    <!-- 3个月预测结果映射 -->
    <resultMap id="MonthForecastMap" type="SapMonthForecast">
        <id property="id" column="id"/>
        <result property="flief" column="supp_code"/>
        <result property="zdepot" column="depot"/>
        <result property="name1" column="name1"/>
        <result property="isRead" column="is_read"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="subItems" ofType="SapMonthForecastItem">
            <id property="id" column="item_id"/>
            <result property="forecastId" column="forecast_id"/>
            <result property="matnr" column="matnr"/>
            <result property="plwrk" column="plwrk"/>
            <result property="zmethd" column="zmethd"/>
            <result property="wanst" column="wanst"/>
            <result property="dispo" column="dispo"/>
            <result property="beskz" column="beskz"/>
            <result property="bstrf" column="bstrf"/>
            <result property="zmonth" column="zmonth"/>
            <result property="gsmng1" column="gsmng1"/>
            <result property="gsmng2" column="gsmng2"/>
            <result property="gsmng3" column="gsmng3"/>
            <result property="gsmngT" column="gsmngt"/>
            <result property="meins" column="meins"/>
        </collection>
    </resultMap>

    <insert id="insertSapMonthForecast" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_month_forecast (
            supp_code, depot, name1, create_by, create_time, update_by, update_time
        ) values (
            #{flief}, #{zdepot}, #{name1}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}
        )
    </insert>

    <insert id="insertSapMonthForecastItem" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_month_forecast_item(
            forecast_id, matnr, plwrk, zmethd, wanst, dispo, beskz, bstrf, meins, zmonth,
            gsmng1, gsmng2, gsmng3, gsmngt, seiku, jyoken, kansan,
            ryosan_dankiri, ryosan_dankiri_m, ryosan_dankiri_ym,
            create_by, create_time, update_by, update_time
        ) values (
            #{forecastId}, #{matnr}, #{plwrk}, #{zmethd}, #{wanst}, #{dispo}, #{beskz},
            #{bstrf}, #{meins}, #{zmonth}, #{gsmng1}, #{gsmng2}, #{gsmng3}, #{gsmngT},
            #{seiku}, #{jyoken}, #{kansan}, #{ryosanDankiri}, #{ryosanDankiriM},
            #{ryosanDankiriYm}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}
        )
    </insert>

    <!-- 查询最新的3个月预测数据 -->
    <select id="selectLatestMonthForecast" parameterType="SapMonthForecast" resultMap="MonthForecastMap">
        SELECT 
            f.*, i.*,
            i.id as item_id
        FROM tbl_sap_month_forecast f
        LEFT JOIN tbl_sap_month_forecast_item i ON f.id = i.forecast_id
        WHERE f.id = (
            SELECT id FROM tbl_sap_month_forecast a
            where 1 = 1
            <if test="flief != null and flief != ''"> and a.supp_code = #{flief}</if>
            <if test="zdepot != null and zdepot != ''"> and a.depot = #{zdepot}</if>
            <if test="zdepot == null or zdepot == ''"> and a.depot is null</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
            ORDER BY create_time DESC LIMIT 1
        )
    </select>

    <!-- 根据ID查询3个月预测数据 -->
    <select id="selectMonthForecastById" resultMap="MonthForecastMap">
        SELECT 
            f.*, i.*,
            i.id as item_id
        FROM tbl_sap_month_forecast f
        LEFT JOIN tbl_sap_month_forecast_item i ON f.id = i.forecast_id
        WHERE f.id = #{id}
    </select>

    <!-- 根据预测ID获取子项列表 -->
    <select id="selectMonthForecastItemsByForecastId" resultType="SapMonthForecastItem">
        SELECT * FROM tbl_sap_month_forecast_item
        WHERE forecast_id = #{forecastId}
    </select>

    <!-- 插入年预测主表数据 -->
    <insert id="insertYearForecast" parameterType="com.datalink.api.domain.SapYearForecast" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_year_forecast (
            supp_code, depot, name1, create_time
        ) values (
            #{flief}, #{zdepot}, #{name1}, #{createTime}
        )
    </insert>

    <!-- 插入年预测明细数据 -->
    <insert id="insertYearForecastItem" parameterType="com.datalink.api.domain.SapYearForecastItem" useGeneratedKeys="true" keyProperty="id">
        insert into tbl_sap_year_forecast_item (
            forecast_id, matnr, plwrk, wanst, dispo, beskz, bstrf, zmonth,
            gsmng1, gsmng2, gsmng3, gsmng4, gsmng5, gsmng6,
            gsmng7, gsmng8, gsmng9, gsmng10, gsmng11, gsmng12,
            first_half_total, second_half_total, year_total,
            meins, create_time
        ) values (
            #{forecastId}, #{matnr}, #{plwrk}, #{wanst}, #{dispo}, #{beskz}, #{bstrf}, #{zmonth},
            #{gsmng1}, #{gsmng2}, #{gsmng3}, #{gsmng4}, #{gsmng5}, #{gsmng6},
            #{gsmng7}, #{gsmng8}, #{gsmng9}, #{gsmng10}, #{gsmng11}, #{gsmng12},
            #{firstHalfTotal}, #{secondHalfTotal}, #{yearTotal},
            #{meins}, #{createTime}
        )
    </insert>

    <!-- 年预测结果映射 -->
    <resultMap id="YearForecastMap" type="SapYearForecast">
        <id property="id" column="id"/>
        <result property="flief" column="supp_code"/>
        <result property="zdepot" column="depot"/>
        <result property="name1" column="name1"/>
        <result property="isRead" column="is_read"/>
        <result property="createTime" column="create_time"/>
        <collection property="subItems" ofType="SapYearForecastItem">
            <id property="id" column="item_id"/>
            <result property="forecastId" column="forecast_id"/>
            <result property="matnr" column="matnr"/>
            <result property="plwrk" column="plwrk"/>
            <result property="wanst" column="wanst"/>
            <result property="dispo" column="dispo"/>
            <result property="beskz" column="beskz"/>
            <result property="bstrf" column="bstrf"/>
            <result property="zmonth" column="zmonth"/>
            <result property="gsmng1" column="gsmng1"/>
            <result property="gsmng2" column="gsmng2"/>
            <result property="gsmng3" column="gsmng3"/>
            <result property="gsmng4" column="gsmng4"/>
            <result property="gsmng5" column="gsmng5"/>
            <result property="gsmng6" column="gsmng6"/>
            <result property="gsmng7" column="gsmng7"/>
            <result property="gsmng8" column="gsmng8"/>
            <result property="gsmng9" column="gsmng9"/>
            <result property="gsmng10" column="gsmng10"/>
            <result property="gsmng11" column="gsmng11"/>
            <result property="gsmng12" column="gsmng12"/>
            <result property="firstHalfTotal" column="first_half_total"/>
            <result property="secondHalfTotal" column="second_half_total"/>
            <result property="yearTotal" column="year_total"/>
            <result property="meins" column="meins"/>
            <result property="createTime" column="create_time"/>
        </collection>
    </resultMap>

    <!-- 查询最新年预测数据 -->
    <select id="selectLatestYearForecast" resultMap="YearForecastMap">
        SELECT 
            f.*, i.*,
            i.id as item_id
        FROM tbl_sap_year_forecast f
        LEFT JOIN tbl_sap_year_forecast_item i ON f.id = i.forecast_id
        WHERE f.id = (
            SELECT id FROM tbl_sap_year_forecast a
            where 1 = 1
            <if test="flief != null and flief != ''"> and a.supp_code = #{flief}</if>
            <if test="zdepot != null and zdepot != ''"> and a.depot = #{zdepot}</if>
            <if test="zdepot == null or zdepot == ''"> and a.depot is null</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
            ORDER BY create_time DESC LIMIT 1
        )
    </select>

    <!-- 根据预测ID查询明细数据 -->
    <select id="selectYearForecastItems" resultType="com.datalink.api.domain.SapYearForecastItem">
        select *
        from tbl_sap_year_forecast_item
        where forecast_id = #{forecastId}
    </select>

    <!-- 更新周预测已读状态 -->
    <update id="updateWeekForecastReadStatus">
        update tbl_sap_week_forecast
        set is_read = #{isRead}
        where id = #{id}
    </update>

    <!-- 更新月预测已读状态 -->
    <update id="updateMonthForecastReadStatus">
        update tbl_sap_month_forecast
        set is_read = #{isRead}
        where id = #{id}
    </update>

    <!-- 更新年预测已读状态 -->
    <update id="updateYearForecastReadStatus">
        update tbl_sap_year_forecast
        set is_read = #{isRead}
        where id = #{id}
    </update>

</mapper>