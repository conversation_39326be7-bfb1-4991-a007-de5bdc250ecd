package com.datalink.api.common;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;

public class SapApiResult  extends HashMap<String, Object> {
    /** 状态码 */
    @JsonProperty("CODE")
    public static final String CODE_TAG = "CODE";

    /** 返回内容 */
    @JsonProperty("MESSAGE")
    public static final String MSG_TAG = "MESSAGE";

    /**
     * 初始化一个新创建的 AjaxResult 对象，使其表示一个空消息。
     */
    public SapApiResult()
    {
    }

    /**
     * 初始化一个新创建的 AjaxResult 对象
     *
     * @param code 状态码
     * @param msg 返回内容
     */
    public SapApiResult(int code, String msg)
    {
        super.put(CODE_TAG, code);
        super.put(MSG_TAG, msg);
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static SapApiResult success()
    {
        return new SapApiResult(ApiConstants.SAP_SUCCESS_CODE, "Success");
    }

    public static SapApiResult error(String message) {
        return new SapApiResult(1, message);
    }
}
