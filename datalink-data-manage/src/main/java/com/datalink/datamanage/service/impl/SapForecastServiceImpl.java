package com.datalink.datamanage.service.impl;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.utils.*;
import com.datalink.api.domain.SapWeekForecast;
import com.datalink.api.domain.SapWeekForecastItem;
import com.datalink.api.domain.SapWeekForecastItemDetail;
import com.datalink.api.domain.SapMonthForecast;
import com.datalink.api.domain.SapMonthForecastItem;
import com.datalink.api.domain.SapYearForecast;
import com.datalink.api.domain.SapYearForecastItem;
import com.datalink.datamanage.mapper.SapForecastMapper;
import com.datalink.datamanage.service.ISapForecastService;
import com.datalink.framework.web.service.PermissionService;
import com.datalink.system.service.ISysDictDataService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;

@Service
@Slf4j
public class SapForecastServiceImpl implements ISapForecastService {
    
    @Autowired
    private SapForecastMapper sapForecastMapper;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private PermissionService permissionService;

    @Value("${file.jasper-template-dir}")
    private String templatePath;

    @Override
    @Transactional
    public void saveWeekForecast(SapWeekForecast forecast) {
        forecast.setCreateTime(DateUtils.getNowDate());
        sapForecastMapper.insertSapWeekForecast(forecast);
        
        List<SapWeekForecastItem> itemList = forecast.getSubItems();
        if (StringUtils.isNotNull(itemList)) {
            for (SapWeekForecastItem item : itemList) {
                item.setForecastId(forecast.getId());
                item.setCreateTime(DateUtils.getNowDate());
                sapForecastMapper.insertSapWeekForecastItem(item);
                
                if (item.getDetails() != null && !item.getDetails().isEmpty()) {
                    for (SapWeekForecastItemDetail detail : item.getDetails()) {
                        detail.setItemId(item.getId());
                        detail.setCreateTime(DateUtils.getNowDate());
                    }
                    sapForecastMapper.batchInsertWeekForecastItemDetails(item.getDetails());
                }
            }
        }
    }
    
    @Override
    @DataScope(supplierAlias = "a")
    public SapWeekForecast getLatestWeekForecast(SapWeekForecast forecast) {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的备注（即配置的可以查看的depot）
            String depot = loginUser.getUser().getRemark();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            forecast.setZdepot(depot);
        }
        return sapForecastMapper.selectLatestWeekForecast(forecast);
    }
    
    @Override
    public SapWeekForecast selectWeekForecastById(Long id) {
        SapWeekForecast forecast = sapForecastMapper.selectWeekForecastById(id);
        if (forecast != null) {
            List<SapWeekForecastItem> items = sapForecastMapper.selectWeekForecastItemsByForecastId(id);
            for (SapWeekForecastItem item : items) {
                List<SapWeekForecastItemDetail> details = 
                    sapForecastMapper.selectWeekForecastItemDetailsByItemId(item.getId());
                item.setDetails(details);
            }
            forecast.setSubItems(items);
        }
        return forecast;
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadWeekForecast(SapWeekForecast sapWeekForecast, String tz) {
        String jasperPath = templatePath + "week_forecast.jrxml";
        String path;
        try {
            SapWeekForecast latestWeekForecast = this.getLatestWeekForecast(sapWeekForecast);
            if (latestWeekForecast == null) {
                return AjaxResult.error(MessageUtils.message("forecast.week.no.data"));
            }
            Map parameters;
            ObjectMapper objectMapper = new ObjectMapper();
            parameters = objectMapper.convertValue(latestWeekForecast, Map.class);
            parameters.put("createTime", DateUtils.parseDateToStrWithTz(DateUtils.YYYY_MM_DD_HH_MM_SS, latestWeekForecast.getCreateTime(), tz));
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), parameters, latestWeekForecast.getSubItems());
            // 更新已读状态
            this.updateWeekForecastReadStatus(latestWeekForecast.getId(), "Y");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    @Transactional
    public void saveMonthForecast(SapMonthForecast forecast) {
        forecast.setCreateTime(DateUtils.getNowDate());
        sapForecastMapper.insertSapMonthForecast(forecast);
        
        if (forecast.getSubItems() != null && !forecast.getSubItems().isEmpty()) {
            for (SapMonthForecastItem item : forecast.getSubItems()) {
                item.setForecastId(forecast.getId());
                item.setCreateTime(DateUtils.getNowDate());
                sapForecastMapper.insertSapMonthForecastItem(item);
            }
        }
    }

    @Override
    @DataScope(supplierAlias = "a")
    public SapMonthForecast getLatestMonthForecast(SapMonthForecast sapMonthForecast) {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的备注（即配置的可以查看的depot）
            String depot = loginUser.getUser().getRemark();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            sapMonthForecast.setZdepot(depot);
        }
        return sapForecastMapper.selectLatestMonthForecast(sapMonthForecast);
    }

    @Override
    public SapMonthForecast selectMonthForecastById(Long id) {
        SapMonthForecast forecast = sapForecastMapper.selectMonthForecastById(id);
        if (forecast != null) {
            List<SapMonthForecastItem> items = 
                sapForecastMapper.selectMonthForecastItemsByForecastId(id);
            forecast.setSubItems(items);
        }
        return forecast;
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadMonthForecast(SapMonthForecast sapMonthForecast, String tz) {
        String jasperPath = templatePath + "month_forecast.jrxml";
        String path;
        try {
            SapMonthForecast latestMonthForecast = this.getLatestMonthForecast(sapMonthForecast);
            if (latestMonthForecast == null) {
                return AjaxResult.error(MessageUtils.message("forecast.month.no.data"));
            }
            Map parameters;
            ObjectMapper objectMapper = new ObjectMapper();
            if (CollectionUtils.isNotEmpty(latestMonthForecast.getSubItems())) {
                latestMonthForecast.setZmonth1(latestMonthForecast.getSubItems().get(0).getZmonth());
                latestMonthForecast.setZmonth2(this.addOneMonth(latestMonthForecast.getSubItems().get(0).getZmonth(), 1));
                latestMonthForecast.setZmonth3(this.addOneMonth(latestMonthForecast.getSubItems().get(0).getZmonth(), 2));
            }
            parameters = objectMapper.convertValue(latestMonthForecast, Map.class);
            parameters.put("createTime", DateUtils.parseDateToStrWithTz(DateUtils.YYYY_MM_DD_HH_MM_SS, latestMonthForecast.getCreateTime(), tz));
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), parameters, latestMonthForecast.getSubItems());
            // 更新已读状态
            this.updateMonthForecastReadStatus(latestMonthForecast.getId(), "Y");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveYearForecast(SapYearForecast forecast) {
        try {
            log.info("开始保存年预测数据");
            
            // 保存主表数据
            forecast.setCreateTime(DateUtils.getNowDate());
            sapForecastMapper.insertYearForecast(forecast);
            
            // 保存明细数据
            if (CollectionUtils.isNotEmpty(forecast.getSubItems())) {
                for (SapYearForecastItem item : forecast.getSubItems()) {
                    item.setForecastId(forecast.getId());
                    item.setCreateTime(DateUtils.getNowDate());
                    sapForecastMapper.insertYearForecastItem(item);
                }
            }
            
            log.info("保存年预测数据完成");
        } catch (Exception e) {
            log.error("保存年预测数据失败", e);
            throw new RuntimeException("保存年预测数据失败", e);
        }
    }

    @Override
    @DataScope(supplierAlias = "a")
    public SapYearForecast getLatestYearForecast(SapYearForecast forecast) {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的备注（即配置的可以查看的depot）
            String depot = loginUser.getUser().getRemark();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            forecast.setZdepot(depot);
        }
        return sapForecastMapper.selectLatestYearForecast(forecast);
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadYearForecast(SapYearForecast sapYearForecast, String tz) {
        String jasperPath = templatePath + "year_forecast.jrxml";
        String path;
        try {
            SapYearForecast latestYearForecast = this.getLatestYearForecast(sapYearForecast);
            if (latestYearForecast == null) {
                return AjaxResult.error(MessageUtils.message("forecast.year.no.data"));
            }
            Map parameters;
            ObjectMapper objectMapper = new ObjectMapper();
            if (CollectionUtils.isNotEmpty(latestYearForecast.getSubItems())) {
                latestYearForecast.setZmonth1(latestYearForecast.getSubItems().get(0).getZmonth());
                latestYearForecast.setZmonth2(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 1));
                latestYearForecast.setZmonth3(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 2));
                latestYearForecast.setZmonth4(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 3));
                latestYearForecast.setZmonth5(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 4));
                latestYearForecast.setZmonth6(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 5));
                latestYearForecast.setZmonth7(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 6));
                latestYearForecast.setZmonth8(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 7));
                latestYearForecast.setZmonth9(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 8));
                latestYearForecast.setZmonth10(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 9));
                latestYearForecast.setZmonth11(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 10));
                latestYearForecast.setZmonth12(this.addOneMonth(latestYearForecast.getSubItems().get(0).getZmonth(), 11));
            }
            parameters = objectMapper.convertValue(latestYearForecast, Map.class);
            parameters.put("createTime", DateUtils.parseDateToStrWithTz(DateUtils.YYYY_MM_DD_HH_MM_SS, latestYearForecast.getCreateTime(), tz));
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), parameters, latestYearForecast.getSubItems());
            // 更新已读状态
            this.updateYearForecastReadStatus(latestYearForecast.getId(), "Y");
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

        return AjaxResult.success(path);
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadMonthForecastTxt(SapMonthForecast monthForecast, String tz) {
        try {
            // 获取最新的三月预测数据
            SapMonthForecast latestMonthForecast = this.getLatestMonthForecast(monthForecast);
            if (latestMonthForecast == null) {
                return AjaxResult.error(MessageUtils.message("forecast.month.no.data"));
            }

//            String fileName = String.format("MonthForecast_%s_%s_%s.txt",
//                latestMonthForecast.getFlief(),
//                latestMonthForecast.getZdepot(),
//                DateUtils.parseDateToStr("yyyyMMdd", latestMonthForecast.getCreateTime()));
            String fileName = String.format("HM01_%s.txt", UUID.randomUUID());
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), "Shift-JIS"))) {
                
                if (CollectionUtils.isNotEmpty(latestMonthForecast.getSubItems())) {
                    for (SapMonthForecastItem item : latestMonthForecast.getSubItems()) {
                        // 创建一个170字符长的字符数组，初始化为空格
                        char[] record = new char[171];
                        Arrays.fill(record, ' ');
                        
                        // 按照规范中的具体位置填充数据
                        TextFileUtil.fillField(record, "01", 1, 2, "F/#");                          // F/# (1-2)
                        TextFileUtil.fillField(record, "", 3, 6, "発注会社");                    // 発注会社 (3-6)
                        TextFileUtil.fillField(record, latestMonthForecast.getFlief(), 7, 11, "取引先");           // 取引先 (7-11)
                        TextFileUtil.fillField(record, latestMonthForecast.getZdepot(), 12, 13, "デポ");                         // デポ (12-13)
                        TextFileUtil.fillField(record, item.getPlwrk(), 14, 17, "要求工場");        // 要求工場 (14-17)
                        String productClass = SapObjectConverter.handleProductClass(dictDataService, item.getMatnr());
                        if (StringUtils.isNotEmpty(productClass)) {
                            item.setMatnr(item.getMatnr().substring(0, item.getMatnr().indexOf("+")));
                        }
                        TextFileUtil.fillField(record, item.getMatnr(), 18, 42, "部品番号");        // 部品番号 (18-42)
                        TextFileUtil.fillField(record, productClass, 43, 43, "製品区分");                     // 製品区分 (43)
                        TextFileUtil.fillField(record, "", 44, 68, "部品名称");                     // 部品名称 (44-68)
                        TextFileUtil.fillField(record, item.getZmethd(), 69, 70, "納入方式");       // 納入方式 (69-70)
                        TextFileUtil.fillField(record, item.getWanst(), 71, 77, "納入場所");         // 納入場所 (71-77)
                        TextFileUtil.fillField(record, item.getDispo(), 78, 80, "計画担当者");       // 計画担当者 (78-80)
                        TextFileUtil.fillField(record, "", 81, 81, "発注区分");                     // 発注区分 (81)
                        TextFileUtil.fillField(record, item.getMeins(), 82, 84, "発注単位");        // 発注単位 (82-84)
                        TextFileUtil.fillField(record, item.getJyoken(), 85, 85, "単位条件");       // 単位条件 (85)
                        TextFileUtil.fillNumber(record, null, 86, 94, 5);              // 換算係数 (86-94,5)
                        TextFileUtil.fillField(record, "", 95, 95, "重要保安区分");             // 重要保安区分 (95)
                        TextFileUtil.fillField(record, item.getRyosanDankiriM(), 96, 97, "量産打切りマーク"); // 量産打切りマーク (96-97)
                        TextFileUtil.fillField(record, item.getRyosanDankiriYm(), 98, 103, "量産打切り年月"); // 量産打切り年月 (98-103)
                        TextFileUtil.fillNumber(record, item.getBstrf(), 104, 110, 0);  // ＳＮＥＰ (104-110)
                        TextFileUtil.fillField(record, "", 111, 118, "荷姿コード");                   // 荷姿コード (111-118)
                        TextFileUtil.fillField(record, item.getZmonth(), 119, 124, "Ｎ月＿年月");     // Ｎ月＿年月 (119-124)
                        TextFileUtil.fillNumber(record, item.getGsmng1(), 125, 132, 0); // Ｎ月 (125-132)
                        TextFileUtil.fillNumber(record, item.getGsmng2(), 133, 140, 0); // Ｎ＋１月 (133-140)
                        TextFileUtil.fillNumber(record, item.getGsmng3(), 141, 148, 0); // Ｎ＋２月 (141-148)
                        TextFileUtil.fillField(record, item.getBeskz(), 149, 149, "自己調達区分");     // 自己調達区分 (149)
                        TextFileUtil.fillField(record, "", 150, 154, "受給先コード");                 // 受給先コード (150-154)
                        TextFileUtil.fillField(record, "", 155, 156, "受給先要元");                 // 受給先要元 (155-156)
                        TextFileUtil.fillField(record, DateUtils.parseDateToStrWithTz("yyyyMMdd",
                            latestMonthForecast.getCreateTime(), tz), 157, 164, "処理年月日"); // 処理年月日 (157-164)
                        TextFileUtil.fillField(record, "", 165, 169, "配信先_取引先");             // 配信先_取引先 (165-169)
                        TextFileUtil.fillField(record, "", 170, 171, "配信先_デポ");               // 配信先_デポ (170-171)
                        
                        writer.write(record);
                        writer.write("\r\n");
                    }
                }
            }
            // 更新已读状态
            this.updateMonthForecastReadStatus(latestMonthForecast.getId(), "Y");
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("生成月度预测txt文件失败", e);
            return AjaxResult.error("生成月度预测txt文件失败：" + e.getMessage());
        }
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadYearForecastTxt(SapYearForecast yearForecast, String tz) {
        try {
            // 获取最新的年度预测数据
            SapYearForecast latestYearForecast = this.getLatestYearForecast(yearForecast);
            if (latestYearForecast == null) {
                return AjaxResult.error(MessageUtils.message("forecast.year.no.data"));
            }

            String fileName = String.format("HM06_%s.txt", UUID.randomUUID());
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), "Shift-JIS"))) {
                
                if (CollectionUtils.isNotEmpty(latestYearForecast.getSubItems())) {
                    for (SapYearForecastItem item : latestYearForecast.getSubItems()) {
                        // 创建一个256字符长的字符数组，初始化为空格
                        char[] record = new char[257];
                        Arrays.fill(record, ' ');
                        
                        // 按照规范中的具体位置填充数据
                        TextFileUtil.fillField(record, "00", 1, 2, "F/#");                          // F/# (1-2)
                        TextFileUtil.fillField(record, "", 3, 6, "会社コード");                  // 会社コード (3-6)
                        TextFileUtil.fillField(record, latestYearForecast.getFlief(), 7, 11, "取引先コード");       // 取引先コード (7-11)
                        TextFileUtil.fillField(record, latestYearForecast.getZdepot(), 12, 13, "デポ");                         // デポ (12-13)
                        TextFileUtil.fillField(record, item.getPlwrk(), 14, 17, "要求工場コード");    // 要求工場コード (14-17)
                        String productClass = SapObjectConverter.handleProductClass(dictDataService, item.getMatnr());
                        if (StringUtils.isNotEmpty(productClass)) {
                            item.setMatnr(item.getMatnr().substring(0, item.getMatnr().indexOf("+")));
                        }
                        TextFileUtil.fillField(record, item.getMatnr(), 18, 42, "部品番号");         // 部品番号 (18-42)
                        TextFileUtil.fillField(record, productClass, 43, 43, "製品区分");                      // 製品区分 (43)
                        TextFileUtil.fillField(record, "", 44, 68, "部品名称");                      // 部品名称 (44-68)
                        TextFileUtil.fillField(record, item.getZmethd(), 69, 70, "納入方式");        // 納入方式 (69-70)
                        TextFileUtil.fillField(record, item.getWanst(), 71, 77, "納入場所");         // 納入場所 (71-77)
                        TextFileUtil.fillField(record, item.getDispo(), 78, 80, "計画担当者コード");   // 計画担当者コード (78-80)
                        TextFileUtil.fillField(record, item.getMeins(), 81, 83, "発注単位");         // 発注単位 (81-83)
                        TextFileUtil.fillField(record, "", 84, 84, "単位条件");        // 単位条件 (84)
                        TextFileUtil.fillNumber(record, null, 85, 93, 5);               // 換算係数 (85-93,5)
                        TextFileUtil.fillNumber(record, item.getBstrf(), 94, 100, 0);                // ＳＮＥＰ (94-100)
                        TextFileUtil.fillField(record, "", 101, 108, "荷姿コード");                  // 荷姿コード (101-108)
                        TextFileUtil.fillField(record, item.getZmonth(), 109, 114, "年度起点年月");   // 年度起点年月 (109-114)
                        
                        // 12个月的内示数
                        TextFileUtil.fillNumber(record, item.getGsmng1(), 115, 122, 0);             // 年度開始月の内示数 (115-122)
                        TextFileUtil.fillNumber(record, item.getGsmng2(), 123, 130, 0);             // 年度開始月＋１ヶ月の内示数 (123-130)
                        TextFileUtil.fillNumber(record, item.getGsmng3(), 131, 138, 0);             // 年度開始月＋２ヶ月の内示数 (131-138)
                        TextFileUtil.fillNumber(record, item.getGsmng4(), 139, 146, 0);             // 年度開始月＋３ヶ月の内示数 (139-146)
                        TextFileUtil.fillNumber(record, item.getGsmng5(), 147, 154, 0);             // 年度開始月＋４ヶ月の内示数 (147-154)
                        TextFileUtil.fillNumber(record, item.getGsmng6(), 155, 162, 0);             // 年度開始月＋５ヶ月の内示数 (155-162)
                        TextFileUtil.fillNumber(record, item.getGsmng7(), 163, 170, 0);             // 年度開始月＋６ヶ月の内示数 (163-170)
                        TextFileUtil.fillNumber(record, item.getGsmng8(), 171, 178, 0);             // 年度開始月＋７ヶ月の内示数 (171-178)
                        TextFileUtil.fillNumber(record, item.getGsmng9(), 179, 186, 0);             // 年度開始月＋８ヶ月の内示数 (179-186)
                        TextFileUtil.fillNumber(record, item.getGsmng10(), 187, 194, 0);            // 年度開始月＋９ヶ月の内示数 (187-194)
                        TextFileUtil.fillNumber(record, item.getGsmng11(), 195, 202, 0);            // 年度開始月＋１０ヶ月の内示数 (195-202)
                        TextFileUtil.fillNumber(record, item.getGsmng12(), 203, 210, 0);            // 年度開始月＋１１ヶ月の内示数 (203-210)
                        
                        // 合计
                        TextFileUtil.fillNumber(record, item.getFirstHalfTotal(), 211, 218, 0);            // 上期合計 (211-218)
                        TextFileUtil.fillNumber(record, item.getSecondHalfTotal(), 219, 226, 0);            // 下期合計 (219-226)
                        TextFileUtil.fillNumber(record, item.getYearTotal(), 227, 234, 0);             // 年度合計 (227-234)
                        
                        TextFileUtil.fillField(record, item.getBeskz(), 235, 235, "自己調達区分");                // 自己調達区分 (235)
                        TextFileUtil.fillField(record, "", 236, 240, "受給先コード");                // 受給先コード (236-240)
                        TextFileUtil.fillField(record, "", 241, 242, "受給先要元");                  // 受給先要元 (241-242)
                        TextFileUtil.fillField(record, DateUtils.parseDateToStrWithTz("yyyyMMdd",
                            latestYearForecast.getCreateTime(), tz), 243, 250, "処理年月日");             // 処理年月日 (243-250)
                        TextFileUtil.fillField(record, "", 251, 255, "配信先取引先");    // 配信先取引先 (251-255)
                        TextFileUtil.fillField(record, "", 256, 257, "配信先デポ");                  // 配信先デポ (256-257)
                        
                        writer.write(record);
                        writer.write("\r\n");
                    }
                }
            }
            // 更新已读状态
            this.updateYearForecastReadStatus(latestYearForecast.getId(), "Y");
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("生成年度预测txt文件失败", e);
            return AjaxResult.error("生成年度预测txt文件失败：" + e.getMessage());
        }
    }

    @Override
    @DataScope(supplierAlias = "a")
    public AjaxResult downloadWeekForecastTxt(SapWeekForecast weekForecast, String tz) {
        try {
            // 获取最新的周预测数据
            SapWeekForecast latestWeekForecast = this.getLatestWeekForecast(weekForecast);
            if (latestWeekForecast == null) {
                return AjaxResult.error(MessageUtils.message("forecast.week.no.data"));
            }

            String fileName = String.format("HM02_%s.txt", UUID.randomUUID());
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), "Shift-JIS"))) {
                
                if (CollectionUtils.isNotEmpty(latestWeekForecast.getSubItems())) {
                    for (SapWeekForecastItem item : latestWeekForecast.getSubItems()) {
                        // 创建一个877字符长的字符数组，初始化为空格
                        char[] record = new char[877];
                        Arrays.fill(record, ' ');
                        
                        // 按照规范中的具体位置填充数据
                        TextFileUtil.fillField(record, "03", 1, 2, "F/#");                          // F/# (1-2)
                        TextFileUtil.fillField(record, "", 3, 6, "発注会社");                    // 発注会社 (3-6)
                        TextFileUtil.fillField(record, latestWeekForecast.getFlief(), 7, 11, "取引先");  // 取引先 (7-11)
                        TextFileUtil.fillField(record, latestWeekForecast.getZdepot(), 12, 13, "デポ");                         // デポ (12-13)
                        TextFileUtil.fillField(record, item.getWerks(), 14, 17, "要求工場");        // 要求工場 (14-17)
                        String productClass = SapObjectConverter.handleProductClass(dictDataService, item.getMatnr());
                        if (StringUtils.isNotEmpty(productClass)) {
                            item.setMatnr(item.getMatnr().substring(0, item.getMatnr().indexOf("+")));
                        }
                        TextFileUtil.fillField(record, item.getMatnr(), 18, 42, "部品番号");        // 部品番号 (18-42)
                        TextFileUtil.fillField(record, productClass, 43, 43, "製品区分");                     // 製品区分 (43)
                        TextFileUtil.fillField(record, "", 44, 68, "部品名称");                     // 部品名称 (44-68)
                        TextFileUtil.fillField(record, item.getZmethd(), 69, 70, "納入方式");       // 納入方式 (69-70)
                        TextFileUtil.fillField(record, item.getWanst(), 71, 77, "納入場所");        // 納入場所 (71-77)
                        TextFileUtil.fillField(record, item.getDispo(), 78, 80, "計画担当者");      // 計画担当者 (78-80)
                        TextFileUtil.fillField(record, item.getOrdCls(), 81, 81, "発注区分");                     // 発注区分 (81)
                        TextFileUtil.fillField(record, item.getMeins(), 82, 84, "発注単位");        // 発注単位 (82-84)
                        TextFileUtil.fillField(record, "", 85, 85, "単位条件");       // 単位条件 (85)
                        TextFileUtil.fillNumber(record, null, 86, 94, 5);                          // 換算係数 (86-94,5)
                        TextFileUtil.fillField(record, "", 95, 95, "重要保安区分");                 // 重要保安区分 (95)
                        TextFileUtil.fillField(record, item.getPoutMk(), 96, 97, "量産打切りマーク"); // 量産打切りマーク (96-97)
                        TextFileUtil.fillField(record, item.getPoutYm(), 98, 103, "量産打切り年月"); // 量産打切り年月 (98-103)
                        TextFileUtil.fillNumber(record, item.getBstrf(), 104, 110, 0);             // ＳＮＥＰ (104-110)
                        TextFileUtil.fillField(record, "", 111, 118, "荷姿コード");                 // 荷姿コード (111-118)
                        TextFileUtil.fillField(record, "", 125, 126, "週№");         // 週№ (125-126)

                        // 日別内示数量
                        if (item.getDetails() != null) {
                            String lastMonth = item.getDetails().get(0).getForecastDate().toString().substring(4, 6);
                            String lastYear = item.getDetails().get(0).getForecastDate().toString().substring(0, 4);
                            // 计算当前月份，lastMonth（01...02...10...12） + 1，超过12需要处理跨年
                            String currentMonth = Integer.parseInt(lastMonth) + 1 > 12 ? "01" : String.format("%02d", Integer.parseInt(lastMonth) + 1);
                            String nextMonth = Integer.parseInt(currentMonth) + 1 > 12 ? "01" : String.format("%02d", Integer.parseInt(currentMonth) + 1);
                            String currentYear = Integer.parseInt(lastMonth) + 1 > 12 ? String.valueOf(Integer.parseInt(lastYear) + 1) : lastYear;
                            TextFileUtil.fillField(record, currentYear + currentMonth, 119, 124, "Ｎ月＿年月");    // Ｎ月＿年月 (119-124)

                            int startPos = 127;
                            // N-1月（12日～31日）的20个数据
                            for (int i = 0; i < 20; i++) {
                                TextFileUtil.fillNumber(record, getDetailAmount(item.getDetails(), lastMonth, i+12),
                                    startPos + i*8, startPos + i*8 + 7, 0);
                            }

                            // N月（01日～31日）的31个数据
                            startPos = 287;
                            for (int i = 0; i < 31; i++) {
                                TextFileUtil.fillNumber(record, getDetailAmount(item.getDetails(), currentMonth, i+1),
                                    startPos + i*8, startPos + i*8 + 7, 0);
                            }

                            // N+1月（01日～31日）的31个数据
                            startPos = 535;
                            for (int i = 0; i < 31; i++) {
                                TextFileUtil.fillNumber(record, getDetailAmount(item.getDetails(), nextMonth, i+1),
                                    startPos + i*8, startPos + i*8 + 7, 0);
                            }

                            // N+2月（01日～04日）的4个数据
                            startPos = 783;
                            for (int i = 0; i < 4; i++) {
                                TextFileUtil.fillNumber(record, getDetailAmount(item.getDetails(), Integer.parseInt(nextMonth) + 1 > 12 ? "01" : String.format("%02d", Integer.parseInt(nextMonth) + 1), i+1),
                                    startPos + i*8, startPos + i*8 + 7, 0);
                            }
                        }
//                        if (item.getDetails() != null) {
//                            int startPos = 127;  // 起始位置
//                            int detailCount = 0; // 明细计数
//
//                            // 按顺序填充所有明细数据
//                            for (SapWeekForecastItemDetail detail : item.getDetails()) {
//                                if (detailCount < 86) { // 总共86个日期数据位置 (20+31+31+4)
//                                    TextFileUtil.fillNumber(record, detail.getForecastQty(),
//                                            startPos + detailCount*8,
//                                            startPos + detailCount*8 + 7,
//                                            0);
//                                    detailCount++;
//                                }
//                            }
//
//                            // 剩余位置填充0
//                            while (detailCount < 86) {
//                                TextFileUtil.fillNumber(record, BigDecimal.ZERO,
//                                        startPos + detailCount*8,
//                                        startPos + detailCount*8 + 7,
//                                        0);
//                                detailCount++;
//                            }
//                        }

                        // 月別内示合計数
                        TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(item.getDatsLm()), 815, 822, 0);    // Ｎ－１月合計 (815-822)
                        TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(item.getDatsM()), 823, 830, 0); // Ｎ月合計 (823-830)
                        TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(item.getDatsNm()), 831, 838, 0);    // Ｎ＋１月合計 (831-838)
                        TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(item.getDatsNnm()), 839, 846, 0); // Ｎ＋２月合計 (839-846)
                        
                        TextFileUtil.fillField(record, "", 847, 854, "日別内示範囲年月日＿ＦＲＯＭ"); // 日別内示範囲年月日＿ＦＲＯＭ (847-854)
                        TextFileUtil.fillField(record, item.getBeskz(), 855, 855, "自己調達区分");   // 自己調達区分 (855)
                        TextFileUtil.fillField(record, "", 856, 860, "受給先コード");               // 受給先コード (856-860)
                        TextFileUtil.fillField(record, "", 861, 862, "受給先要元");                // 受給先要元 (861-862)
                        TextFileUtil.fillField(record, DateUtils.parseDateToStrWithTz("yyyyMMdd",
                            latestWeekForecast.getCreateTime(), tz), 863, 870, "処理年月日"); // 処理年月日 (863-870)
                        TextFileUtil.fillField(record, "", 871, 875, "配信先_取引先"); // 配信先_取引先 (871-875)
                        TextFileUtil.fillField(record, "", 876, 877, "配信先_デポ");               // 配信先_デポ (876-877)
                        
                        writer.write(record);
                        writer.write("\r\n");
                    }
                }
            }
            // 更新已读状态
            this.updateWeekForecastReadStatus(latestWeekForecast.getId(), "Y");
            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("生成周预测txt文件失败", e);
            return AjaxResult.error("生成周预测txt文件失败：" + e.getMessage());
        }
    }

    private String addOneMonth(String yearMonth, int monthsToAdd) {
        // 将字符串解析为YearMonth对象
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        YearMonth ym = YearMonth.parse(yearMonth, formatter);

        // 加n个月
        YearMonth nextMonth = ym.plusMonths(monthsToAdd);

        // 转回字符串格式
        return nextMonth.format(formatter);
    }

    // 辅助方法：根据月份和日期从明细列表中获取对应的数量
    private BigDecimal getDetailAmount(List<SapWeekForecastItemDetail> details, String month, int day) {
        if (details == null) return BigDecimal.ZERO;

        return details.stream()
            .filter(d -> d.getForecastDate().toString().substring(4, 6).equals(month) && d.getForecastDate().toString().substring(6).equals(String.format("%02d", day)))
            .map(SapWeekForecastItemDetail::getForecastQty)
            .findFirst()
            .orElse(BigDecimal.ZERO);
    }

    @Override
    public void updateWeekForecastReadStatus(Long id, String isRead) {
        sapForecastMapper.updateWeekForecastReadStatus(id, isRead);
    }
    
    @Override
    public void updateMonthForecastReadStatus(Long id, String isRead) {
        sapForecastMapper.updateMonthForecastReadStatus(id, isRead);
    }
    
    @Override
    public void updateYearForecastReadStatus(Long id, String isRead) {
        sapForecastMapper.updateYearForecastReadStatus(id, isRead);
    }
}