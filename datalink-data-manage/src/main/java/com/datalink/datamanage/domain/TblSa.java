package com.datalink.datamanage.domain;

import com.datalink.common.DataType;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 协议对象 tbl_sa
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public class TblSa extends BaseEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 协议ID */
    private Long saId;

    /** 协议编号 */
    @Excel(name = "协议编号")
    private String saCode;

    /** 消息类型 */
    @Excel(name = "消息类型")
    private String msgType;

    /** IDOC编号 */
    @Excel(name = "IDOC编号")
    private Long docNum;

    /** 公司代码 */
    @Excel(name = "公司代码")
    private String compCode;

    /** 工厂代码 */
    @Excel(name = "工厂代码")
    private String plantCode;

    /** 工厂名称 */
    @Excel(name = "工厂名称")
    private String plantName;

    /** 采购组 */
    @Excel(name = "采购组")
    private String plannerNo;

    /** 采购组描述 */
    @Excel(name = "采购组描述")
    private String plannerName;

    /** 供应商代码 */
    @Excel(name = "供应商代码")
    private String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String suppName;

    /** 采购订单日期 */
    @JsonFormat(pattern = "yyyyMMdd")
    @Excel(name = "采购订单日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date poDate;

    /** 预留字段1 */
    @Excel(name = "预留字段1")
    private String info1;

    /** 预留字段2 */
    @Excel(name = "预留字段2")
    private String info2;

    /** 预留字段3 */
    @Excel(name = "预留字段3")
    private String info3;

    /** 收发标志 */
    @Excel(name = "收发标志")
    private String direction;

    /** KafKa发送状态 */
    @Excel(name = "KafKa发送状态")
    private String kafkaStatus;

    /** 协议行项目信息 */
    private List<TblSaItem> detail;

    public void setSaId(Long saId)
    {
        this.saId = saId;
    }

    public Long getSaId()
    {
        return saId;
    }
    public void setSaCode(String saCode)
    {
        this.saCode = saCode;
    }

    public String getSaCode()
    {
        return saCode;
    }
    public void setMsgType(String msgType)
    {
        this.msgType = msgType;
    }

    public String getMsgType()
    {
        return msgType;
    }
    public void setDocNum(Long docNum)
    {
        this.docNum = docNum;
    }

    public Long getDocNum()
    {
        return docNum;
    }
    public void setCompCode(String compCode)
    {
        this.compCode = compCode;
    }

    public String getCompCode()
    {
        return compCode;
    }
    public void setPlantCode(String plantCode)
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode()
    {
        return plantCode;
    }
    public void setPlantName(String plantName)
    {
        this.plantName = plantName;
    }

    public String getPlantName()
    {
        return plantName;
    }
    public void setPlannerNo(String plannerNo)
    {
        this.plannerNo = plannerNo;
    }

    public String getPlannerNo()
    {
        return plannerNo;
    }
    public void setPlannerName(String plannerName)
    {
        this.plannerName = plannerName;
    }

    public String getPlannerName()
    {
        return plannerName;
    }
    public void setSuppCode(String suppCode)
    {
        this.suppCode = suppCode;
    }

    @Override
    public DataType getObjectType() {
        return DataType.SA_TYPE;
    }

    public String getSuppCode()
    {
        return suppCode;
    }
    public void setSuppName(String suppName)
    {
        this.suppName = suppName;
    }

    public String getSuppName()
    {
        return suppName;
    }
    public void setPoDate(Date poDate)
    {
        this.poDate = poDate;
    }

    public Date getPoDate()
    {
        return poDate;
    }
    public void setInfo1(String info1)
    {
        this.info1 = info1;
    }

    public String getInfo1()
    {
        return info1;
    }
    public void setInfo2(String info2)
    {
        this.info2 = info2;
    }

    public String getInfo2()
    {
        return info2;
    }
    public void setInfo3(String info3)
    {
        this.info3 = info3;
    }

    public String getInfo3()
    {
        return info3;
    }
    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    public List<TblSaItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblSaItem> detail)
    {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("saId", getSaId())
            .append("saCode", getSaCode())
            .append("msgType", getMsgType())
            .append("docNum", getDocNum())
            .append("compCode", getCompCode())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("plannerNo", getPlannerNo())
            .append("plannerName", getPlannerName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("poDate", getPoDate())
            .append("info1", getInfo1())
            .append("info2", getInfo2())
            .append("info3", getInfo3())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("tblSaItemList", getDetail())
            .toString();
    }
}
