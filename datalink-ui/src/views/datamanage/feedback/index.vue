<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item :label="$t('feedback.dnNo')" prop="dnNo">
        <el-input
          v-model="queryParams.dnNo"
          :placeholder="$t('feedback.enterDnNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="!isCarrierWithDepot" :label="$t('order.form.depot')" prop="depot">
        <el-input v-model="queryParams.depot" :placeholder="$t('order.placeholder.depot')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('feedback.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('feedback.enterCompCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('feedback.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('feedback.enterSuppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('feedback.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('feedback.enterSuppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('feedback.plantCode')" prop="plantCode">
        <el-input
          v-model="queryParams.plantCode"
          :placeholder="$t('feedback.enterPlantCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('feedback.plantName')" prop="plantName">
        <el-input
          v-model="queryParams.plantName"
          :placeholder="$t('feedback.enterPlantName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("feedback.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("feedback.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['datamanage:feedback:export']"
        >{{ $t('feedback.export') }}</el-button>
      </el-col> -->

      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-printer" size="mini" :loading="printTxtLoading"
          @click="handlePrintTxt">{{
            $t('order.button.printTxt') }}</el-button>
      </el-col>

      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="feedbackList"
      @selection-change="handleSelectionChange"
    >
    <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('feedback.dnNo')" align="center" prop="dnNo">
        <template slot-scope="scope">
          <router-link
            :to="{
              name: 'FeedbackDetail',
              params: { feedbackId: scope.row.feedId },
            }"
            class="link-type"
          >
            <span>{{ scope.row.dnNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('order.orderDetail.depot')" align="center" prop="depot" />

      <el-table-column
        :label="$t('feedback.compCode')"
        align="center"
        prop="compCode"
      />
      <el-table-column
        :label="$t('feedback.suppCode')"
        align="center"
        prop="suppCode"
      />
      <!-- <el-table-column :label="$t('feedback.suppName')" align="center" prop="suppName" /> -->
      <el-table-column
        :label="$t('feedback.plantCode')"
        align="center"
        prop="plantCode"
      />
      <!-- <el-table-column :label="$t('feedback.plantName')" align="center" prop="plantName" /> -->

      <el-table-column
        :label="$t('feedback.actionsText')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.dnNo && scope.row.dnNo.startsWith('ASN')"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            >{{ $t("feedback.viewText") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改收货反馈对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('feedback.dnNo')" prop="dnno">
          <el-input
            v-model="form.dnno"
            :placeholder="$t('feedback.enterDnNo')"
          />
        </el-form-item>
        <el-form-item :label="$t('feedback.compCode')" prop="compcode">
          <el-input
            v-model="form.compcode"
            :placeholder="$t('feedback.enterCompCode')"
          />
        </el-form-item>
        <el-form-item :label="$t('feedback.plantCode')" prop="plantcode">
          <el-input
            v-model="form.plantcode"
            :placeholder="$t('feedback.enterPlantCode')"
          />
        </el-form-item>
        <el-form-item :label="$t('feedback.plantName')" prop="plantname">
          <el-input
            v-model="form.plantname"
            :placeholder="$t('feedback.enterPlantName')"
          />
        </el-form-item>
        <el-form-item :label="$t('feedback.suppCode')" prop="suppcode">
          <el-input
            v-model="form.suppcode"
            :placeholder="$t('feedback.enterSuppCode')"
          />
        </el-form-item>
        <el-form-item :label="$t('feedback.suppName')" prop="suppname">
          <el-input
            v-model="form.suppname"
            :placeholder="$t('feedback.enterSuppName')"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{
          $t("feedback.confirm")
        }}</el-button>
        <el-button @click="cancel">{{ $t("feedback.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFeedback,
  getFeedback,
  delFeedback,
  addFeedback,
  updateFeedback,
  exportFeedback,
  printTxt
} from "@/api/datamanage/feedback";

import { listAsn } from "@/api/datamanage/asn";

export default {
  name: "Feedback",
  data() {
    return {
      // 遮罩层
      loading: true,
      printTxtLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货反馈表格数据
      feedbackList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dnNo: null,
        compCode: null,
        plantCode: null,
        plantName: null,
        suppCode: null,
        suppName: null,
        orderByColumn: "createTime",
        isAsc: "desc",
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dnno: [
          {
            required: true,
            message: this.$t("feedback.validation.dnNoRequired"),
            trigger: "blur",
          },
        ],
        compcode: [
          {
            required: true,
            message: this.$t("feedback.validation.compCodeRequired"),
            trigger: "blur",
          },
        ],
        plantcode: [
          {
            required: true,
            message: this.$t("feedback.validation.plantCodeRequired"),
            trigger: "blur",
          },
        ],
        plantname: [
          {
            required: true,
            message: this.$t("feedback.validation.plantNameRequired"),
            trigger: "blur",
          },
        ],
        suppcode: [
          {
            required: true,
            message: this.$t("feedback.validation.suppCodeRequired"),
            trigger: "blur",
          },
        ],
        suppname: [
          {
            required: true,
            message: this.$t("feedback.validation.suppNameRequired"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    // if (this.$route.path.toLowerCase().endsWith('recv/feedback')) {
    //   this.queryParams.direction = 'I';
    // } else {
    //   this.queryParams.direction = 'O';
    // }
    this.getList();
  },

  computed: {
    // 判断是否为固定仓库的运输商
    isCarrierWithDepot() {
      console.log(this.$store.state.user)
      const roles = this.$store.state.user.roles;
      const remark = this.$store.state.user.remark;
      return roles && roles.some(role => role === 'carrier') && remark;
    }
  },
  methods: {
    /** 查询收货反馈列表 */
    getList() {
      this.loading = true;
      listFeedback(this.queryParams).then((response) => {
        this.feedbackList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        feedId: null,
        dnno: null,
        compcode: null,
        plantcode: null,
        plantname: null,
        suppcode: null,
        suppname: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.feedId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("feedback.addFeedback");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const feedbackId = row.feedId || this.ids;
      this.$router.push("/feedback/detail/" + feedbackId);
    },
    /** 查看按钮操作 */
    async handleView(row) {
      this.reset();
      const asnCode = row.dnNo;
      try {
        const response = await this.getASNId(asnCode);
        if (response?.rows?.length > 0) {
          const asnId = response.rows[0].asnId;
          this.$router.push({ name: "AsnDetail", params: { asnId } });
        } else {
          this.msgError(this.$t("feedback.asnCodeNotFound"));
        }
      } catch (error) {
        this.msgError(this.$t("feedback.asnCodeNotFound"));
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.feedId != null) {
            updateFeedback(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("feedback.updateSuccess"));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addFeedback(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(this.$t("feedback.addSuccess"));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const feedids = row.feedId || this.ids;
      this.$confirm(
        this.$t("feedback.confirmDelete", { feedId: feedids }),
        this.$t("feedback.warning"),
        {
          confirmButtonText: this.$t("feedback.confirm"),
          cancelButtonText: this.$t("feedback.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          return delFeedback(feedids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("feedback.deleteSuccess"));
        })
        .catch(() => {});
    },
    getASNId(asnCode) {
      const asnQueryParams = {
        pageNum: 1,
        pageSize: 1,
        asnCode,
        docno: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        docdate: null,
        dnno: null,
        planDeliveryDate: null,
        deliveryDate: null,
        orderByColumn: "createTime",
        isAsc: "desc",
      };

      return listAsn(asnQueryParams);
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("feedback.confirmExport"),
        this.$t("feedback.warning"),
        {
          confirmButtonText: this.$t("feedback.confirm"),
          cancelButtonText: this.$t("feedback.cancel"),
          type: "warning",
        }
      )
        .then(() => {
          return exportFeedback(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        })
        .catch(() => {});
    },

    handlePrintTxt() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('feedback.alert.selectOrder'))
        return
      }
      this.printTxtLoading = true;

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close();
      }, 15000);

      console.log(this.ids)
      printTxt(this.ids).then(response => {
        // 通过 window.open 打开 PDF
        window.open(process.env.VUE_APP_BASE_API + "/common/download?fileName=" + encodeURI(response.msg) + "&delete=true");
        loading.close()
        this.printTxtLoading = false;
      }).catch(() => {
        loading.close()
        this.printTxtLoading = false;  // 如果请求失败，重置 loading 状态

      });
    },
  },
};
</script>
