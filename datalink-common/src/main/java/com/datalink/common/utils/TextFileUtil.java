package com.datalink.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;

/**
 * 文本文件格式化工具类
 */
public class TextFileUtil {
    private static final Logger log = LoggerFactory.getLogger(TextFileUtil.class);

    /**
     * 填充字符串字段，同时处理字段长度检查
     *
     * @param record 记录数组
     * @param value 要填充的值
     * @param start 起始位置（从1开始）
     * @param end 结束位置
     * @param fieldName 字段名称（用于日志）
     */
    public static void fillField(char[] record, String value, int start, int end, String fieldName) {
        if (value == null) value = "";
        int length = end - start + 1;
        
        // 计算字符串实际占用的字节数
        int byteLength = 0;
        StringBuilder sb = new StringBuilder();
        
        for (char c : value.toCharArray()) {
            int charByteLength = isFullWidth(c) ? 2 : 1;
            if (byteLength + charByteLength <= length) {
                sb.append(c);
                byteLength += charByteLength;
            } else {
                log.warn("字段[{}]的值[{}]超出长度限制{}字节，将被截断", fieldName, value, length);
                break;
            }
        }
        
        // 补充空格到指定长度
        while (byteLength < length) {
            sb.append(' ');
            byteLength++;
        }
        
        String paddedValue = sb.toString();
        System.arraycopy(paddedValue.toCharArray(), 0, record, start - 1, paddedValue.length());
    }

    /**
     * 填充数字字段
     *
     * @param record 记录数组
     * @param value 要填充的值
     * @param start 起始位置（从1开始）
     * @param end 结束位置
     * @param scale 小数位数
     */
    public static void fillNumber(char[] record, BigDecimal value, int start, int end, int scale) {
        if (value == null) value = BigDecimal.ZERO;
        int length = end - start + 1;
        
        if (scale > 0) {
            // 将数值乘以10的scale次方，去除小数点
            BigDecimal multiplier = BigDecimal.valueOf(Math.pow(10, scale));
            BigDecimal scaledValue = value.multiply(multiplier);
            
            // 格式化为固定长度的整数，左补0
            String format = "%0" + length + "d";
            String numStr = String.format(format, scaledValue.longValue());
            
            if (numStr.length() > length) {
                numStr = numStr.substring(numStr.length() - length);
                log.warn("数值[{}]超出字段长度{}，已截断为[{}]", value, length, numStr);
            }
            
            System.arraycopy(numStr.toCharArray(), 0, record, start - 1, numStr.length());
        } else {
            // 无小数位的情况：右对齐，左补0
            String format = "%0" + length + "d";
            String numStr = String.format(format, value.longValue());
            
            if (numStr.length() > length) {
                numStr = numStr.substring(numStr.length() - length);
                log.warn("数值[{}]超出字段长度{}，已截断为[{}]", value, length, numStr);
            }
            
            System.arraycopy(numStr.toCharArray(), 0, record, start - 1, numStr.length());
        }
    }

    /**
     * 判断是否为全角字符
     *
     * @param c 要判断的字符
     * @return 是否为全角字符
     */
    private static boolean isFullWidth(char c) {
        // 全角字符的Unicode范围
        return (c >= 0x3000 && c <= 0x9FFF)   // CJK统一表意文字
            || (c >= 0xFF00 && c <= 0xFFEF)   // 全角ASCII、全角标点
            || (c >= 0x30A0 && c <= 0x30FF)   // 片假名
            || (c >= 0x3040 && c <= 0x309F);  // 平假名
    }
} 