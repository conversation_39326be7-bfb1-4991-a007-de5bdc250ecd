<template>
  <div class="login">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form relative">
      <h3 class="title"> {{ $t('login.title') }}</h3>
      <lang-select class="right-menu-item hover-effect absolute login-i18n" />
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" auto-complete="off"
          :placeholder="$t('login.username.placeholder')">
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>

      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" auto-complete="off"
          :placeholder="$t('login.password.placeholder')" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input v-model="loginForm.code" auto-complete="off" :placeholder="$t('login.verificationCode.placeholder')"
          style="width: 63%" @keyup.enter.native="handleLogin">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img" />
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">{{ $t('login.rememberPwd')
        }}</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" size="medium" type="primary" style="width:100%;"
          @click.native.prevent="handleLogin">
          <span v-if="!loading">{{ $t('login.logIn') }}</span>
          <span v-else>{{ $t('login.loggingIn') }}</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <!--    <div class="el-login-footer">-->
    <!--      <span>Copyright © 2018-2021 ruoyi.vip All Rights Reserved.</span>-->
    <!--    </div>-->
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import { i18n } from '@/api/i18n';
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

import LangSelect from '@/components/LangSelect'

export default {
  name: "Login",
  components: {
    LangSelect
  },
  data() {
    return {
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loading: false,
      redirect: undefined
    };
  },
  computed: {
    // 计算属性：动态根据语言获取验证规则
    loginRules() {
      return {
        username: [
          { required: true, trigger: "blur", message: this.$t('login.username.empty') }
        ],
        password: [
          { required: true, trigger: "blur", message: this.$t('login.password.empty') }
        ],
        code: [
          { required: true, trigger: "change", message: this.$t('login.verificationCode.empty') }
        ]
      };
    },
    language() {
      return this.$store.getters.language;
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
    this.tellBackendCurrentLanguage();
  },
  methods: {
    tellBackendCurrentLanguage() {
      const lang = this.$store.getters.language;
      i18n(lang).then(() => {
      })
    },
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(() => { });
          }).catch(() => {
            this.loading = false;
            this.getCode();
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}

.title {
  margin: 10px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;

    input {
      height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}

.right-menu-item {
  display: inline-block;
  padding: 0 8px;
  height: 100%;
  font-size: 18px;
  color: #5a5e66;
  vertical-align: text-bottom;

  &.hover-effect {
    cursor: pointer;
    transition: background .3s;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.login-i18n {
  right: 5px;
  height: auto;
  width: auto;
  top: 10px;

  .custom-icon-size {

    width: 1.25em !important;
    height: 1.25em !important;
    color: #1890ff;
  }
}
</style>
