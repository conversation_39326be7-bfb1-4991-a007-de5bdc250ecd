package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.datalink.common.DataType;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;

/**
 * 收货反馈对象 tbl_feedback
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
public class TblFeedback  extends BaseHeadEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 收货反馈ID */
    private Long feedId;

    /** 送货单号 */
    @Excel(name = "送货单号")
    @JsonAlias("dnno")
    private String dnNo;

    /** 删除标识 */
    @Excel(name = "删除标识")
    private String delFlag;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String compName;

    /** 凭证日期 */
    @Excel(name = "凭证日期")
    private String docDate;

    /** 总金额 */
    @Excel(name = "总金额")
    private String totalAmount;

    /** 货币 */
    @Excel(name = "货币")
    private String currency;

    /** 结算单号 */
    @Excel(name = "结算单号")
    private String settlementNo;

    /** 开票日期 */
    @Excel(name = "开票日期")
    private String invoiceDate;

    /** 开票总金额 */
    @Excel(name = "开票总金额")
    private BigDecimal invoiceAmount;

    /** 开票总税额 */
    @Excel(name = "开票总税额")
    private BigDecimal invoiceTax;

    /** 金税发票号 */
    @Excel(name = "金税发票号")
    private String invoiceNo;

    private String depot;
    private String receivingPlace;
    private Date receivingDate;
    private BigDecimal receivingQuantity;
    private String orderUnit;
    private Date deliveryNoteDate;
    private Date deliveryNoteTime;

    /** 收发标志 */
    private String direction;

    /** KafKa发送状态 */
    private String kafkaStatus;

    /** 验收状态 */
    @Excel(name = "验收状态")
    private String status;

    /** 收货反馈行项目信息 */
    private List<TblFeedbackItem> detail;

    public void setFeedId(Long feedId)
    {
        this.feedId = feedId;
    }

    public Long getFeedId()
    {
        return feedId;
    }
    public void setDnNo(String dnNo)
    {
        this.dnNo = dnNo;
    }

    public String getDnNo()
    {
        return dnNo;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setCompName(String compName)
    {
        this.compName = compName;
    }

    public String getCompName()
    {
        return compName;
    }

    public void setDocDate(String docDate)
    {
        this.docDate = docDate;
    }

    public String getDocDate()
    {
        return docDate;
    }

    public void setTotalAmount(String totalAmount)
    {
        this.totalAmount = totalAmount;
    }

    public String getTotalAmount()
    {
        return totalAmount;
    }

    public void setCurrency(String currency)
    {
        this.currency = currency;
    }

    public String getCurrency()
    {
        return currency;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(BigDecimal invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getDepot() {
        return depot;
    }

    public void setDepot(String depot) {
        this.depot = depot;
    }

    public String getReceivingPlace() {
        return receivingPlace;
    }

    public void setReceivingPlace(String receivingPlace) {
        this.receivingPlace = receivingPlace;
    }

    public Date getReceivingDate() {
        return receivingDate;
    }

    public void setReceivingDate(Date receivingDate) {
        this.receivingDate = receivingDate;
    }

    public BigDecimal getReceivingQuantity() {
        return receivingQuantity;
    }

    public void setReceivingQuantity(BigDecimal receivingQuantity) {
        this.receivingQuantity = receivingQuantity;
    }

    public String getOrderUnit() {
        return orderUnit;
    }

    public void setOrderUnit(String orderUnit) {
        this.orderUnit = orderUnit;
    }

    public Date getDeliveryNoteDate() {
        return deliveryNoteDate;
    }

    public void setDeliveryNoteDate(Date deliveryNoteDate) {
        this.deliveryNoteDate = deliveryNoteDate;
    }

    public Date getDeliveryNoteTime() {
        return deliveryNoteTime;
    }

    public void setDeliveryNoteTime(Date deliveryNoteTime) {
        this.deliveryNoteTime = deliveryNoteTime;
    }

    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }

    @Override
    public DataType getObjectType() {
        return DataType.FEEDBACK_TYPE;
    }

    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public List<TblFeedbackItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblFeedbackItem> detail)
    {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("feedId", getFeedId())
            .append("dnNo", getDnNo())
            .append("delFlag", getDelFlag())
            .append("compCode", getCompCode())
            .append("compName", getCompName())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("docDate", getDocDate())
            .append("totalAmount", getTotalAmount())
            .append("currency", getCurrency())
            .append("settlementNo", getSettlementNo())
            .append("invoiceDate", getInvoiceDate())
            .append("invoiceAmount", getInvoiceAmount())
            .append("invoiceTax", getInvoiceTax())
            .append("invoiceNo", getInvoiceNo())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("status", getStatus())
            .append("tblFeedbackItemList", getDetail())
            .toString();
    }
}
