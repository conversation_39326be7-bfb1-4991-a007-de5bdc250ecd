package com.datalink.datamanage.controller;

import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblSapKanbanSv;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblSapKanbanKd;
import com.datalink.datamanage.service.ITblSapKanbanService;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.common.core.page.TableDataInfo;

/**
 * 看板KDController
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/datamanage/kanban")
public class TblSapKanbanController extends BaseController
{
    @Autowired
    private ITblSapKanbanService tblSapKanbanService;

    /**
     * 查询看板KD列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:list')")
    @GetMapping("/listKd")
    public TableDataInfo listKd(TblSapKanbanKd tblSapKanbanKd)
    {
        startPage();
        List<TblSapKanbanKd> list = tblSapKanbanService.selectTblSapKanbanKdList(tblSapKanbanKd);
        return getDataTable(list);
    }

    /**
     * 查询看板LINE列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:list')")
    @GetMapping("/listLine")
    public TableDataInfo listLine(TblSapKanbanKd tblSapKanbanLine)
    {
        startPage();
        List<TblSapKanbanKd> list = tblSapKanbanService.selectTblSapKanbanLineList(tblSapKanbanLine);
        return getDataTable(list);
    }

    /**
     * 查询看板SV列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:list')")
    @GetMapping("/listSv")
    public TableDataInfo listSv(TblSapKanbanSv tblSapKanbanSv)
    {
        startPage();
        List<TblSapKanbanSv> list = tblSapKanbanService.selectTblSapKanbanSvList(tblSapKanbanSv);
        return getDataTable(list);
    }

    /**
     * 获取看板KD详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tblSapKanbanService.selectTblSapKanbanKdById(id));
    }

    /**
     * 下载看板KD列表txt
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:export')")
    @PostMapping("/downloadKanbanKdTxt")
    @Log(title = "下载Kanban-KD", businessType = BusinessType.EXPORT)
    public AjaxResult downloadKanbanKdTxt(@RequestBody List<Long> ids, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        return tblSapKanbanService.downloadKanbanKdTxt(ids, tz);
    }

    /**
     * 下载看板LINE列表txt
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:export')")
    @PostMapping("/downloadKanbanLineTxt")
    @Log(title = "下载Kanban-LINE", businessType = BusinessType.EXPORT)
    public AjaxResult downloadKanbanLineTxt(@RequestBody List<Long> ids, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        return tblSapKanbanService.downloadKanbanLineTxt(ids, tz);
    }

    /**
     * 下载看板SV列表txt
     */
    @PreAuthorize("@ss.hasPermi('datamanage:kanban:export')")
    @PostMapping("/downloadKanbanSvTxt")
    @Log(title = "下载Kanban-SV", businessType = BusinessType.EXPORT)
    public AjaxResult downloadKanbanSvTxt(@RequestBody List<Long> ids, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz)
    {
        return tblSapKanbanService.downloadKanbanSvTxt(ids, tz);
    }
}
