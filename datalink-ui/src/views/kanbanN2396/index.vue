<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item :label="$t('kanban.form.demandCode')" prop="demandCode">
        <el-input v-model="queryParams.demandCode" :placeholder="$t('kanban.placeholder.demandCode')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('kanban.form.issueNo')" prop="issueNo">
        <el-input v-model="queryParams.issueNo" :placeholder="$t('kanban.placeholder.issueNo')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item :label="$t('kanban.form.deliveryInstructionDate')" prop="deliveryInstructionDate">
        <el-date-picker v-model="queryParams.deliveryInstructionDate" size="small" style="width: 240px"
          value-format="yyyy-MM-dd" type="date"
          :placeholder="$t('kanban.placeholder.deliveryInstructionDate')"></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('kanban.form.deliveryInstructionTime')" prop="deliveryInstructionTime">
        <el-time-picker v-model="queryParams.deliveryInstructionTime" size="small" style="width: 240px"
          value-format="HH:mm" format="HH:mm"
          :placeholder="$t('kanban.placeholder.deliveryInstructionTime')"></el-time-picker>
      </el-form-item> -->
      <el-form-item :label="$t('kanban.form.maker')" prop="maker">
        <el-input v-model="queryParams.maker" :placeholder="$t('kanban.placeholder.maker')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('kanban.form.depot')" prop="depot">
        <el-input v-model="queryParams.depot" :placeholder="$t('kanban.placeholder.depot')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('kanban.form.customerPartsNo')" prop="customerPartsNo">
        <el-input v-model="queryParams.customerPartsNo" :placeholder="$t('kanban.placeholder.customerPartsNo')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item :label="$t('kanban.form.deliveryTicketNo')" prop="deliveryTicketNo">
        <el-input v-model="queryParams.deliveryTicketNo" :placeholder="$t('kanban.placeholder.deliveryTicketNo')" clearable
          size="small" @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('kanban.button.search')
          }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t('kanban.button.reset')
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-download" size="mini" :loading="printLineKdSpecLoading"
          @click="handlePrintLineKdSpec">{{
            $t('kanban.button.printLineKdSpec') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="kdKanbanList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('kanban.table.demandCode')" align="center" prop="ZDMND_CD" />
      <el-table-column :label="$t('kanban.table.issueNo')" align="center" prop="ZISSU_NO" />
      <el-table-column :label="$t('kanban.table.deliveryInstructionDate')" align="center" prop="ZDELI_INSTR_YMD">
        <template slot-scope="scope">
          <span>{{ formatDate(scope.row.ZDELI_INSTR_YMD) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('kanban.table.deliveryInstructionTime')" align="center" prop="ZDELI_INSTR_HM">
        <template slot-scope="scope">
          <span>{{ formatTime(scope.row.ZDELI_INSTR_HM) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('kanban.table.maker')" align="center" prop="ZMAKER" />
      <el-table-column :label="$t('kanban.table.depot')" align="center" prop="ZDEPO" />
      <el-table-column :label="$t('kanban.table.customerPartsNo')" align="center" prop="ZCUST_PARTS_NO" />
      <el-table-column :label="$t('kanban.table.deliveryTicketNo')" align="center" prop="ZDELI_TICKET_NO" />
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import {
  listKd,
  downloadKanbanKdTxt
} from '@/api/datamanage/kanban'

export default {
  name: 'KanbanKd',
  data() {
    return {
      loading: true,
      printLineKdSpecLoading: false,
      ids: [],
      showSearch: true,
      total: 0,
      kdKanbanList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        demandCode: null,
        issueNo: null,
        deliveryInstructionDate: null,
        deliveryInstructionTime: null,
        maker: null,
        depot: null,
        customerPartsNo: null,
        deliveryTicketNo: null,
        orderByColumn: '',
        isAsc: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      listKd(this.queryParams).then(response => {
        this.kdKanbanList = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    handlePrintLineKdSpec() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('kanban.alert.selectKanban'))
        return
      }
      this.printLineKdSpecLoading = true

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      })

      downloadKanbanKdTxt(this.ids).then(response => {
        window.open(process.env.VUE_APP_BASE_API + "/common/download?fileName=" + encodeURI(response.msg) + "&delete=true")
        loading.close()
        this.printLineKdSpecLoading = false
      }).catch(() => {
        loading.close()
        this.printLineKdSpecLoading = false
      })
    },
    formatDate(dateStr) {
      if (!dateStr || dateStr.length !== 8) return ''
      return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
    },
    formatTime(timeStr) {
      if (!timeStr || timeStr.length !== 4) return ''
      return `${timeStr.slice(0, 2)}:${timeStr.slice(2, 4)}`
    }
  }
}
</script>
