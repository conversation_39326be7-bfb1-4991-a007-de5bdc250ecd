package com.datalink.datamanage.mapper;

import com.datalink.datamanage.domain.TblAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 附件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-24
 */
public interface TblAttachmentMapper
{
    /**
     * 查询附件
     *
     * @param attachmentId 附件ID
     * @return 附件
     */
    public TblAttachment selectTblAttachmentById(Long attachmentId);

    public List<TblAttachment> selectNoticeReplyTblAttachmentListByReplyId(Long replyId);

    /**
     * 查询附件列表
     *
     * @param tblAttachment 附件
     * @return 附件集合
     */
    public List<TblAttachment> selectTblAttachmentList(TblAttachment tblAttachment);

    /**
     * 新增附件
     *
     * @param tblAttachment 附件
     * @return 结果
     */
    public int insertTblAttachment(TblAttachment tblAttachment);

    /**
     * 修改附件
     *
     * @param tblAttachment 附件
     * @return 结果
     */
    public int updateTblAttachment(TblAttachment tblAttachment);

    /**
     * 删除附件
     *
     * @param attachmentId 附件ID
     * @return 结果
     */
    public int deleteTblAttachmentById(Long attachmentId);

    /**
     * 批量删除附件
     *
     * @param attachmentIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblAttachmentByIds(Long[] attachmentIds);

    public int batchTblAttachments(List<TblAttachment> attachmentList);

    public int deleteTblAttachmentByParentAndType(@Param("parent") Long parent, @Param("type") String type);
}
