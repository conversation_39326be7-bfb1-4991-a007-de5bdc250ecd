import request from '@/utils/request'

// 查询公告列表
export function listNotice(query) {
  return request({
    url: '/datamanage/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId) {
  return request({
    url: '/datamanage/notice/' + noticeId,
    method: 'get'
  })
}

// 新增公告
export function addNotice(data) {
  return request({
    url: '/datamanage/notice',
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: '/datamanage/notice',
    method: 'put',
    data: data
  })
}

// 删除公告
export function delNotice(noticeId) {
  return request({
    url: '/datamanage/notice/' + noticeId,
    method: 'delete'
  })
}

// 导出公告
export function exportNotice(query) {
  return request({
    url: '/datamanage/notice/export',
    method: 'get',
    params: query
  })
}


export function listNoticeReply(query) {
  return request({
    url: '/datamanage/notice/listNoticeReply',
    method: 'get',
    params: query
  })
}

export function listReply(query) {
  return request({
    url: '/datamanage/notice/listReply',
    method: 'get',
    params: query
  })
}

export function updateReply(reply) {
  return request({
    url: '/datamanage/notice/updateReply',
    method: 'post',
    data: reply
  })
}

export function updateReplyRead(reply) {
  return request({
    url: '/datamanage/notice/updateReplyRead',
    method: 'post',
    data: reply
  })
}
