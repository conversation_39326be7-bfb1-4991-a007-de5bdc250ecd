package com.datalink.api.domain;

import com.datalink.common.core.domain.BaseEntity;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class SapYearForecastItem extends BaseEntity {
    private Long id;
    private Long forecastId;  // 关联主表ID
    private String matnr;     // 部品番号
    private String plwrk;     // 要求工場
    private String zmethd;    // 納入方式
    private String wanst;     // 納入場所
    private String dispo;     // 計画担当
    private String beskz;     // 自己
    private BigDecimal bstrf; // SNEP
    private String zmonth;     // 开始月份

    private BigDecimal gsmng1;  // 1月数量
    private BigDecimal gsmng2;  // 2月数量
    private BigDecimal gsmng3;  // 3月数量
    private BigDecimal gsmng4;  // 4月数量
    private BigDecimal gsmng5;  // 5月数量
    private BigDecimal gsmng6;  // 6月数量
    private BigDecimal gsmng7;  // 7月数量
    private BigDecimal gsmng8;  // 8月数量
    private BigDecimal gsmng9;  // 9月数量
    private BigDecimal gsmng10; // 10月数量
    private BigDecimal gsmng11; // 11月数量
    private BigDecimal gsmng12; // 12月数量
    
    private BigDecimal firstHalfTotal;  // 上期合计
    private BigDecimal secondHalfTotal; // 下期合计
    private BigDecimal yearTotal;       // 年度合计
    
    private String meins;     // 発注単位
} 