package com.datalink.datamanage.domain;

import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 公告回复对象 tbl_notice_reply
 *
 * <AUTHOR>
 * @date 2022-05-02
 */
public class TblNoticeReply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 公告回复ID */
    private Long replyId;

    /** 回复内容 */
    private String content;

    /** 供应商 */
    @Excel(name = "供应商")
    private String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    private String suppName;

    /** 公告ID */
    @Excel(name = "公告ID")
    private Long noticeId;

    /** 回复时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回复时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date replyTime;

    /** KafKa发送状态 */
    @Excel(name = "KafKa发送状态")
    private String kafkaStatus;

    public List<TblAttachment> getReplyAttachmentList() {
        return replyAttachmentList;
    }

    public void setReplyAttachmentList(List<TblAttachment> replyAttachmentList) {
        this.replyAttachmentList = replyAttachmentList;
    }

    private List<TblAttachment> replyAttachmentList;

    public String getIsRead() {
        return isRead;
    }

    public void setIsRead(String isRead) {
        this.isRead = isRead;
    }

    private String isRead;

    public void setReplyId(Long replyId)
    {
        this.replyId = replyId;
    }

    public Long getReplyId()
    {
        return replyId;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setSuppCode(String suppCode)
    {
        this.suppCode = suppCode;
    }

    public String getSuppCode()
    {
        return suppCode;
    }
    public void setSuppName(String suppName)
    {
        this.suppName = suppName;
    }

    public String getSuppName()
    {
        return suppName;
    }
    public void setNoticeId(Long noticeId)
    {
        this.noticeId = noticeId;
    }

    public Long getNoticeId()
    {
        return noticeId;
    }
    public void setReplyTime(Date replyTime)
    {
        this.replyTime = replyTime;
    }

    public Date getReplyTime()
    {
        return replyTime;
    }
    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("replyId", getReplyId())
            .append("content", getContent())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("noticeId", getNoticeId())
            .append("createTime", getCreateTime())
            .append("replyTime", getReplyTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("kafkaStatus", getKafkaStatus())
            .toString();
    }
}
