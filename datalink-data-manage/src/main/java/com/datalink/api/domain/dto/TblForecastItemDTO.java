package com.datalink.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 预测行项目DTO
 */
@Data
public class TblForecastItemDTO {
    
    @NotBlank(message = "预测编号不能为空")
    @JsonProperty("ZFNO")
    private String forecastCode;
    
    @NotBlank(message = "预测行项目不能为空")
    @JsonProperty("ZFPOS")
    private String itemNo;
    
    @NotBlank(message = "工厂代码不能为空")
    @JsonProperty("WERKS")
    private String plantCode;
    
    @NotBlank(message = "物料编码不能为空")
    @JsonProperty("MATNR")
    private String articleNo;
    
    @NotBlank(message = "供应商代码不能为空")
    @JsonProperty("LIFNR")
    private String suppCode;
    
    @NotBlank(message = "预测类型不能为空")
    @JsonProperty("FCTYPE")
    private String proType;
    
    @NotBlank(message = "预测月份不能为空")
    @JsonProperty("KMONTH")
    private String kmonth;
    
    @NotNull(message = "数量不能为空")
    @JsonProperty("MENGE")
    private String quantity;
    
    @NotBlank(message = "单位不能为空")
    @JsonProperty("MEINS")
    private String unit;
}
