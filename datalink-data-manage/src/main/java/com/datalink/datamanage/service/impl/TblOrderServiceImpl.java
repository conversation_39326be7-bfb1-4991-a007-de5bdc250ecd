package com.datalink.datamanage.service.impl;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.common.DataConstants;
import com.datalink.common.Util;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.utils.*;
import com.datalink.common.utils.SapApiClient;
import com.datalink.common.utils.file.FileUtils;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.mapper.TblEventMapper;
import com.datalink.datamanage.mapper.TblOrderMapper;
import com.datalink.datamanage.service.ITblOrderService;
import com.datalink.framework.web.service.PermissionService;
import com.datalink.system.service.ISysConfigService;
import com.datalink.system.service.ISysDictDataService;
import com.datalink.system.service.ISysDictTypeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-22
 */
@Service
public class TblOrderServiceImpl implements ITblOrderService
{
    private static final Logger logger = LoggerFactory.getLogger(TblOrderServiceImpl.class);

    @Resource
    private TblOrderMapper tblOrderMapper;

    @Resource
    private TblEventMapper tblEventMapper;

    @Resource
    private ISysConfigService configService;

    @Autowired
    private SapApiClient sapApiClient;

    @Value("${file.jasper-template-dir}")
    private String templatePath;

    @Autowired
    private ISysDictDataService dictDataService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private PermissionService permissionService;

    // 固定值
    private static final String PREFIX = "!GP?";
    private static final String SUFFIX = "!END?";

    /**
     * 查询订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    @Override
    public TblOrder selectTblOrderById(Long orderId)
    {
        //更新订单已阅状态
        TblOrder order = tblOrderMapper.selectTblOrderById(orderId);
        makeRead(order);
        return order;
    }

    private void makeRead(TblOrder order) {
        if (null != order && !order.getIsRead().equals("Y") && order.getDirection().equals(DataConstants.DIRECTION_IN)){
            tblOrderMapper.markRead(order.getOrderId());
//            Map<String, Object> map = new HashMap<>();
//            map.put("isRead", "Y");
//            map.put("orderCode", order.getOrderCode());
//            TblEvent event = new TblEvent(ServiceEnum.OrderService, "updateOrderByCodeMap", map, configService.selectConfigByKey(DataConstants.COMPANY_ID_CONFIG_KEY), order.getCompCode(), order.getPlantName());
//            tblEventMapper.insertTblEvent(event);
        }
    }

    /**
     * 查询订单列表
     *
     * @param tblOrder 订单
     * @return 订单
     */
    @Override
    public List<TblOrder> selectTblOrderList(TblOrder tblOrder)
    {
        return tblOrderMapper.selectTblOrderList(tblOrder);
    }

    /**
     * 查询订单列表（包括订单行项目）--接口专用
     *
     * @param tblOrder 订单
     * @return 订单集合
     */
    @Override
    public List<TblOrder> selectTblOrderFullList(TblOrder tblOrder) {
        return tblOrderMapper.selectTblOrderFullList(tblOrder);
    }

    @Override
    public List<TblOrder> selectTblOrderWithItemList(TblOrder tblOrder) {
        return tblOrderMapper.selectTblOrderWithItemList(tblOrder);
    }

    /**
     * 新增订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblOrder(TblOrder tblOrder)
    {
//        tblOrder.setCreateTime(DateUtils.getNowDate());
        int rows = tblOrderMapper.insertTblOrder(tblOrder);
        insertTblOrderItem(tblOrder);
        return rows;
    }

    /**
     * 修改订单
     *
     * @param tblOrder 订单
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblOrder(TblOrder tblOrder)
    {
        tblOrder.setUpdateTime(DateUtils.getNowDate());
        tblOrderMapper.deleteTblOrderItemByOrderId(tblOrder.getOrderId());
        updateTblOrderItem(tblOrder);
        return tblOrderMapper.updateTblOrder(tblOrder);
    }

    /**
     * 批量删除订单
     *
     * @param orderIds 需要删除的订单ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblOrderByIds(Long[] orderIds)
    {
        tblOrderMapper.deleteTblOrderItemByOrderIds(orderIds);
        return tblOrderMapper.deleteTblOrderByIds(orderIds);
    }

    /**
     * 删除订单信息
     *
     * @param orderId 订单ID
     * @return 结果
     */
    @Override
    public int deleteTblOrderById(Long orderId)
    {
        tblOrderMapper.deleteTblOrderItemByOrderId(orderId);
        return tblOrderMapper.deleteTblOrderById(orderId);
    }

    /**
     * 仅更新订单信息
     *
     * @param tblOrder 订单ID
     * @return 结果
     */
    @Override
    public int updateTblOrderOnly(TblOrder tblOrder) {
        tblOrder.setUpdateTime(DateUtils.getNowDate());
        return tblOrderMapper.updateTblOrder(tblOrder);
    }

    /**
     * 新增订单行项目信息
     *
     * @param tblOrder 订单对象
     */
    public void insertTblOrderItem(TblOrder tblOrder)
    {
        List<TblOrderItem> tblOrderItemList = tblOrder.getDetail();
        Long orderId = tblOrder.getOrderId();
        if (StringUtils.isNotNull(tblOrderItemList))
        {
            List<TblOrderItem> list = new ArrayList<TblOrderItem>();
            List<TblOrderAsnQuantity> orderAsnQuantities = new ArrayList<>();
            for (TblOrderItem tblOrderItem : tblOrderItemList)
            {
                TblOrderAsnQuantity quantity = new TblOrderAsnQuantity();
                quantity.setOrderCode(tblOrder.getOrderCode());
                quantity.setOrderLineNo(tblOrderItem.getItemNo());
                quantity.setCompCode(tblOrder.getCompCode());
                quantity.setQuantity(tblOrderItem.getQuantity());
                quantity.setUnsentQuantity(tblOrderItem.getQuantity());
                orderAsnQuantities.add(quantity);
                tblOrderItem.setOrderId(orderId);
                list.add(tblOrderItem);
            }
            if (list.size() > 0)
            {
                tblOrderMapper.batchTblOrderItem(list);
                tblOrderMapper.batchTblOrderAsnQuantity(orderAsnQuantities);
            }
        }
    }

    /*
     * 更新订单行项目信息
     *
     * @param tblOrder 订单对象
     */
    public void updateTblOrderItem(TblOrder tblOrder)
    {
        List<TblOrderItem> tblOrderItemList = tblOrder.getDetail();
        Long orderId = tblOrder.getOrderId();
        if (StringUtils.isNotNull(tblOrderItemList))
        {
            List<TblOrderItem> list = new ArrayList<TblOrderItem>();
            for (TblOrderItem tblOrderItem : tblOrderItemList)
            {
                TblOrderAsnQuantity quantity = new TblOrderAsnQuantity();
                quantity.setOrderCode(tblOrder.getOrderCode());
                quantity.setOrderLineNo(tblOrderItem.getItemNo());
                quantity.setCompCode(tblOrder.getCompCode());
                quantity.setQuantity(tblOrderItem.getQuantity());
                quantity.setUnsentQuantity(tblOrderItem.getQuantity());
                tblOrderItem.setOrderId(orderId);
                list.add(tblOrderItem);
                tblOrderMapper.updateTblOrderAsnQuantity(quantity);
            }
            if (list.size() > 0)
            {
                tblOrderMapper.batchTblOrderItem(list);
            }
        }
    }
    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId(){
        return tblOrderMapper.selectLastId();
    }

    @Override
    public List<TblOrderItem> selectTblOrderItemList(TblOrderItem orderItem) {
        return tblOrderMapper.selectTblOrderItemList(orderItem);
    }

    /**
     * 查询订单(不包含行项目)
     *
     * @param orderId 订单ID
     * @return 订单
     */
    @Override
    public TblOrder selectTblOrderOnlyById(Long orderId) {
        TblOrder param = new TblOrder();
        param.setOrderId(orderId);
        TblOrder order = tblOrderMapper.selectTblOrderOnlyById(param);
        makeRead(order);
        return order;
    }

    /**
     * 更新订单完成状态
     *
     * @param orderCode 订单编号
     * @return 结果
     */
    @Override
    public int updateOrderCompleteStatus(String orderCode) {
        return tblOrderMapper.updateOrderCompleteStatus(orderCode);
    }

    @Override
    public int updateOrderByCodeMap(Map<String, Object> map) {
        return tblOrderMapper.updateOrderByCodeMap(map);
    }

    @Override
    public AjaxResult printOrderPdf(List<Long> orderIds, String tz) {
        String jasperPath = templatePath + "xpp.jrxml";
        HashMap<String, Object> parameters = new HashMap<String, Object>();
        List<Map<String, Object>> list = new ArrayList<>();
        if(null == tz){
            tz = "GMT+9";
        }
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy/MM/dd");
        sdf.setTimeZone(timeZone);
        sdf2.setTimeZone(timeZone);
        for (Long orderId : orderIds) {
            TblOrder o = new TblOrder();
            o.setOrderId(orderId);
            TblOrder order = this.selectTblOrderById(orderId);
            List<TblOrderItem> detail = order.getDetail();
            for (TblOrderItem item : detail) {
                String productCategory = SapObjectConverter.handleProductClass(dictDataService, item.getArticleNo());
                if (StringUtils.isNotEmpty(productCategory) && !"ZOR".equals(item.getOrderType())) {
                    item.setArticleNo(item.getArticleNo().substring(0, item.getArticleNo().indexOf("+")));
                }
                // 总箱数 = 总数量 / 每箱数量，向上取整
                int packCount = item.getQuantity().divide(item.getQtyPerPack(), 0, RoundingMode.UP).intValue();
                if (packCount > 999) {
                    return AjaxResult.error(StringUtils.format(MessageUtils.message("pack.count.max"), order.getOrderCode()));
                }
                for (int i = 1; i <= packCount; i++) {
                    Map map = new HashMap<>();
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.setDateFormat(sdf2);
                    map = objectMapper.convertValue(order, Map.class);
                    map.putAll(objectMapper.convertValue(item, Map.class));
                    if (i < packCount) {
                        map.put("capacity", item.getQtyPerPack());
                    } else {
                        map.put("capacity", item.getQuantity().subtract(item.getQtyPerPack().multiply(new BigDecimal(i - 1))));
                    }
                    map.put("createTime", sdf2.format(order.getCreateTime()));
                    map.put("capacityBarcode", String.format("%08d", ((BigDecimal)map.get("capacity")).intValue()));
                    map.put("deliveryDate", sdf2.format(item.getDeliveryDate()));
                    map.put("packNo", i);
                    map.put("packNoStr", getIndex(i));
                    map.put("packCount", packCount);
                    map.put("qrCodeData", generateQRCodeString("P", order.getOrderCode(), i, "",
                            item.getArticleNo(), productCategory, ((BigDecimal)map.get("capacity")).intValue(),
                            sdf.format(item.getDeliveryDate()), item.getStockLoc(), "", order.getPlantCode(),
                            "", order.getSuppCode(), ""));
                    map.put("qrCodeData2", generateQRCodeString2(order, item, i, ((BigDecimal)map.get("capacity")).intValue()));
                    map.put("productClass", productCategory);
                    list.add(map);
                }
            }
        }
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), parameters, list);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    private static String getIndex(int i) {
        String index;
        // 当i不超过99时，index = i当i超过99时，根据A0..A9..AA..AZ..B0..B9..BA..BZ..C0..Y0...YZ的规则生成index
        if (i <= 99) {
            index = String.format("%02d", i);
        } else {
            // 修复0-9和A-Z之间不连续的问题
            char secondChar;
            int remainder = (i - 100) % 36;
            if (remainder <= 9) {
                // 0-9范围
                secondChar = (char)('0' + remainder);
            } else {
                // A-P范围 (10-25对应A-P)
                secondChar = (char)('A' + remainder - 10);
            }
            index = String.valueOf((char)('A' + (i - 100) / 36)) + String.valueOf(secondChar);
        }
        return index;
    }


    /**
     * 生成二维码字符串
     * @param labelType 标签类型
     * @param issueNo 発行NO
     * @param pageNo 页码
     * @param customerPartNo 得意先部品番号
     * @param partNo 部品番号
     * @param productCategory 製品区分
     * @param containerQty 収容数
     * @param orderDate 発注日
     * @param fromStorage From保管場所
     * @param toStorage To保管場所
     * @param fromFactory From工場
     * @param fromProcessDivision From工順区分
     * @param fromProcess From工程
     * @param toFactory To工場
     * @return 二维码字符串
     */
    public static String generateQRCodeString(String labelType, String issueNo, int pageNo, String customerPartNo, String partNo,
                                              String productCategory, int containerQty, String orderDate,
                                              String fromStorage, String toStorage, String fromFactory,
                                              String fromProcessDivision, String fromProcess, String toFactory) {

        // 発行NO + ページ分子（99形式）英数字10
        String issuePage = formatRight(issueNo, 10) + formatLeft(getIndex(pageNo), 2);

        // 得意先部品番号英数字30
        String customerPart = formatRight(customerPartNo, 30);

        // 部品番号英数字25
        String partNumber = formatRight(partNo, 25);

        // 製品区分英数字	1
        String productCate = formatRight(productCategory, 1);

        // 収容数　（数字7桁）
        String containerQuantity = formatLeft(String.valueOf(containerQty), 7);

        // 発注日（YYYYMMDD形式）
        String formattedOrderDate = formatRight(orderDate, 8);

        // From保管場所英数字7
        String fromStoragePadded = formatRight(fromStorage, 7);

        // To保管場所英数字7
        String toStoragePadded = formatRight(toStorage, 7);

        // From工場英数字3
        String fromFactoryPadded = formatRight(fromFactory, 3);

        // From工順区分英数字1
        String fromProcessDivisionPadded = formatRight(fromProcessDivision, 1);

        // From工程英数字7
        String fromProcessPadded = formatRight(fromProcess, 7);

        // To工場英数字3
        String toFactoryPadded = formatRight(toFactory, 3);

        // 生成QRコード字符串
        return PREFIX +
                labelType +
                issuePage +
                customerPart +
                partNumber +
                productCate +
                containerQuantity +
                formattedOrderDate +
                fromStoragePadded +
                toStoragePadded +
                fromFactoryPadded +
                fromProcessDivisionPadded +
                fromProcessPadded +
                toFactoryPadded +
                SUFFIX;
    }

    // 左侧补0，超出长度时截取
    public static String formatLeft(String input, int length) {
        if (input == null) {
            input = "";
        }
        if (input.length() > length) {
            return input.substring(input.length() - length);
        }
        while (input.length() < length) {
            input = "0" + input;
        }
        return input;
    }

    // 右侧补空格，超出长度时截取
    public static String formatRight(String input, int length) {
        if (input == null) {
            input = "";
        }
        if (input.length() > length) {
            return input.substring(0, length);
        }
        while (input.length() < length) {
            input = input + " ";
        }
        return input;
    }

    @Override
    public AjaxResult printInstructPdf(List<Long> orderIds, String tz) {
        String jasperPath = templatePath + "bpnrzss.jrxml";
        List<Map> list = new ArrayList<>();
        if(null == tz){
            tz = "GMT+9";
        }
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yy/MM/dd");
        sdf.setTimeZone(timeZone);
        sdf2.setTimeZone(timeZone);

        for (Long orderId : orderIds) {
            TblOrder o = new TblOrder();
            o.setOrderId(orderId);
            TblOrder order = this.selectTblOrderById(orderId);
            List<TblOrderItem> detail = order.getDetail();
            for (TblOrderItem item : detail) {
                String productClass = SapObjectConverter.handleProductClass(dictDataService, item.getArticleNo());
                if (StringUtils.isNotEmpty(productClass)) {
                    item.setArticleNo(item.getArticleNo().substring(0, item.getArticleNo().indexOf("+")));
                }
                Map map = new HashMap<>();
                ObjectMapper objectMapper = new ObjectMapper();
                map = objectMapper.convertValue(order, Map.class);
                map.putAll(objectMapper.convertValue(item, Map.class));
                map.put("createTime", DateFormatUtils.format(order.getCreateTime(), "MM/dd"));
                map.put("deliveryDate", sdf2.format(item.getDeliveryDate()));
                map.put("productClass", productClass);
                // 获取当前登录用户
                LoginUser loginUser = SecurityUtils.getLoginUser();
                // 判断是否包含没有价格权限的供应商角色
                if (permissionService.hasRole("noPriceSupplier") && !loginUser.getUser().isAdmin()) {
                    // 隐藏采购价格，将netPrice设置为null
                    map.put("price", "");
                } else {
                    map.put("price", item.getNetPrice().divide(StringUtils.isNotEmpty(item.getPriceUnit()) ? SapObjectConverter.convertSapNumber(item.getPriceUnit()) : BigDecimal.ONE, 2, RoundingMode.HALF_UP).toString());
                }
                list.add(map);
            }
        }
        Map<String, Object> header = new HashMap<>(list.get(0));
        header.put("date", sdf2.format(new Date()));
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), header, list);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public AjaxResult printPickingList(List<Long> orderIds, String tz) {
        String jasperPath = templatePath + "picking_list.jrxml";
        List<Map> list = new ArrayList<>();
        if(null == tz){
            tz = "GMT+9";
        }
        TimeZone timeZone = TimeZone.getTimeZone(tz);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yy/MM/dd");
        sdf.setTimeZone(timeZone);
        sdf2.setTimeZone(timeZone);

        int lineCode = 1;
        for (Long orderId : orderIds) {
            TblOrder o = new TblOrder();
            o.setOrderId(orderId);
            TblOrder order = this.selectTblOrderById(orderId);
            List<TblOrderItem> detail = order.getDetail();
            for (TblOrderItem item : detail) {
                Map map = new HashMap<>();
                ObjectMapper objectMapper = new ObjectMapper();
                map = objectMapper.convertValue(order, Map.class);
                map.putAll(objectMapper.convertValue(item, Map.class));
                map.put("lineNo", String.valueOf(lineCode++));
                map.put("packQty", item.getQuantity().divide(item.getQtyPerPack(), 0, RoundingMode.UP));
                map.put("deliveryDate", sdf2.format(item.getDeliveryDate()));
                list.add(map);
            }
        }
        // 如果detail不是20的整数倍，则补足
        int size = list.size();
        if (size % 20 != 0){
            for (int i = 0; i < 20 - size % 20; i++){
                Map<String, Object> dataMap = new HashMap<>();
                list.add(dataMap);
            }
        }
        Map<String, Object> header = new HashMap<>(list.get(0));
        header.put("createDate", sdf2.format(new Date()));
        String path;
        try {
            path = JasperReportUtil.exportToPdf(jasperPath, RuoYiConfig.getDownloadPath(), header, list);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
        return AjaxResult.success(path);
    }

    @Override
    public boolean checkOrderCompleteStatus(String orderCode) {
        TblOrder order = new TblOrder();
        order.setOrderCode(orderCode);
        List<TblOrder> orders = tblOrderMapper.selectTblOrderList(order);
        if (orders.isEmpty()) {
            return false;
        }
        TblOrder tblOrder = orders.get(0);
        if ("Y".equals(tblOrder.getIsComplete())) {
            return true;
        }
        return false;
    }

    /**
     * 订单确认
     *
     * @param orderIds 订单ID列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int confirmOrders(List<Long> orderIds) {
        try {
            logger.info("开始订单确认，订单ID列表: {}", orderIds.toString());

            // 1. 查询需要确认的订单数据
            List<TblOrder> orders = new ArrayList<>();
            for (Long orderId : orderIds) {
                TblOrder order = selectTblOrderById(orderId);
                if (order != null) {
                    if (!DataConstants.ORDER_STATUS_NEW.equals(order.getStatus())) {
                        logger.error("订单ID {} 的状态不是New，无法确认", orderId);
                        throw new RuntimeException("订单号 " + order.getOrderCode() + " 的状态不是New，无法确认");
                    }
                    orders.add(order);
                }
            }

            if (orders.isEmpty()) {
                logger.warn("未找到需要确认的订单数据");
                return 0;
            }

            // 2. 构建SAP接口请求数据
            List<Map<String, Object>> sapData = new ArrayList<>();
            for (TblOrder order : orders) {
                if (order.getDetail() != null && !order.getDetail().isEmpty()) {
                    for (TblOrderItem item : order.getDetail()) {
                        Map<String, Object> sapItem = new HashMap<>();
                        sapItem.put("EBELN", order.getOrderCode());
                        sapItem.put("EBELP", item.getItemNo() != null ? item.getItemNo() : "10");
                        sapData.add(sapItem);
                    }
                }
            }

            if (sapData.isEmpty()) {
                logger.warn("订单数据中没有行项目，无法确认");
                return 0;
            }

            // 3. 构建SAP请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("DATA", sapData);

            // 4. 获取SAP订单确认接口URL
            String sapUrl = configService.selectConfigByKey(DataConstants.SAP_ORDER_CONFIRM_URL);
            if (StringUtils.isEmpty(sapUrl)) {
                throw new RuntimeException("SAP订单确认接口URL未配置");
            }

            // 5. 调用SAP接口
            logger.info("调用SAP订单确认接口，URL: {}, 数据: {}", sapUrl, requestBody);
            HashMap<String, Object> sapResult = sapApiClient.callSapApi(sapUrl, requestBody);

            // 6. 检查SAP接口返回结果
            if (sapResult == null) {
                throw new RuntimeException("SAP接口返回结果为空");
            }

            String mstyp = (String) sapResult.get("MSTYP");
            String msg = (String) sapResult.get("MSG");

            logger.info("SAP接口返回结果: MSTYP={}, MSG={}", mstyp, msg);

            // 7. 判断SAP接口是否成功
            if (!"S".equals(mstyp)) {
                throw new RuntimeException("SAP订单确认失败: " + msg);
            }

            // 8. SAP接口成功，更新数据库状态
            int result = tblOrderMapper.batchUpdateOrderStatus(orderIds, DataConstants.ORDER_STATUS_CONFIRMED);
            logger.info("订单确认完成，更新了 {} 条记录", result);

            return result;

        } catch (Exception e) {
            logger.error("订单确认失败: {}", e.getMessage(), e);
            throw new RuntimeException("订单确认失败: " + e.getMessage(), e);
        }
    }

    @Override
    public AjaxResult printOrderTxt(List<Long> orderIds, String tz) {
        Charset.availableCharsets().forEach((name, charset) -> System.out.println(name));
        List<String> lines = new ArrayList<>();
        TimeZone timeZone = TimeZone.getTimeZone(null==tz?"GMT+9":tz);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setTimeZone(timeZone);
        for (Long orderId : orderIds) {
            TblOrder o = new TblOrder();
            o.setOrderId(orderId);
            TblOrder order = this.selectTblOrderById(orderId);
            List<TblOrderItem> detail = order.getDetail();
            for (TblOrderItem item : detail) {
                StringBuffer sb = new StringBuffer();
                //根据PO的纳入方式字段（首字母）判断：
                //M: 12
                //W or R: 13
                //D 及空白、其他: 14
                String rcvType = item.getRcvType();
                if (StringUtils.isNotEmpty(rcvType)) {
                    rcvType = rcvType.substring(0, 1);
                }
                if ("M".equalsIgnoreCase(rcvType)) {
                    sb.append("12");//Ｆ／＃
                } else if ("W".equalsIgnoreCase(rcvType) || "R".equalsIgnoreCase(rcvType)) {
                    sb.append("13");//Ｆ／＃
                } else {
                    sb.append("14");//Ｆ／＃
                }
                sb.append(Util.fillAfter(order.getCompCode(), 4, ' '));//発注会社
                sb.append(Util.fillAfter(order.getSuppCode(), 5, ' '));//取引先
                sb.append(Util.fillAfter(item.getDepot(), 2, ' '));//デポ
                sb.append(Util.fillAfter(order.getPlantCode(), 4, ' '));//要求工場
                sb.append(Util.fillAfter(order.getOrderCode(), 10, ' '));//発行№
                String productClass = SapObjectConverter.handleProductClass(dictDataService, item.getArticleNo());
                if (StringUtils.isNotEmpty(productClass)) {
                    item.setArticleNo(item.getArticleNo().substring(0, item.getArticleNo().indexOf("+")));
                }
                sb.append(Util.fillAfter(item.getArticleNo(), 25, ' '));//部品番号
                sb.append(Util.fillAfter(productClass, 1, ' '));//製品区分
                sb.append(Util.fillAfter(item.getArticleName(), 25, ' ', Charset.forName("MS932")));//部品名称
                sb.append(Util.fillAfter(item.getUnloadingNo(), 7, ' '));//納入場所
                sb.append(Util.fillAfter(sdf.format(null != item.getDeliveryDate()?item.getDeliveryDate():""), 8, ' '));//納入指示年月日
                sb.append("    ");//納入指示時分
                sb.append(Util.fillBefore(item.getQuantity().setScale(0, RoundingMode.UNNECESSARY).toString(), 8, '0'));//納入指示数量
                sb.append(Util.fillAfter(item.getUnit(), 3, ' '));//基本数量単位
                sb.append("   ");//価格契約単位
                sb.append(' ');//単位条件
                sb.append("000000000");//換算係数
                sb.append(Util.fillAfter(item.getProductType(),1,' '));//生産方式
                sb.append(Util.fillAfter(item.getRcvType(), 2, ' '));//納入方式
                sb.append(Util.fillAfter(item.getPurDocType(), 1, ' '));//発注区分
                sb.append(' ');//限定理由
                sb.append(' ');//発行理由
                sb.append("   ");//計画担当者
                sb.append(Util.fillAfter(item.getStockLoc(), 3, ' '));//在庫担当者
                sb.append(Util.fillAfter(item.getSecure(),1, ' '));//重要保安区分
                sb.append("000");//受入許容範囲
                sb.append(Util.fillAfter(item.getStockLoc(), 7, ' '));//ＦＲＯＭ保管場所
                sb.append("       ");//ＴＯ保管場所
                sb.append(Util.fillAfter(item.getLocAdd(), 7, ' '));//ＬＯＣＡＴＩＯＮ
                sb.append(Util.fillBefore(null != item.getQtyPerPack()?item.getQtyPerPack().setScale(0, RoundingMode.UNNECESSARY).toString():"", 7, '0'));//ＳＮＥＰ
                sb.append(Util.fillAfter(item.getWorkbinNo(), 8, ' '));//荷姿コード
                sb.append(Util.fillAfter(item.getPurchaseType(), 1, ' '));//自己調達区分
                sb.append("     ");//受給先コード
                sb.append("  ");//受給先要元
                sb.append(Util.fillAfter(order.getCustomerCode(), 5, ' '));//得意先コード
                sb.append(Util.fillAfter(order.getRequester(), 2, ' '));//要元
                sb.append(Util.fillAfter(item.getCustomerOrderCode(), 30, ' '));//得意先部番
                sb.append(Util.fillAfter(null!=item.getCustomerDeliveryDate()?sdf.format(item.getCustomerDeliveryDate()):"", 8, ' ' ));//得意先納入指示年月日
                sb.append("    ");//得意先納入指示時分
                sb.append(Util.fillAfter(item.getArticleType(), 12, ' '));//部品番号識別－１
                sb.append(' ');//現品票ラベル発行フラグ
                sb.append(' ');//遅防法シンボル
                // 获取当前登录用户
                LoginUser loginUser = SecurityUtils.getLoginUser();
                // 判断是否包含没有价格权限的供应商角色
                if (permissionService.hasRole("noPriceSupplier") && !loginUser.getUser().isAdmin()) {
                    // 隐藏采购价格，将netPrice设置为""
                    sb.append(Util.fillBefore("", 11, '0'));
                } else {
                    sb.append(Util.fillBefore(null != item.getNetPrice()?item.getNetPrice().divide(StringUtils.isNotEmpty(item.getPriceUnit()) ? SapObjectConverter.convertSapNumber(item.getPriceUnit()) : BigDecimal.ONE, 2, RoundingMode.HALF_UP).toString().replace(".",""):"", 11, '0'));//単価
                }
                sb.append("          ");//設通№
                sb.append(Util.fillAfter(item.getRemark(), 12, ' '));//備考
                sb.append(Util.fillAfter(sdf.format(order.getCreateTime()), 8, ' '));//発注年月日
                sb.append(Util.fillAfter(order.getCompName(), 25, ' '));//発注者名称
                sb.append(Util.fillAfter(order.getSuppName(), 25, ' ', Charset.forName("MS932")));//受注者名称
                sb.append(Util.fillAfter(item.getUnloadingName(), 12, ' '));//納入先名称
                sb.append(Util.fillAfter(order.getSuppCode(), 5, ' ' ));//取引先
                sb.append(Util.fillAfter(item.getDepot(), 2, ' '));//デポ
                lines.add(sb.toString());
            }
        }
        String fileName = "HM03_" + UUID.randomUUID() + ".txt";
        try{
            FileUtils.writeLines(new File(RuoYiConfig.getDownloadPath() + fileName), "MS932",lines);
            return AjaxResult.success(fileName);
        }catch(Exception e){
            return AjaxResult.error(e.getMessage());
        }

    }

    /**
     * 查询flatOrder列表
     *
     * @param flatOrder flatOrder
     * @param tz
     * @return flatOrder
     */
    @Override
    public List<FlatOrder> selectFlatOrderList(FlatOrder flatOrder, String tz)
    {
        TimeZone timeZone = TimeZone.getTimeZone(null == tz ? "GMT+9" : tz);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        sdf.setTimeZone(timeZone);
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的备注（即配置的可以查看的depot）
            String depot = loginUser.getUser().getRemark();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            flatOrder.setDepot(depot);
        }
        if (!loginUser.getUser().isAdmin()) {
            // 试制工厂（223X）员工只能看到试制工厂的订单
            if (permissionService.hasRole("223Xuser")) {
                // 设置查询条件 - 只能查看试制工厂的数据
                flatOrder.setIs223XUser("Y");
            } else {
                flatOrder.setIs223XUser("N");
            }
        }
        // 查询条件中的时间需要转换为 UTC
        convertSearchTimeToUTC(flatOrder, tz);
        List<FlatOrder> flatOrders = tblOrderMapper.selectFlatOrderList(flatOrder);
        flatOrders.forEach(flatOrder1 -> {
            if (flatOrder1.getCreateTime() != null) flatOrder1.setCreateTime(DateUtils.parseDate(sdf.format(flatOrder1.getCreateTime())));
            if (flatOrder1.getSapUpdateTime() != null) flatOrder1.setSapUpdateTime(DateUtils.parseDate(sdf.format(flatOrder1.getSapUpdateTime())));
            if (flatOrder1.getReceiveTime() != null) flatOrder1.setReceiveTime(DateUtils.parseDate(sdf.format(flatOrder1.getReceiveTime())));
            // 根据字典配置获取plantCode对应的plantName
//            flatOrder1.setPlantName(dictDataService.selectDictLabel("plant", flatOrder1.getPlantCode()) );
        });
        return flatOrders;
    }

    private void convertSearchTimeToUTC(FlatOrder flatOrder, String tz) {
        if (flatOrder == null || flatOrder.getParams() == null) return;
        if (tz == null) tz = "Asia/Tokyo";

        Map<String, Object> params = flatOrder.getParams();
        ZoneId userZone = ZoneId.of(tz);
        // 转换创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        if (params.containsKey("createTimeBegin")) {
            String createTimeBegin = (String) params.get("createTimeBegin");
            LocalDateTime localDateTime = LocalDateTime.parse(createTimeBegin, formatter);
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            params.put("createTimeBegin", utcZonedDateTime.format(formatter));
        }
        if (params.containsKey("createTimeEnd")) {
            String createTimeEnd = (String) params.get("createTimeEnd");
            LocalDateTime localDateTime = LocalDateTime.parse(createTimeEnd, formatter);
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            params.put("createTimeEnd", utcZonedDateTime.format(formatter));
        }
        if (params.containsKey("sapUpdateTimeBegin")) {
            String sapUpdateTimeBegin = (String) params.get("sapUpdateTimeBegin");
            LocalDateTime localDateTime = LocalDateTime.parse(sapUpdateTimeBegin, formatter);
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            params.put("sapUpdateTimeBegin", utcZonedDateTime.format(formatter));
        }
        if (params.containsKey("sapUpdateTimeEnd")) {
            String sapUpdateTimeEnd = (String) params.get("sapUpdateTimeEnd");
            LocalDateTime localDateTime = LocalDateTime.parse(sapUpdateTimeEnd, formatter);
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            params.put("sapUpdateTimeEnd", utcZonedDateTime.format(formatter));
        }
        if (params.containsKey("receiveTimeBegin")) {
            String receiveTimeBegin = (String) params.get("receiveTimeBegin");
            LocalDateTime localDateTime = LocalDateTime.parse(receiveTimeBegin, formatter);
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            params.put("receiveTimeBegin", utcZonedDateTime.format(formatter));
        }
        if (params.containsKey("receiveTimeEnd")) {
            String receiveTimeEnd = (String) params.get("receiveTimeEnd");
            LocalDateTime localDateTime = LocalDateTime.parse(receiveTimeEnd, formatter);
            ZonedDateTime utcZonedDateTime = localDateTime.atZone(userZone).withZoneSameInstant(ZoneOffset.UTC);
            params.put("receiveTimeEnd", utcZonedDateTime.format(formatter));
        }
    }

    /**
     * 生成二维码数据2
     */
    private String generateQRCodeString2(TblOrder order, TblOrderItem item, int pageNo, int containerQty) {
        StringBuilder sb = new StringBuilder();

        // ① 固定值 "!GP?"
        sb.append(PREFIX);

        // ② 固定值 "E"
        sb.append("E");

        // ③ 得意先部品番号(25桁)
        sb.append(formatRight(item.getCustomerArticleNo(), 25));

        // ④ 収容数(前ゼロ無し左詰め)
        sb.append(String.format("%-6d", containerQty));

        // ⑤ P/O №(20桁)
        sb.append(formatRight(item.getCustomerPoNo(), 20));

        // ⑥ RAN №(9桁)
        sb.append(formatRight(item.getRankNo(), 9));

        // ⑦ Serial No.(10桁): 発行№(8桁) + ページ分子(2桁)
        String issueNo = order.getOrderCode();
        if (issueNo.length() > 8) {
            issueNo = issueNo.substring(issueNo.length() - 8);
        }
        sb.append(formatRight(issueNo, 8));
        sb.append(getIndex(pageNo));

        // ⑧ Supplier Part No.(25桁)
        sb.append(formatRight(item.getArticleNo(), 25));

        // ⑨ Supplier Code(20桁)
        sb.append(formatRight(order.getSuppCode(), 20));

        // ⑩ Issue Date(8桁) YYYYMMDD
        String issueDate = DateFormatUtils.format(item.getDeliveryDate(), "yyyyMMdd");
        sb.append(formatRight(issueDate, 8));

        // ⑪ Free Space for Shippers
        // ⑪-1 得意先コード(5桁)
        sb.append(formatRight(order.getCustomerCode(), 5));

        // ⑪-2 From工場コード(3桁)
        sb.append(formatRight(order.getPlantCode(), 4));

        // ⑪-3 インボイスＮＯ+インボイス枝番(14桁)
        sb.append(formatRight("", 14));  // 暂时为空

        // ⑪-4 受注Order №(10桁)
        sb.append(formatRight(item.getCustomerOrderCode(), 10));

        // ⑪-5 Pallet №(4桁)
        sb.append(formatRight("", 4));  // 暂时为空

        // ⑪-5 要求数量(7桁)
        sb.append(String.format("%-7d", item.getQuantity().intValue()));

        // ⑪-6 発行№&ページ分子(12桁)
        sb.append(formatRight(order.getOrderCode(), 10));
        sb.append(String.format("%02d", pageNo));

//        // ⑪-7 From工場コード(3桁)
//        sb.append(formatRight(order.getPlantCode(), 3));

        // ⑪-8 From工程(7桁)
        sb.append(formatRight(item.getWorkbinNo(), 7));

        // ⑪-9 ラベル発行区分(1桁)
        sb.append("S");

        // ⑪-10 空白(3桁)
        sb.append("   ");

        // ⑫ 固定値 "!END?"
        sb.append(SUFFIX);

        return sb.toString();
    }

    @Override
    public Map<String, Boolean> checkOrderStatus(String orderCode) {
        Map<String, Boolean> result = new HashMap<>();
        
        TblOrder order = new TblOrder();
        order.setOrderCode(orderCode);
        List<TblOrder> orders = tblOrderMapper.selectTblOrderList(order);
        
        if (orders.isEmpty()) {
            result.put("isComplete", false);
            result.put("isConfirmed", false);
        } else {
            TblOrder tblOrder = orders.get(0);
            result.put("isComplete", "Y".equals(tblOrder.getIsComplete()));
            result.put("isConfirmed", DataConstants.ORDER_STATUS_CONFIRMED.equals(tblOrder.getStatus()));
        }
        
        return result;
    }
}
