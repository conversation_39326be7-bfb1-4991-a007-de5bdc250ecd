<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="公告标题" prop="noticeTitle">
        <el-input
          v-model="queryParams.noticeTitle"
          placeholder="请输入公告标题"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择公告状态" clearable size="small">
          <el-option
            v-for="dict in statusOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker
          v-model="daterangePublishTime"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['datamanage:notice:add']"
        >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['datamanage:notice:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="danger"-->
<!--          plain-->
<!--          icon="el-icon-delete"-->
<!--          size="mini"-->
<!--          :disabled="multiple"-->
<!--          @click="handleDelete"-->
<!--          v-hasPermi="['datamanage:notice:remove']"-->
<!--        >删除</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
		  :loading="exportLoading"
          @click="handleExport"
          v-hasPermi="['datamanage:notice:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公告标题" align="center" prop="noticeTitle" />
      <el-table-column label="公告状态" align="center" prop="status" >
        <template slot-scope="scope">
          <span>{{statusFormat(scope.row)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="需要回复" align="center" prop="noticeTitle" >
        <template slot-scope="scope">
          <span>{{needReplyFormat(scope.row)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="发布时间" align="center" prop="publishTime" width="180">
<!--        <template slot-scope="scope">-->
<!--          <span>{{ parseTime(scope.row.publishTime, '{y}-{m}-{d}') }}</span>-->
<!--        </template>-->
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['datamanage:notice:edit']"
            v-if="scope.row.status==='0'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleView(scope.row)"
            v-hasPermi="['datamanage:notice:list']"
            v-if="scope.row.status==='1'"
          >查看公告</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleVieReplyList(scope.row)"
            v-hasPermi="['datamanage:notice:list']"
            v-if="scope.row.status==='1'"
          >查看回复</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['datamanage:notice:remove']"
            v-if="scope.row.status==='0'"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公告标题" prop="noticeTitle">
          <el-input v-model="form.noticeTitle" placeholder="请输入公告标题" :disabled="'1'===form.status"/>
        </el-form-item>
        <el-form-item label="公告对象" prop="tblNoticeReplyList">
          <el-select v-model="tblNoticeReplyList" placeholder="公告对象" clearable style="width: 100%" multiple filterable @change='changeSelect' @remove-tag='removeTag' value-key="suppCode" :disabled="'1'===form.status">
            <el-option label='全选' value='全选' @click.native='selectAll'></el-option>
            <el-option
              v-for="dict in supplierList"
              :key="dict.suppCode"
              :label="dict.suppName"
              :value="dict"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公告内容">
          <editor v-model="form.noticeContent" :min-height="192" :read-only="'1'===form.status" />
        </el-form-item>
        <el-form-item label="需要回复">
          <el-switch v-model="form.needReply"  :disabled="'1'===form.status" active-value="Y" inactive-value="N"/>
        </el-form-item>
        <el-form-item label="附件">
          <attachment-upload v-model="form.tempAttachmentList" :readonly="'1'===form.status" ref="attach"/>
        </el-form-item>
<!--        <el-form-item label="公告状态">-->
<!--          <el-radio-group v-model="form.status">-->
<!--            <el-radio-->
<!--              v-for="dict in statusOptions"-->
<!--              :key="dict.dictValue"-->
<!--              :label="dict.dictValue"-->
<!--            >{{dict.dictLabel}}</el-radio>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="公司代码" prop="compCode">-->
<!--          <el-input v-model="form.compCode" placeholder="请输入公司代码" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="工厂" prop="plantCode">-->
<!--          <el-input v-model="form.plantCode" placeholder="请输入工厂" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="工厂名称" prop="plantName">-->
<!--          <el-input v-model="form.plantName" placeholder="请输入工厂名称" />-->
<!--        </el-form-item>-->
<!--        <el-divider content-position="center">公告回复信息</el-divider>-->
<!--        <el-row :gutter="10" class="mb8">-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddTblNoticeReply">添加</el-button>-->
<!--          </el-col>-->
<!--          <el-col :span="1.5">-->
<!--            <el-button type="danger" icon="el-icon-delete" size="mini" @click="handleDeleteTblNoticeReply">删除</el-button>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-table :data="tblNoticeReplyList" :row-class-name="rowTblNoticeReplyIndex" @selection-change="handleTblNoticeReplySelectionChange" ref="tblNoticeReply">-->
<!--          <el-table-column type="selection" width="50" align="center" />-->
<!--          <el-table-column label="序号" align="center" prop="index" width="50"/>-->
<!--          <el-table-column label="供应商" prop="suppCode">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.suppCode" placeholder="请输入供应商" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="供应商名称" prop="suppName">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.suppName" placeholder="请输入供应商名称" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="创建时间" prop="createTime">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.createTime" placeholder="请输入创建时间" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="创建者" prop="createBy">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.createBy" placeholder="请输入创建者" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="更新时间" prop="updateTime">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.updateTime" placeholder="请输入更新时间" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="更新者" prop="updateBy">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.updateBy" placeholder="请输入更新者" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="收发标志" prop="direction">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.direction" placeholder="请输入收发标志" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--          <el-table-column label="KafKa发送状态" prop="kafkaStatus">-->
<!--            <template slot-scope="scope">-->
<!--              <el-input v-model="scope.row.kafkaStatus" placeholder="请输入KafKa发送状态" />-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :disabled="buttonDisabled" @click="submitForm(false)" v-if="'0'===form.status">保 存</el-button>
        <el-button type="primary" :disabled="buttonDisabled" @click="submitForm(true)" v-if="'0'===form.status">发 布</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="查看回复" :visible.sync="replyOpen" width="800px" append-to-body>

      <el-form :model="replyForm.queryParams" ref="replyForm" :inline="true" v-show="replyForm.showSearch" label-width="68px">
        <el-form-item label="公告对象" prop="suppCode">
          <el-select v-model="replyForm.queryParams.suppCode" placeholder="公告对象" clearable filterable >
            <el-option
              v-for="dict in supplierList"
              :key="dict.suppCode"
              :label="dict.suppName"
              :value="dict.suppCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="回复时间">
          <el-date-picker
            v-model="replyForm.daterangeReplyTime"
            size="small"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="是否已阅" prop="isRead">
          <el-select
            v-model="replyForm.queryParams.isRead"
            placeholder="请选择"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in yesNoOption"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否回复" prop="isReply">
          <el-select v-model="replyForm.queryParams.params.isReply" placeholder="是否回复" clearable size="small">
            <el-option
              v-for="dict in yesNoOption"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleReplyQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetReplyQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">

<!--        <el-col :span="1.5">-->
<!--          <el-button-->
<!--            type="warning"-->
<!--            plain-->
<!--            icon="el-icon-download"-->
<!--            size="mini"-->
<!--            :loading="exportLoading"-->
<!--            @click="handleExport"-->
<!--            v-hasPermi="['datamanage:reply:export']"-->
<!--          >导出</el-button>-->
<!--        </el-col>-->
        <right-toolbar :showSearch.sync="replyForm.showSearch" @queryTable="getReplyList"></right-toolbar>
      </el-row>

      <el-table v-loading="replyForm.loading" :data="replyForm.replyList">
        <el-table-column type="expand">
          <template slot-scope="scope">
            <span>{{ scope.row.content}}</span>
            <li :key="file.uid" v-for="(file, index) in scope.row.replytempAttachmentList">
              <el-link :href="file.url" :underline="false" target="_blank" :download="file.name">
                <span class="el-icon-document"> {{ file.name }} </span>
              </el-link>
              <!--        <div class="ele-upload-list__item-content-action">-->
              <!--          <el-link :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>-->
              <!--        </div>-->
            </li>
          </template>
        </el-table-column>
        <el-table-column label="供应商编号" align="center" prop="suppCode" />
        <el-table-column label="供应商名称" align="center" prop="suppName" />
        <el-table-column prop="isRead" label="是否已阅" :formatter="isReadFormat" align="center"></el-table-column>
        <el-table-column label="回复内容" align="center" prop="content" show-overflow-tooltip/>
        <el-table-column label="回复时间" align="center" prop="replyTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.replyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="replyForm.total>0"
        :total="replyForm.total"
        :page.sync="replyForm.queryParams.pageNum"
        :limit.sync="replyForm.queryParams.pageSize"
        @pagination="getReplyList"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  addNotice,
  delNotice,
  exportNotice,
  getNotice,
  listNotice,
  listReply,
  updateNotice
} from "@/api/datamanage/notice";
import Editor from '@/components/Editor';
import {listConfig} from "@/api/system/config";
import AttachmentUpload from "@/components/AttachmentUpload/index.vue";
import {listAttachment} from "@/api/datamanage/attachment";

// const validateReplyList = (rule, value, callback)=>{
//   console.log(rule);
//   console.log(value);
//   if (!value || value.length < 1){
//     callback(new Error('请至少选择一个公告对象'));
//   }else{
//     callback();
//   }
// }
export default {
  name: "TblNotice",
  components: {
    Editor,
    AttachmentUpload,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTblNoticeReply: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 公告回复表格数据
      tblNoticeReplyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      replyOpen:false,
      // 公告状态字典
      statusOptions: [],
      // 创建时间时间范围
      daterangeCreateTime: [],
      daterangePublishTime:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: null,
        status: null,
        createTime: null,
        publishTime:null,
        direction:'O',
        orderByColumn: 'createTime',
        isAsc:'desc',
      },
      supplierList:[],
      // 表单参数
      form: {},
      buttonDisabled: false,
      // 表单校验
      rules: {
        noticeTitle: [
          { required: true, message: "公告标题不能为空", trigger: "blur" }
        ],
        noticeContent: [
          { required: true, message: "公告内容不能为空", trigger: "blur" }
        ],
        tblNoticeReplyList: [
          { type: 'array', required: true, message: "公告对象不能为空", min: 1,  trigger: "blur" }
        ],
      },
      yesNoOption:[],
      replyForm:{
        loading:true,
        daterangeReplyTime:[],
        replyList:[],
        total:0,
        showSearch:true,
        queryParams:{
          pageNum: 1,
          pageSize: 10,
          noticeId:null,
          kafkaStatus:null,
          suppCode:null,
          replyTime:null,
          isRead: null,
          params:{isReply:null},
          orderByColumn:'replyTime',
          isAsc:'desc',
        }
      },
      uploadUrl:''
    };
  },
  created() {
    this.getList();
    this.getSupplierList();
    this.getDicts("notice_status").then(response => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_yes_no").then(response=>{
      this.yesNoOption = response.data;
    });
    this.uploadUrl = process.env.VUE_APP_BASE_API + "/common/upload";
  },
  methods: {
    isReadFormat(row, column) {
      return this.selectDictLabel(this.yesNoOption, row.isRead);
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangePublishTime && '' != this.daterangePublishTime) {
        this.queryParams.params["beginPublishTime"] = this.daterangePublishTime[0];
        this.queryParams.params["endPublishTime"] = this.daterangePublishTime[1];
      }
      listNotice(this.queryParams).then(response => {
        this.noticeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getReplyList(){
      this.replyForm.loading = true;
      if (null != this.replyForm.daterangeReplyTime && '' != this.replyForm.daterangeReplyTime) {
        this.replyForm.queryParams.params["beginReplyTime"] = this.replyForm.daterangeReplyTime[0];
        this.replyForm.queryParams.params["endReplyTime"] = this.replyForm.daterangeReplyTime[1];
      }
      listReply(this.replyForm.queryParams).then(response=>{
        this.replyForm.replyList = response.rows;
        this.replyForm.total = response.total;
        this.replyForm.loading = false;
      });
    },
    handleVieReplyList(row){
      this.replyForm = {
        loading:true,
        daterangeReplyTime:[],
        replyList:[],
        total:0,
        showSearch:true,
        queryParams:{
          pageNum: 1,
          pageSize: 10,
          noticeId:row.noticeId,
          kafkaStatus:null,
          suppCode:null,
          replyTime:null,
          params:{isReply:null},
          orderByColumn:'replyTime',
          isAsc:'desc',
        }
      };
      this.replyOpen = true;
      this.getReplyList();
    },
    getSupplierList(){
      listConfig({configKey:'supp.'}).then(response=>{
        this.supplierList = response.rows.map(config=>{return {suppName:config.configName, suppCode:config.configKey.replace('supp.',''), direction:'O',kafkaStatus:'0'}});
      });
    },
    // 公告状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    needReplyFormat(row, column) {
      return this.selectDictLabel(this.yesNoOption, row.needReply);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: null,
        noticeTitle: null,
        noticeType: null,
        noticeContent: null,
        status: "0",
        compCode: null,
        plantCode: null,
        plantName: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        needReply: 'N',
        direction: 'O',
        tblNoticeReplyList: [],
        tempAttachmentList:[]
      };
      if(this.$refs.attach){
        this.$refs.attach.fileList = [];
      }

      this.tblNoticeReplyList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleReplyQuery() {
      this.replyForm.queryParams.pageNum = 1;
      this.getReplyList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetReplyQuery() {
      this.replyForm.daterangeReplyTime = [];
      this.replyForm.queryParams.params = {isReply: null};
      this.resetForm("replyForm");
      this.handleReplyQuery();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangePublishTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noticeId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加公告";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noticeId = row.noticeId || this.ids
      getNotice(noticeId).then(response => {
        this.form = response.data;
        this.tblNoticeReplyList = response.data.tblNoticeReplyList;
        if(this.tblNoticeReplyList.length === this.supplierList.length){
          this.tblNoticeReplyList.unshift('全选');
        }
        listAttachment({type:'Notice',parent:noticeId}).then(response=>{
          this.form.tempAttachmentList = response.rows;
          if(this.$refs.attach) {
            this.$refs.attach.fileList = response.rows;
          }
          this.open = true;
          this.title = "修改公告";
        });

      });
    },
    /** 修改按钮操作 */
    handleView(row) {
      this.reset();
      const noticeId = row.noticeId || this.ids
      getNotice(noticeId).then(response => {
        this.form = response.data;
        this.tblNoticeReplyList = response.data.tblNoticeReplyList;
        if(this.tblNoticeReplyList.length === this.supplierList.length){
          this.tblNoticeReplyList.unshift('全选');
        }
        listAttachment({type:'Notice',parent:noticeId}).then(response=>{
          this.form.tempAttachmentList = response.rows;

        });
        this.open = true;
        this.title = "查看公告";

      });
    },
    /** 提交按钮 */
    submitForm(isPublish) {
      this.buttonDisabled = true;
      this.form.tblNoticeReplyList = this.tblNoticeReplyList.filter((item)=>{
        return item !== '全选'
      });
      if(isPublish){
        this.form.status='1';
        this.form.tblNoticeReplyList.map(reply=>{reply.kafkaStatus = '1';return reply;});
        this.form.publishTime = this.parseTime(new Date(),'{y}-{m}-{d} {h}:{i}:{s}');
      }else{
        this.form.status = '0';
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          // for (let attach of this.form.tempAttachmentList){
          //   attach.url = attach.url.replace(process.env.VUE_APP_BASE_API,'');
          // }
          if(isPublish){
            this.$confirm("发布后无法修改，请确认","警告", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            }).then(()=> {
              // for (let attach of this.form.tempAttachmentList){
              //   attach.url = attach.url.replace(process.env.VUE_APP_BASE_API,'');
              // }
              this.addOrUpdate(true);
            });
          }else{
            this.addOrUpdate(false);
          }
        }
        this.buttonDisabled = false;
      });
    },
    addOrUpdate(isPublish){
      this.form.attachmentList=[];
      for (let attach of this.form.tempAttachmentList){
        this.form.attachmentList.push({name:attach.name, url: attach.url.replace(process.env.VUE_APP_BASE_API,'')});
        // attach.url = attach.url.replace(process.env.VUE_APP_BASE_API,'');
      }
      // console.log(this.form.tempAttachmentList);
      if (this.form.noticeId != null) {
        updateNotice(this.form).then(response => {
          this.msgSuccess(isPublish?"发布成功":"保存成功");
          this.open = false;
          this.getList();
        });
      } else {
        addNotice(this.form).then(response => {
          this.msgSuccess(isPublish?"发布成功":"保存成功");
          this.open = false;
          this.getList();
        });
      }
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const noticeIds = row.noticeId || this.ids;
      this.$confirm('是否确认删除公告编号为"' + noticeIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delNotice(noticeIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有公告数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportNotice(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        })
    },
    selectAll(){
      if (this.tblNoticeReplyList.length<this.supplierList.length){
        this.tblNoticeReplyList = []
        this.supplierList.map((item)=> {
          this.tblNoticeReplyList.push(item);
        })
        this.tblNoticeReplyList.unshift('全选')

      }else {
        this.tblNoticeReplyList=[]
      }
    },
    changeSelect(val){
      if (!val.includes('全选') && val.length === this.supplierList.length){
        this.tblNoticeReplyList.unshift('全选')
      }
      else if(val.includes('全选') && (val.length-1) < this.supplierList.length){
        this.tblNoticeReplyList = this.tblNoticeReplyList.filter((item)=>{
          return item !== '全选'
        })
      }

    },
    removeTag(val){
      if(val==='全选'){
        this.tblNoticeReplyList=[]
      }
    },
  }
};
</script>
