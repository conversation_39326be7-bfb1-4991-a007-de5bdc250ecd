<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblFeedbackMapper">

    <resultMap type="TblFeedback" id="TblFeedbackResult">
        <result property="feedId"    column="Feed_ID"    />
        <result property="dnNo"    column="Dn_No"    />
        <result property="delFlag"    column="Del_Flag"    />
        <result property="compCode"    column="Comp_Code"    />
        <result property="compName"    column="Comp_Name"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="plantName"    column="Plant_Name"    />
        <result property="suppCode"    column="Supp_Code"    />
        <result property="suppName"    column="Supp_Name"    />
        <result property="docDate"    column="Doc_Date"    />
        <result property="totalAmount"    column="Total_Amount"    />
        <result property="currency"    column="Currency"    />
        <result property="settlementNo"    column="Settlement_No"    />
        <result property="invoiceDate"    column="Invoice_Date"    />
        <result property="invoiceAmount"    column="Invoice_Amount"    />
        <result property="invoiceTax"    column="Invoice_Tax"    />
        <result property="invoiceNo"    column="Invoice_No"    />
        <result property="depot"    column="depot"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="direction"    column="Direction"    />
        <result property="kafkaStatus"    column="Kafka_Status"    />
        <result property="status"    column="Status"    />
    </resultMap>

    <resultMap id="TblFeedbackTblFeedbackItemResult" type="TblFeedback" extends="TblFeedbackResult">
        <result column="depot" property="depot"/>
        <result column="receiving_place" property="receivingPlace"/>
        <result column="receiving_date" property="receivingDate"/>
        <result column="receiving_quantity" property="receivingQuantity"/>
        <result column="order_unit" property="orderUnit"/>
        <result column="delivery_note_date" property="deliveryNoteDate"/>
        <result column="delivery_note_time" property="deliveryNoteTime"/>
        <collection property="detail" notNullColumn="sub_Item_ID" javaType="java.util.List" resultMap="TblFeedbackItemResult" />
    </resultMap>

    <resultMap id="TblFeedbackTblFeedbackItemApiResult" type="TblApiFeedback" extends="TblFeedbackResult">
        <collection property="detail" ofType="com.datalink.api.domain.TblApiFeedbackItem" select="selectTblApiFeedbackItemByFeedId" column="Feed_ID"/>
    </resultMap>

    <resultMap type="TblFeedbackItem" id="TblFeedbackItemResult">
        <result property="itemId"    column="sub_Item_ID"    />
        <result property="seqNo"    column="sub_Seq_No"    />
        <result property="plantCode"    column="sub_Plant_Code"    />
        <result property="orderCode"    column="sub_Order_Code"    />
        <result property="orderLineNo"    column="sub_Item_No"    />
        <result property="articleNo"    column="sub_Article_No"    />
        <result property="articleName"    column="sub_Article_Name"    />
        <result property="rcvDate"    column="sub_Rcv_Date"    />
        <result property="rcvTime"    column="sub_Rcv_Time"    />
        <result property="quantity"    column="sub_Quantity"    />
        <result property="unit"    column="sub_Unit"    />
        <result property="itemAmount"    column="sub_Item_Amount"    />
        <result property="taxCode"    column="sub_Tax_Code"    />
        <result property="rcvDocNo"    column="sub_Rcv_Doc_No"    />
        <result property="articleDocAnnual"    column="sub_Article_Doc_Annual"    />
        <result property="rcvDocItemNo"    column="sub_Rcv_Doc_Item_No"    />
        <result property="feedId"    column="sub_Feed_ID"    />
        <result property="createTime"    column="sub_Create_Time"    />
        <result property="createBy"    column="sub_Create_By"    />
        <result property="updateTime"    column="sub_Update_Time"    />
        <result property="updateBy"    column="sub_Update_By"    />
    </resultMap>

    <resultMap id="TblApiFeedbackItemResult" type="TblApiFeedbackItem" extends="TblFeedbackItemResult">
    </resultMap>

    <sql id="selectTblFeedbackVo">
        select Feed_ID, Dn_No, Del_Flag, Comp_Code, Comp_Name, Plant_Code, Plant_Name, Supp_Code, Supp_Name, Doc_Date, Total_Amount, Currency, Settlement_No, Invoice_Date, Invoice_Amount, Invoice_Tax, Invoice_No, depot, Create_Time, Create_By, Update_Time, Update_By, Direction, Kafka_Status, Status from tbl_feedback a
    </sql>

    <sql id="selectTblFeedbackItemVo">
        select Item_ID as sub_Item_ID, Seq_No as sub_Seq_No, Plant_Code as sub_Plant_Code, Order_Code as sub_Order_Code, Order_Line_No as sub_Item_No, Article_No as sub_Article_No, Article_Name as sub_Article_Name, Rcv_Date as sub_Rcv_Date, Rcv_Time as sub_Rcv_Time, Quantity as sub_Quantity, Unit as sub_Unit, Item_Amount as sub_Item_Amount, Tax_Code as sub_Tax_Code, Rcv_Doc_No as sub_Rcv_Doc_No, Article_Doc_Annual as sub_Article_Doc_Annual, Rcv_Doc_Item_No as sub_Rcv_Doc_Item_No, Feed_ID as sub_Feed_ID, Create_Time as sub_Create_Time, Create_By as sub_Create_By, Update_Time as sub_Update_Time, Update_By as sub_Update_By from tbl_feedback_item
    </sql>

    <select id="selectTblFeedbackItemList" parameterType="TblFeedbackItem" resultMap="TblFeedbackItemResult">
        <include refid="selectTblFeedbackItemVo"/>
        <where>
            <if test="orderCode != null  and orderCode != ''"> and Order_Code = #{orderCode}</if>
            <if test="orderLineNo != null  and orderLineNo != ''"> and Order_Line_No = #{orderLineNo}</if>
            <if test="articleNo != null  and articleNo != ''"> and Article_No = #{articleNo}</if>
            <if test="articleName != null  and articleName != ''"> and Article_Name like concat('%', #{articleName}, '%')</if>
            <if test="rcvDate != null "> and Rcv_Date = #{rcvDate}</if>
            <if test="rcvTime != null "> and Rcv_Time = #{rcvTime}</if>
            <if test="quantity != null "> and Quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and Unit = #{unit}</if>
            <if test="rcvDocNo != null  and rcvDocNo != ''"> and Rcv_Doc_No = #{rcvDocNo}</if>
            <if test="articleDocAnnual != null "> and Article_Doc_Annual = #{articleDocAnnual}</if>
            <if test="rcvDocItemNo != null "> and Rcv_Doc_Item_No = #{rcvDocItemNo}</if>
            <if test="feedId != null "> and Feed_ID = #{feedId}</if>
        </where>
    </select>

    <select id="selectTblFeedbackList" parameterType="TblFeedback" resultMap="TblFeedbackResult">
        <include refid="selectTblFeedbackVo"/>
        <where>
            <if test="dnNo != null  and dnNo != ''"> and Dn_No like concat('%', #{dnNo}, '%')</if>
            <if test="compCode != null  and compCode != ''"> and Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="depot != null  and depot != ''"> and depot like concat('%', #{depot}, '%')</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectTblFeedbackOnlyById" parameterType="TblFeedback" resultMap="TblFeedbackResult">
        <include refid="selectTblFeedbackVo"/>
        where Feed_ID = #{feedId}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectTblFeedbackById" parameterType="Long" resultMap="TblFeedbackTblFeedbackItemResult">
        select a.Feed_ID, a.Dn_No, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Doc_Date, a.Total_Amount, a.Currency, a.Settlement_No, a.Invoice_Date, a.Invoice_Amount, a.Invoice_Tax, a.Invoice_No, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status, a.Status,
            b.Item_ID as sub_Item_ID, b.Order_Code as sub_Order_Code, b.Order_Line_No as sub_Item_No, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Rcv_Date as sub_Rcv_Date, b.Rcv_Time as sub_Rcv_Time, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Rcv_Doc_No as sub_Rcv_Doc_No, b.Article_Doc_Annual as sub_Article_Doc_Annual, b.Rcv_Doc_Item_No as sub_Rcv_Doc_Item_No, b.Feed_ID as sub_Feed_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_feedback a
        left join tbl_feedback_item b on b.Feed_ID = a.Feed_ID
        where a.Feed_ID = #{feedId}
    </select>

    <select id="selectTblFeedbackWithItemList" parameterType="TblFeedback" resultMap="TblFeedbackTblFeedbackItemResult">
        select a.Feed_ID, a.Dn_No, a.depot, a.receiving_place, a.receiving_date, a.receiving_quantity, a.order_unit, a.delivery_note_date, a.delivery_note_time, a.Comp_Code, a.Plant_Code, a.Plant_Name, a.Supp_Code, a.Supp_Name, a.Doc_Date, a.Total_Amount, a.Currency, a.Settlement_No, a.Invoice_Date, a.Invoice_Amount, a.Invoice_Tax, a.Invoice_No, a.Create_Time, a.Create_By, a.Update_Time, a.Update_By, a.Direction, a.Kafka_Status, a.Status,
               b.Item_ID as sub_Item_ID, b.Order_Code as sub_Order_Code, b.Order_Line_No as sub_Item_No, b.Article_No as sub_Article_No, b.Article_Name as sub_Article_Name, b.Rcv_Date as sub_Rcv_Date, b.Rcv_Time as sub_Rcv_Time, b.Quantity as sub_Quantity, b.Unit as sub_Unit, b.Rcv_Doc_No as sub_Rcv_Doc_No, b.Article_Doc_Annual as sub_Article_Doc_Annual, b.Rcv_Doc_Item_No as sub_Rcv_Doc_Item_No, b.Feed_ID as sub_Feed_ID, b.Create_Time as sub_Create_Time, b.Create_By as sub_Create_By, b.Update_Time as sub_Update_Time, b.Update_By as sub_Update_By
        from tbl_feedback a
                 left join tbl_feedback_item b on b.Feed_ID = a.Feed_ID
        <where>
            <if test="dnNo != null  and dnNo != ''"> and Dn_No like concat('%', #{dnNo}, '%')</if>
            <if test="compCode != null  and compCode != ''"> and Comp_Code like concat('%', #{compCode}, '%')</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code like concat('%', #{plantCode}, '%')</if>
            <if test="plantName != null  and plantName != ''"> and Plant_Name like concat('%', #{plantName}, '%')</if>
            <if test="suppCode != null  and suppCode != ''"> and Supp_Code like concat('%', #{suppCode}, '%')</if>
            <if test="suppName != null  and suppName != ''"> and Supp_Name like concat('%', #{suppName}, '%')</if>
            <if test="direction != null  and direction != ''"> and Direction = #{direction}</if>
            <if test="kafkaStatus != null  and kafkaStatus != ''"> and Kafka_Status = #{kafkaStatus}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>

    <select id="selectTblApiFeedbackItemByFeedId" parameterType="Long" resultMap="TblApiFeedbackItemResult">
        <include refid="selectTblFeedbackItemVo"/>
        where Feed_ID = #{feedId}
    </select>

    <select id="selectTblFeedbackFullList" parameterType="TblFeedback" resultMap="TblFeedbackTblFeedbackItemApiResult">
        <include refid="selectTblFeedbackVo"/>
        <where>
            Direction = 'I'
            <if test="params.cursor !=null">and Feed_ID > #{params.cursor}</if>
            <if test="params.cursorInclude !=null">and Feed_ID >= #{params.cursorInclude}</if>
            <if test="params.time !=null">and Create_Time > #{params.time}</if>
            <if test="params.timeInclude !=null">and Create_Time >= #{params.timeInclude}</if>
        </where>
        order by Feed_ID asc
        <if test="params.limit !=null and params.limit !=''">limit #{params.limit}</if>
    </select>

    <insert id="insertTblFeedback" parameterType="TblFeedback" useGeneratedKeys="true" keyProperty="feedId">
        insert into tbl_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dnNo != null and dnNo != ''">Dn_No,</if>
            <if test="delFlag != null and delFlag != ''">Del_Flag,</if>
            <if test="compCode != null and compCode != ''">Comp_Code,</if>
            <if test="compName != null and compName != ''">Comp_Name,</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code,</if>
            <if test="plantName != null and plantName != ''">Plant_Name,</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code,</if>
            <if test="suppName != null and suppName != ''">Supp_Name,</if>
            <if test="docDate != null and docDate != ''">Doc_Date,</if>
            <if test="totalAmount != null and totalAmount != ''">Total_Amount,</if>
            <if test="currency != null and currency != ''">Currency,</if>
            <if test="settlementNo != null and settlementNo != ''">Settlement_No,</if>
            <if test="invoiceDate != null and invoiceDate != ''">Invoice_Date,</if>
            <if test="invoiceAmount != null">Invoice_Amount,</if>
            <if test="invoiceTax != null">Invoice_Tax,</if>
            <if test="invoiceNo != null and invoiceNo != ''">Invoice_No,</if>
            <if test="depot != null and depot != ''">depot,</if>
            <if test="receivingPlace != null and receivingPlace != ''">receiving_place,</if>
            <if test="receivingDate != null ">receiving_date,</if>
            <if test="receivingQuantity != null and receivingQuantity != ''">receiving_quantity,</if>
            <if test="orderUnit != null and orderUnit != ''">order_unit,</if>
            <if test="deliveryNoteDate != null ">delivery_note_date,</if>
            <if test="deliveryNoteTime != null ">delivery_note_time,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="direction != null and direction != ''">Direction,</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status,</if>
            <if test="status != null and status != ''">Status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dnNo != null and dnNo != ''">#{dnNo},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="compCode != null and compCode != ''">#{compCode},</if>
            <if test="compName != null and compName != ''">#{compName},</if>
            <if test="plantCode != null and plantCode != ''">#{plantCode},</if>
            <if test="plantName != null and plantName != ''">#{plantName},</if>
            <if test="suppCode != null and suppCode != ''">#{suppCode},</if>
            <if test="suppName != null and suppName != ''">#{suppName},</if>
            <if test="docDate != null and docDate != ''">#{docDate},</if>
            <if test="totalAmount != null and totalAmount != ''">#{totalAmount},</if>
            <if test="currency != null and currency != ''">#{currency},</if>
            <if test="settlementNo != null and settlementNo != ''">#{settlementNo},</if>
            <if test="invoiceDate != null and invoiceDate != ''">#{invoiceDate},</if>
            <if test="invoiceAmount != null">#{invoiceAmount},</if>
            <if test="invoiceTax != null">#{invoiceTax},</if>
            <if test="invoiceNo != null and invoiceNo != ''">#{invoiceNo},</if>
            <if test="depot != null and depot != ''">#{depot},</if>
            <if test="receivingPlace != null and receivingPlace != ''">#{receivingPlace},</if>
            <if test="receivingDate != null ">#{receivingDate},</if>
            <if test="receivingQuantity != null and receivingQuantity != ''">#{receivingQuantity},</if>
            <if test="orderUnit != null and orderUnit != ''">#{orderUnit},</if>
            <if test="deliveryNoteDate != null ">#{deliveryNoteDate},</if>
            <if test="deliveryNoteTime != null ">#{deliveryNoteTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="direction != null and direction != ''">#{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">#{kafkaStatus},</if>
            <if test="status != null and status != ''">#{status},</if>
         </trim>
    </insert>

    <update id="updateTblFeedback" parameterType="TblFeedback">
        update tbl_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="dnNo != null and dnNo != ''">Dn_No = #{dnNo},</if>
            <if test="delFlag != null and delFlag != ''">Del_Flag = #{delFlag},</if>
            <if test="compCode != null and compCode != ''">Comp_Code = #{compCode},</if>
            <if test="compName != null and compName != ''">Comp_Name = #{compName},</if>
            <if test="plantCode != null and plantCode != ''">Plant_Code = #{plantCode},</if>
            <if test="plantName != null and plantName != ''">Plant_Name = #{plantName},</if>
            <if test="suppCode != null and suppCode != ''">Supp_Code = #{suppCode},</if>
            <if test="suppName != null and suppName != ''">Supp_Name = #{suppName},</if>
            <if test="docDate != null and docDate != ''">Doc_Date = #{docDate},</if>
            <if test="totalAmount != null and totalAmount != ''">Total_Amount = #{totalAmount},</if>
            <if test="currency != null and currency != ''">Currency = #{currency},</if>
            <if test="settlementNo != null and settlementNo != ''">Settlement_No = #{settlementNo},</if>
            <if test="invoiceDate != null and invoiceDate != ''">Invoice_Date = #{invoiceDate},</if>
            <if test="invoiceAmount != null">Invoice_Amount = #{invoiceAmount},</if>
            <if test="invoiceTax != null">Invoice_Tax = #{invoiceTax},</if>
            <if test="invoiceNo != null and invoiceNo != ''">Invoice_No = #{invoiceNo},</if>
            <if test="depot != null and depot != ''">depot = #{depot},</if>
            <if test="receivingPlace != null and receivingPlace != ''">receiving_place = #{receivingPlace},</if>
            <if test="receivingDate != null ">receiving_date = #{receivingDate},</if>
            <if test="receivingQuantity != null and receivingQuantity != ''">receiving_quantity = #{receivingQuantity},</if>
            <if test="orderUnit != null and orderUnit != ''">order_unit = #{orderUnit},</if>
            <if test="deliveryNoteDate != null ">delivery_note_date = #{deliveryNoteDate},</if>
            <if test="deliveryNoteTime != null ">delivery_note_time = #{deliveryNoteTime},</if>
            <if test="createTime != null">Create_Time = #{createTime},</if>
            <if test="createBy != null">Create_By = #{createBy},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
            <if test="direction != null and direction != ''">Direction = #{direction},</if>
            <if test="kafkaStatus != null and kafkaStatus != ''">Kafka_Status = #{kafkaStatus},</if>
            <if test="status != null and status != ''">Status = #{status},</if>
        </trim>
        where Feed_ID = #{feedId}
    </update>

    <delete id="deleteTblFeedbackById" parameterType="Long">
        delete from tbl_feedback where Feed_ID = #{feedId}
    </delete>

    <delete id="deleteTblFeedbackByIds" parameterType="String">
        delete from tbl_feedback where Feed_ID in
        <foreach item="feedId" collection="array" open="(" separator="," close=")">
            #{feedId}
        </foreach>
    </delete>

    <delete id="deleteTblFeedbackItemByFeedIds" parameterType="String">
        delete from tbl_feedback_item where Feed_ID in
        <foreach item="feedId" collection="array" open="(" separator="," close=")">
            #{feedId}
        </foreach>
    </delete>

    <delete id="deleteTblFeedbackItemByFeedId" parameterType="Long">
        delete from tbl_feedback_item where Feed_ID = #{feedId}
    </delete>

    <insert id="batchTblFeedbackItem">
        insert into tbl_feedback_item( Item_ID, Seq_No, Plant_Code, Order_Code, Order_Line_No, Article_No, Article_Name, Rcv_Date, Rcv_Time, Quantity, Unit, Item_Amount, Tax_Code, Rcv_Doc_No, Article_Doc_Annual, Rcv_Doc_Item_No, Feed_ID, Create_Time, Create_By, Update_Time, Update_By) values
		<foreach item="item" index="index" collection="list" separator=",">
            ( #{item.itemId}, #{item.seqNo}, #{item.plantCode}, #{item.orderCode}, #{item.orderLineNo}, #{item.articleNo}, #{item.articleName}, #{item.rcvDate}, #{item.rcvTime}, #{item.quantity}, #{item.unit}, #{item.itemAmount}, #{item.taxCode}, #{item.rcvDocNo}, #{item.articleDocAnnual}, #{item.rcvDocItemNo}, #{item.feedId}, #{item.createTime}, #{item.createBy}, #{item.updateTime}, #{item.updateBy})
        </foreach>
    </insert>

    <select id="selectLastId" resultType="Long">
        select max(Feed_ID) from tbl_feedback;
    </select>

    <update id="updateOrderAsnQuantity" parameterType="TblFeedbackItem">
        update tbl_order_asn_quantity
        set Unsent_Quantity = Unsent_Quantity - #{quantity}
        where Order_Code = #{orderCode} and Order_Line_No = #{orderLineNo}
    </update>

    <update id="updateOrderComplete" parameterType="String">
        update tbl_order
        set Is_Complete = 'Y'
        where Order_Code = #{orderCode}
        and not exists (
            select 1 from tbl_order_asn_quantity
            where Order_Code = #{orderCode}
            and Unsent_Quantity > 0
        )
    </update>
</mapper>