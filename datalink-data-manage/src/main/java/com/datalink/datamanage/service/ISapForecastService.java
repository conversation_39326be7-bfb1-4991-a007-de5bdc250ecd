package com.datalink.datamanage.service;

import com.datalink.api.domain.SapWeekForecast;
import com.datalink.api.domain.SapMonthForecast;
import com.datalink.api.domain.SapYearForecast;
import com.datalink.common.core.domain.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

public interface ISapForecastService {
    
    /**
     * 保存周预测数据
     */
    void saveWeekForecast(SapWeekForecast forecast);
    
    /**
     * 获取最新的周预测数据
     */
    SapWeekForecast getLatestWeekForecast(SapWeekForecast sapWeekForecast);

    /**
     * 根据ID查询周预测数据
     */
    SapWeekForecast selectWeekForecastById(Long id);

    AjaxResult downloadWeekForecast(SapWeekForecast sapWeekForecast, String tz);

    /**
     * 保存3个月预测数据
     */
    void saveMonthForecast(SapMonthForecast forecast);
    
    /**
     * 获取最新的3个月预测数据
     */
    SapMonthForecast getLatestMonthForecast(SapMonthForecast sapMonthForecast);
    
    /**
     * 根据ID查询3个月预测数据
     */
    SapMonthForecast selectMonthForecastById(Long id);
    
    /**
     * 下载3个月预测数据
     */
    AjaxResult downloadMonthForecast(SapMonthForecast sapMonthForecast, String tz);

    @Transactional(rollbackFor = Exception.class)
    void saveYearForecast(SapYearForecast forecast);

    SapYearForecast getLatestYearForecast(SapYearForecast forecast);

    AjaxResult downloadYearForecast(SapYearForecast sapYearForecast, String tz);

    AjaxResult downloadMonthForecastTxt(SapMonthForecast monthForecast, String tz);

    AjaxResult downloadYearForecastTxt(SapYearForecast yearForecast, String tz);

    AjaxResult downloadWeekForecastTxt(SapWeekForecast weekForecast, String tz);

    /**
     * 更新周预测已读状态
     */
    void updateWeekForecastReadStatus(Long id, String isRead);
    
    /**
     * 更新月预测已读状态
     */
    void updateMonthForecastReadStatus(Long id, String isRead);
    
    /**
     * 更新年预测已读状态
     */
    void updateYearForecastReadStatus(Long id, String isRead);
}