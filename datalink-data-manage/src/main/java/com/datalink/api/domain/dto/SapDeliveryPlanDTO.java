package com.datalink.api.domain.dto;

import com.datalink.api.domain.SapDeliveryPlanDetail;
import com.datalink.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 支给计划对象 tbl_sap_delivery_plan
 * 
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SapDeliveryPlanDTO extends BaseEntity
{
    // F/#
    @JsonProperty("ZTYPE")
    private String formatType;
    // 取引先
    @JsonProperty("LIFNR")
    private String client;
    // デポ
    @JsonProperty("ZDEPOT")
    private String depot;
    // 要求工場
    @JsonProperty("WERKS")
    private String factory;
    // 部品番号
    @JsonProperty("MATNR")
    private String partNumber;
    // 製品区分
    private String productCategory;
    // 納入方式
    @JsonProperty("ZMETHD")
    private String deliveryMethod;
    // 納入場所
    @JsonProperty("WANST")
    private String deliveryLocation;
    // 手配ロット
    private String allocationLot;
    // 支給区分
    @JsonProperty("ZQF")
    private String supplyType;
    // 基本数量単位
    @JsonProperty("MEINS")
    private String basicUnit;
    // SNEP
    @JsonProperty("BSTRF")
    private String snep;
    // 支給方式
    private String supplyMethod;
    // 発注単位
    private String orderUnit;
    // 単位条件
    private String unitCondition;
    // 前月支給残
    @JsonProperty("ZQQTY")
    private String lastMonthRemaining;
    // 作成年月日
    @JsonProperty("ERDAT")
    private String creationDate;
    // 取引先在庫
    @JsonProperty("LBLAB")
    private String clientInventory;
    // 確定済み年月日
    private String confirmedDate;
    // 更新年月日－時分
    private String updatedDateTime;


    /** 支给计划明细信息 */

    // 支給计划月日
    @JsonProperty("BDTER")
    private String supplyPlanDate;
    // 支給计划数量
    @JsonProperty("BDMNG")
    private String supplyPlanQuantity;
    // 删除标识
    @JsonProperty("LOEKZ")
    private String deleteFlag;
    // 预留号
    @JsonProperty("RSNUM")
    private String rsnum;
    // 预留行号
    @JsonProperty("RSPOS")
    private String rspos;

    /** 支给实际明细信息 */

    // 交货单
    @JsonProperty("VBELN")
    private String deliveryNote;
    // 交货单行号
    @JsonProperty("POSNR")
    private String deliveryNoteItemNumber;
    // 支給実績月日
    @JsonProperty("WADAT_IST")
    private String supplyActualDate;
    // 支給実績数量
    @JsonProperty("LFIMG")
    private String supplyActualQuantity;
    // 支給実績重量
    @JsonProperty("BRGEW")
    private String supplyActualWeight;

}
