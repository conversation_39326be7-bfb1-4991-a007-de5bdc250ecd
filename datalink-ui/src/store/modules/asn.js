const state = {
  asnEdit: null,
  asnCreate: null
}

const mutations = {
  SET_ASN_EDIT: (state, data) => {
    state.asnEdit = data
  },
  SET_ASN_CREATE: (state, data) => {
    state.asnCreate = data
  },
  CLEAR_ASN_DATA: (state) => {
    state.asnEdit = null
    state.asnCreate = null
  }
}

const actions = {
  setAsnEdit({ commit }, data) {
    commit('SET_ASN_EDIT', data)
  },
  setAsnCreate({ commit }, data) {
    commit('SET_ASN_CREATE', data)
  },
  clearAsnData({ commit }) {
    commit('CLEAR_ASN_DATA')
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
