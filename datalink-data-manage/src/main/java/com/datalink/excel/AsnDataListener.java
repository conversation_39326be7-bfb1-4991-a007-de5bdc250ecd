package com.datalink.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.datalink.common.utils.DateUtils;
import com.datalink.datamanage.domain.TblAsn;
import com.datalink.datamanage.domain.TblAsnArticle;
import com.datalink.datamanage.domain.TblAsnItem;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Map;

public class AsnDataListener extends AnalysisEventListener<Map<Integer, String>> {
    private TblAsn asn;
    private int recordPart;

    public AsnDataListener() {
        super();
        asn = new TblAsn();
        asn.setDetail(new ArrayList<TblAsnItem>());
        recordPart = 0;
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        int row = context.readRowHolder().getRowIndex();

        if (row == 1){
            asn.setCompCode(data.get(1));
            asn.setSuppCode(data.get(3));
            asn.setSuppName(data.get(5));
        }else if(row == 2){
            asn.setPlanDeliveryDate(DateUtils.parseDate(data.get(1)));
            asn.setDeliveryDate(DateUtils.parseDate(data.get(3)));
        }else{
            if (data.get(1).startsWith("工厂代码")){
                recordPart = 1; //ASN Detail
                return;
            }else if (data.get(1).startsWith("采购订单行项目")){
                recordPart = 2; //ASN Article
                return;
            }
            switch(recordPart){
                case 1:
                    TblAsnItem asnItem = new TblAsnItem();
                    asnItem.setOrderCode(data.get(0));
                    asnItem.setPlantCode(data.get(1));
                    asnItem.setPlantName(data.get(2));
                    asnItem.setUnloadingNo(data.get(3));
                    asnItem.setUnloadingName(data.get(4));
                    asnItem.setSendLocNo(data.get(5));
                    asnItem.setSendLocName(data.get(6));
                    asnItem.setRcvLocNo(data.get(7));
                    asnItem.setRcvLocName(data.get(8));
                    asnItem.setArticles(new ArrayList<TblAsnArticle>());
                    asn.getDetail().add(asnItem);
                    break;
                case 2:
                    TblAsnArticle article = new TblAsnArticle();
                    article.setOrderLineNo(data.get(1));
                    article.setArticleNo(data.get(2));
                    article.setArticleName(data.get(3));
                    article.setQuantity(getNumber(data.get(4)));
                    article.setUnit(data.get(5));
                    article.setQtyPerPack(getNumber(data.get(6)));
                    article.setPackQty(getNumber(data.get(7)));
                    article.setStartWith(data.get(8));
                    article.setEndWith(data.get(9));
                    article.setBatchNo(data.get(10));
                    article.setNonStd(data.get(11));
                    for (TblAsnItem item : asn.getDetail()){
                        if (item.getOrderCode().equals(data.get(0))){
                            item.getArticles().add(article);
                        }
                    }
                    break;
                default:
                    break;
            }


        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        System.out.println(asn);
        analysisContext.readWorkbookHolder().setCustomObject(asn);
    }

    private BigDecimal getNumber(String value){
        if (StringUtils.isNumeric(value)){
            return new BigDecimal(value);
        }else{
            return null;
        }
    }
}
