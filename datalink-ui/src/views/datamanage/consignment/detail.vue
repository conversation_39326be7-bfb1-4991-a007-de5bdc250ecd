<template>
  <div class="app-container">
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('consignment.detail.consignmentInfo') }}
      </div>
      <el-table
        border
        :show-header="false"
        :data="baseTableData">

        <el-table-column
          prop="title1"
          :label="$t('consignment.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('consignment.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content1 }}
          </template>
        </el-table-column>

        <el-table-column
          prop="title2"
          :label="$t('consignment.detail.title')"
          width="185">
        </el-table-column>

        <el-table-column
          :label="$t('consignment.detail.content')"
          min-width="310">
          <template  slot-scope="scope">
            {{ scope.row.content2 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <p></p>
    <el-card shadow="never" class="border-none margin-t24">
      <div slot="header">
        {{ $t('consignment.detail.lineItemInfo') }}
      </div>
      <el-table v-loading="loading" :data="consignmentitemList">
        <el-table-column :label="$t('consignment.detail.articleNo')" align="center" prop="articleNo" />
        <el-table-column :label="$t('consignment.detail.articleName')" align="center" prop="articleName" />
        <el-table-column :label="$t('consignment.detail.quantity')" align="center" prop="quantity" />
        <el-table-column :label="$t('consignment.detail.unit')" align="center" prop="unit" />
        <el-table-column :label="$t('consignment.detail.days')" align="center" prop="days" />
        <el-table-column :label="$t('consignment.detail.remark')" align="center" prop="remark" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
import { getConsignmentOnly, listConsignmentItems } from "@/api/datamanage/consignment";

export default {
  name: "ConsignmentDetail",
  data() {
    return {
      consignment: {},
      consignmentitemList: [],
      loading: true,
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        consignmentId: null
      }
    };
  },
  created() {
    const consignmentId = this.$route.params.consignmentId;
    this.queryParams.consignmentId = consignmentId;
    this.getConsignment(consignmentId);
    this.getList();
  },
  methods: {
    getConsignment(consignmentId) {
      getConsignmentOnly(consignmentId).then(res => {
        this.consignment = res.data;
      });
    },
    /** 查询订单行项目列表 */
    getList() {
      this.loading = true;
      listConsignmentItems(this.queryParams).then(response => {
        this.consignmentitemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    }
  },
  computed: {
    // 基本信息-表格数据
    baseTableData() {
      if (this.consignment.direction === 'I') {
        return [
          {
            title1: this.$t('consignment.detail.compCode'),
            content1: this.consignment.compCode
          },
          {
            title1: this.$t('consignment.detail.plantCode'),
            content1: this.consignment.plantCode,
            title2: this.$t('consignment.detail.plantName'),
            content2: this.consignment.plantName
          },
          {
            title1: this.$t('consignment.detail.suppCode'),
            content1: this.consignment.suppCode,
            title2: this.$t('consignment.detail.suppName'),
            content2: this.consignment.suppName
          },
          {
            title1: this.$t('consignment.detail.updateDate'),
            content1: this.consignment && this.consignment.updateDate && this.consignment.updateDate.length >= 8
              ? this.consignment.updateDate.slice(0, 4) + '-' + this.consignment.updateDate.slice(4, 6) + '-' + this.consignment.updateDate.slice(6, 8)
              : '',
            title2: this.$t('consignment.detail.updateTime'),
            content2: this.consignment && this.consignment.updateTime && this.consignment.updateTime.length >= 6
              ? this.consignment.updateTime.slice(0, 2) + ':' + this.consignment.updateTime.slice(2, 4) + ':' + this.consignment.updateTime.slice(4, 6)
              : ''
          }
        ];
      } else {
        return [
          {
            title1: this.$t('consignment.detail.suppCode'),
            content1: this.consignment.suppCode,
            title2: this.$t('consignment.detail.suppName'),
            content2: this.consignment.suppName
          },
          {
            title1: this.$t('consignment.detail.plantCode'),
            content1: this.consignment.plantCode,
            title2: this.$t('consignment.detail.plantName'),
            content2: this.consignment.plantName
          },
          {
            title1: this.$t('consignment.detail.updateDate'),
            content1: this.consignment && this.consignment.updateDate && this.consignment.updateDate.length >= 8
              ? this.consignment.updateDate.slice(0, 4) + '-' + this.consignment.updateDate.slice(4, 6) + '-' + this.consignment.updateDate.slice(6, 8)
              : '',
            title2: this.$t('consignment.detail.updateTime'),
            content2: this.consignment && this.consignment.updateTime && this.consignment.updateTime.length >= 6
              ? this.consignment.updateTime.slice(0, 2) + ':' + this.consignment.updateTime.slice(2, 4) + ':' + this.consignment.updateTime.slice(4, 6)
              : ''
          }
        ];
      }
    }
  }
};
</script>

<style scoped>

</style>
