package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblConsignmentInventory;
import com.datalink.datamanage.domain.TblConsignmentInventoryItem;
import com.datalink.datamanage.service.ITblConsignmentInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api("寄售库存接口")
@Validated
@RestController
@RequestMapping("/api/consignment")
public class TblConsignmentInventoryApi {

    @Autowired
    private ITblConsignmentInventoryService tblConsignmentInventoryService;

    //    @ApiOperation("查询寄售库存")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"consignmentid","searchvalue","createby","updateby","updatetimesys","params","createtime","itemid","direction","kafkastatus"}, value={TblConsignmentInventory.class, TblConsignmentInventoryItem.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","consignmentInventoryid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblConsignmentInventoryItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询寄售库存", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblConsignmentInventory> consignmentInventoryList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblConsignmentInventory searchParam = new TblConsignmentInventory();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            consignmentInventoryList = tblConsignmentInventoryService.selectTblConsignmentInventoryFullList(searchParam);
        }
        if(consignmentInventoryList.isEmpty()){
            Long lastId = tblConsignmentInventoryService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+consignmentInventoryList.get(consignmentInventoryList.size()-1).getConsignmentId());
            ajax.put("time", consignmentInventoryList.get(consignmentInventoryList.size()-1).getCreateTime());
        }

        ajax.put("items", consignmentInventoryList);
        return ajax;
    }

    //    @ApiOperation("发送")
    @ApiImplicitParam(name = "consignmentInventory", value = "寄售库存信息", dataType = "TblConsignmentInventory")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送寄售库存", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblConsignmentInventory tblConsignmentInventory) {
        tblConsignmentInventory.setDirection(DataConstants.DIRECTION_OUT);
        tblConsignmentInventory.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblConsignmentInventory.setCreateBy(SecurityUtils.getUsername());
        tblConsignmentInventoryService.insertTblConsignmentInventory(tblConsignmentInventory);
        return ApiResult.success();
    }

    //    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "consignmentInventory", value = "寄售库存信息", dataType = "TblConsignmentInventory", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送寄售库存", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblConsignmentInventory> consignmentInventoryList) {
        for (TblConsignmentInventory consignmentInventory : consignmentInventoryList){
            consignmentInventory.setDirection(DataConstants.DIRECTION_OUT);
            consignmentInventory.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            consignmentInventory.setCreateBy(SecurityUtils.getUsername());
            tblConsignmentInventoryService.insertTblConsignmentInventory(consignmentInventory);
        }

        return ApiResult.success();
    }
}
