<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item v-if="queryParams.direction === 'I'" :label="$t('consignment.form.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('consignment.form.inputCompCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'O'" :label="$t('consignment.form.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('consignment.form.inputSuppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="queryParams.direction === 'O'" :label="$t('consignment.form.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('consignment.form.inputSuppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('consignment.form.plantCode')" prop="plantCode">
        <el-input
          v-model="queryParams.plantcode"
          :placeholder="$t('consignment.form.inputPlantCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('consignment.form.plantName')" prop="plantName">
        <el-input
          v-model="queryParams.plantname"
          :placeholder="$t('consignment.form.inputPlantName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('consignment.form.updateDate')">
        <el-date-picker
          v-model="daterangeUpdateDate"
          size="small"

          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('consignment.form.startDate')"
          :end-placeholder="$t('consignment.form.endDate')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('consignment.form.updateTime')">
        <el-time-picker
          v-model="daterangeUpdateTime"
          size="small"

          value-format="HH:mm:ss"
          is-range
          range-separator="-"
          :start-placeholder="$t('consignment.form.startTime')"
          :end-placeholder="$t('consignment.form.endTime')"
        ></el-time-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ $t('consignment.form.search') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ $t('consignment.form.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['datamanage:consignment:export']"
        >{{ $t('consignment.form.export') }}</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="consignmentList" @selection-change="handleSelectionChange">
      <el-table-column v-if="queryParams.direction === 'I'" :label="$t('consignment.table.compCode')" align="center" prop="compCode" />
      <el-table-column v-if="queryParams.direction === 'O'" :label="$t('consignment.table.suppCode')" align="center" prop="suppCode" />
      <el-table-column v-if="queryParams.direction === 'O'" :label="$t('consignment.table.suppName')" align="center" prop="suppName" />
      <el-table-column :label="$t('consignment.table.plantCode')" align="center" prop="plantCode" />
      <el-table-column :label="$t('consignment.table.plantName')" align="center" prop="plantName" />
      <el-table-column :label="$t('consignment.table.updateDate')" align="center" prop="updateDate" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.updateDate.slice(0, 4) + '-' + scope.row.updateDate.slice(4, 6) + '-' +  scope.row.updateDate.slice(6, 8) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('consignment.table.updateTime')" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.updateTime.slice(0, 2) + ':' + scope.row.updateTime.slice(2, 4) + ':' +  scope.row.updateTime.slice(4, 6) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('consignment.table.actions')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="goDetail(scope.row)"
            v-hasPermi="['datamanage:consignment:query']"
          >{{ $t('consignment.table.view') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改寄售库存对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="$t('consignment.dialog.compCode')" prop="compcode">
          <el-input v-model="form.compcode" :placeholder="$t('consignment.dialog.inputCompCode')" />
        </el-form-item>
        <el-form-item :label="$t('consignment.dialog.plantCode')" prop="plantcode">
          <el-input v-model="form.plantcode" :placeholder="$t('consignment.dialog.inputPlantCode')" />
        </el-form-item>
        <el-form-item :label="$t('consignment.dialog.plantName')" prop="plantname">
          <el-input v-model="form.plantname" :placeholder="$t('consignment.dialog.inputPlantName')" />
        </el-form-item>
        <el-form-item :label="$t('consignment.dialog.suppCode')" prop="suppcode">
          <el-input v-model="form.suppcode" :placeholder="$t('consignment.dialog.inputSuppCode')" />
        </el-form-item>
        <el-form-item :label="$t('consignment.dialog.suppName')" prop="suppname">
          <el-input v-model="form.suppname" :placeholder="$t('consignment.dialog.inputSuppName')" />
        </el-form-item>
        <el-form-item :label="$t('consignment.dialog.updateDate')" prop="updatedate">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.updatedate"
            type="date"
            value-format="yyyy-MM-dd"
            :placeholder="$t('consignment.dialog.selectUpdateDate')">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ $t('consignment.dialog.confirm') }}</el-button>
        <el-button @click="cancel">{{ $t('consignment.dialog.cancel') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listConsignment, getConsignment, delConsignment, addConsignment, updateConsignment, exportConsignment } from "@/api/datamanage/consignment";

export default {
  name: "Consignment",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 寄售库存表格数据
      consignmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 时间窗开始时间范围
      daterangeUpdateDate: [],
      // 时间窗结束时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        compCode: null,
        plantCode: null,
        plantName: null,
        updateDate: null,
        updateTime: null,
        direction: 'I',
        orderByColumn: 'createTime',
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        compcode: [
          { required: true, message: this.$t('consignment.validation.compCodeRequired'), trigger: "blur" }
        ],
        plantcode: [
          { required: true, message: this.$t('consignment.validation.plantCodeRequired'), trigger: "blur" }
        ],
        plantname: [
          { required: true, message: this.$t('consignment.validation.plantNameRequired'), trigger: "blur" }
        ],
        suppcode: [
          { required: true, message: this.$t('consignment.validation.suppCodeRequired'), trigger: "blur" }
        ],
        suppname: [
          { required: true, message: this.$t('consignment.validation.suppNameRequired'), trigger: "blur" }
        ],
        updatedate: [
          { required: true, message: this.$t('consignment.validation.updateDateRequired'), trigger: "blur" }
        ],
        updatetime: [
          { required: true, message: this.$t('consignment.validation.updateTimeRequired'), trigger: "blur" }
        ]
      }
    };
  },
  created() {
    if (this.$route.path.toLowerCase().endsWith('recv/consignment')){
      this.queryParams.direction = 'I';
    }else{
      this.queryParams.direction = 'O';
    }
    this.getList();
  },
  methods: {
    /** 查询寄售库存列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (this.daterangeUpdateDate.length > 0) {
        this.queryParams.params["beginUpdateDate"] = this.daterangeUpdateDate[0];
        this.queryParams.params["endUpdateDate"] = this.daterangeUpdateDate[1];
      }
      if (this.daterangeUpdateTime.length > 0) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listConsignment(this.queryParams).then(response => {
        this.consignmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        consignmentid: null,
        compcode: null,
        plantcode: null,
        plantname: null,
        suppcode: null,
        suppname: null,
        updatedate: null,
        updatetime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.consignmentid)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t('consignment.dialog.addTitle');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const consignmentId = row.consignmentid || this.ids;
      this.$router.push("/consignment/detail/" + consignmentId);
    },
    goDetail(row) {
      this.$router.push({name: "ConsignmentDetail", params: {consignmentId: row.consignmentId}});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.consignmentid != null) {
            updateConsignment(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('consignment.messages.updateSuccess'));
                this.open = false;
                this.getList();
              }
            });
          } else {
            addConsignment(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(this.$t('consignment.messages.addSuccess'));
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const consignmentids = row.consignmentid || this.ids;
      this.$confirm(this.$t('consignment.messages.deleteConfirm', { id: consignmentids }), this.$t('common.warning'), {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: "warning"
        }).then(function() {
          return delConsignment(consignmentids);
        }).then(() => {
          this.getList();
          this.msgSuccess(this.$t('consignment.messages.deleteSuccess'));
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(this.$t('consignment.messages.exportConfirm'), this.$t('common.warning'), {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: "warning"
        }).then(function() {
          return exportConsignment(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>
