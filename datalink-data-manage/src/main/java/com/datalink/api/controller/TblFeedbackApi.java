package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.api.common.SapApiResult;
import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.AcceptanceDetailRequest;
import com.datalink.api.domain.SapGrItem;
import com.datalink.api.domain.SapListRequst;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.domain.TblFeedbackItem;
import com.datalink.datamanage.service.ITblFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api("收货反馈接口")
@Validated
@RestController
@RequestMapping("/api/feedback")
public class TblFeedbackApi {

    private static final Logger logger = LoggerFactory.getLogger(TblFeedbackApi.class);

    @Autowired
    private ITblFeedbackService tblFeedbackService;

    //    @ApiOperation("查询收货反馈")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"feedid","searchvalue","createby","updateby","updatetime","remark","params","createtime","itemid","direction","kafkastatus"}, value={TblFeedback.class, TblFeedbackItem.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","orderid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblOrderItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询收货反馈", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblFeedback> feedbacks = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblFeedback searchParam = new TblFeedback();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            feedbacks = tblFeedbackService.selectTblFeedbackFullList(searchParam);
        }
        if(feedbacks.isEmpty()){
            Long lastId = tblFeedbackService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+feedbacks.get(feedbacks.size()-1).getFeedId());
            ajax.put("time", feedbacks.get(feedbacks.size()-1).getCreateTime());
        }

        ajax.put("items", feedbacks);
        return ajax;
    }

    //    @ApiOperation("发送")
    @ApiImplicitParam(name = "feedback", value = "收货反馈信息", dataType = "TblFeedback")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送收货反馈", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblFeedback tblFeedback) {
        tblFeedback.setDirection(DataConstants.DIRECTION_OUT);
        tblFeedback.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblFeedback.setCreateBy(SecurityUtils.getUsername());
        tblFeedbackService.insertTblFeedback(tblFeedback);
        return ApiResult.success();
    }

    //    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "feedback", value = "收货反馈信息", dataType = "TblFeedback", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送收货反馈", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblFeedback> feedbacks) {
        for (TblFeedback feedback : feedbacks){
            feedback.setDirection(DataConstants.DIRECTION_OUT);
            feedback.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            feedback.setCreateBy(SecurityUtils.getUsername());
            tblFeedbackService.insertTblFeedback(feedback);
        }

        return ApiResult.success();
    }

    //    @ApiOperation("SAP发送收货反馈")
    @PostMapping("/sapGr")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送收货反馈", businessType = BusinessType.IMPORT)
    public SapApiResult sapGr(@Valid @RequestBody SapListRequst<SapGrItem> request) {
        try{
            List<TblFeedback> feedbacks = SapObjectConverter.convertToFeedBack(request);
            for (TblFeedback feedback:feedbacks){
                tblFeedbackService.insertTblFeedback(feedback);
                // 买卖两件业务，收到收货反馈后需要判断剩余数量，若为0则更新订单状态为完成
                tblFeedbackService.updateOrderStatus(feedback);
            }
        }catch(Exception e){
            logger.error("SAP发送收货反馈异常", e);
            return SapApiResult.error(e.getMessage());
        }

        return SapApiResult.success();
    }

    @ApiOperation("接收结算单")
    @ApiImplicitParam(name = "request", value = "结算单信息", dataType = "AcceptanceDetailRequest")
    @PostMapping("/receive")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "接收结算单", businessType = BusinessType.IMPORT)
    public AjaxResult receive(@Valid @RequestBody AcceptanceDetailRequest request) {
        try {
            // 转换为TblFeedback对象
            TblFeedback feedback = SapObjectConverter.convertToFeedback(request);

            // 根据dnNo查询是否已存在相同的结算单
            TblFeedback searchParam = new TblFeedback();
            searchParam.setDnNo(feedback.getDnNo());
            List<TblFeedback> existingFeedbacks = tblFeedbackService.selectTblFeedbackList(searchParam);

            if (!existingFeedbacks.isEmpty()) {
                // 结算单已存在,执行更新
                TblFeedback existingFeedback = existingFeedbacks.get(0);
                feedback.setFeedId(existingFeedback.getFeedId());
                feedback.setStatus(DataConstants.FEEDBACK_STATUS_NEW); // 重新设置为New状态
                tblFeedbackService.updateTblFeedback(feedback);
                logger.info("更新结算单成功，结算单号：{}", request.getZsett());
            } else {
                // 结算单不存在,执行插入，设置状态为New
                feedback.setStatus(DataConstants.FEEDBACK_STATUS_NEW);
                feedback.setDirection(DataConstants.DIRECTION_IN);
                feedback.setKafkaStatus(DataConstants.KAFKA_STATUS_NO_SENT);
                feedback.setCreateBy(SecurityUtils.getUsername());
                tblFeedbackService.insertTblFeedback(feedback);
                logger.info("新增结算单成功，结算单号：{}", request.getZsett());
            }

            return AjaxResult.success();

        } catch (Exception e) {
            logger.error("处理结算单数据失败：", e);
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            return AjaxResult.error(e.getCause().getMessage());
            throw new RuntimeException("处理结算单数据失败: " + e.getCause().getMessage());
        }
    }

}
