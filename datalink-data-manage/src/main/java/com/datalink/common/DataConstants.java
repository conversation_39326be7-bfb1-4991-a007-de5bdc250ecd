package com.datalink.common;

public class DataConstants {
    public static final String COMPANY_ID_CONFIG_KEY = "compId";

    public static final String PLANT_CODE_CONFIG_KEY = "plantCode";

    public static final String PLANT_NAME_CONFIG_KEY = "plantName";

    public static final String PO_TYPE = "PO";

    public static final String CONSIGNMENT_TYPE = "Consignment";

    public static final String FORECAST_TYPE = "Forecast";

    public static final String ASN_TYPE = "ASN";

    public static final String FEEDBACK_TYPE = "Feedback";

    public static final String INVENTORY_TYPE = "Inventory";

    public static final String EVENT_TYPE = "Event";

    public static final String NOTICE_TYPE = "Notice";

    public static final String UPDATE_PO_TYPE = "UPDATE_"+PO_TYPE;

    public static final String UPDATE_CONSIGNMENT_TYPE = "UPDATE_"+CONSIGNMENT_TYPE;

    public static final String UPDATE_FORECAST_TYPE = "UPDATE_"+FORECAST_TYPE;

    public static final String UPDATE_ASN_TYPE = "UPDATE_"+ASN_TYPE;

    public static final String UPDATE_FEEDBACK_TYPE = "UPDATE_"+FEEDBACK_TYPE;

    public static final String UPDATE_INVENTORY_TYPE = "UPDATE_"+INVENTORY_TYPE;

    public static final String DIRECTION_IN = "I";

    public static final String DIRECTION_OUT = "O";

    public static final String KAFKA_STATUS_TO_SEND = "1";

    public static final String KAFKA_STATUS_SENT = "2";

    public static final String KAFKA_STATUS_SEND_FAIL = "3";

    public static final String KAFKA_STATUS_NO_SENT = "0";

    public static final String FORECAST_STATUS_NEW = "New";

    public static final String FORECAST_STATUS_CONFIRMED = "Confirmed";

    public static final String ORDER_STATUS_NEW = "New";

    public static final String ORDER_STATUS_CONFIRMED = "Confirmed";

    public static final String FEEDBACK_STATUS_NEW = "New";

    public static final String FEEDBACK_STATUS_CONFIRMED = "Confirmed";

    public static final String SUPPLIER_PREFIX = "supp.";

    public static final String ROUTE_PREFIX = "route.";

    public static final String ASN_CODE_PARAM = "asn.code";

    public static final String BAR_CODE_PARAM = "asn.barcode";

    public static final String NOTICE_CODE_PARAM = "notice.code";
    public static final String NOTICE_REPLY_TYPE = "NoticeReply";
    public static final String SA_TYPE = "SA";
    public static final String SAP_ASN_URL = "sap.asn.url";
    public static final String SAP_USER = "sap.user";
    public static final String SAP_PASS = "sap.pass";
    public static final String SAP_FORECAST_CONFIRM_URL = "sap.forecast.confirm.url";
    public static final String SAP_ORDER_CONFIRM_URL = "sap.order.confirm.url";
    public static final String SAP_FEEDBACK_CONFIRM_URL = "sap.feedback.confirm.url";
}
