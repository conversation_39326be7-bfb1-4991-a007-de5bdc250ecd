package com.datalink.datamanage.service.impl;

import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblInventory;
import com.datalink.datamanage.domain.TblInventoryItem;
import com.datalink.datamanage.mapper.TblInventoryMapper;
import com.datalink.datamanage.service.ITblInventoryService;
import com.datalink.datamanage.service.ITblInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 库存Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
@Service
public class TblInventoryServiceImpl implements ITblInventoryService 
{
    @Autowired
    private TblInventoryMapper tblInventoryMapper;

    /**
     * 查询库存
     * 
     * @param inventoryId 库存ID
     * @return 库存
     */
    @Override
    public TblInventory selectTblInventoryById(Long inventoryId)
    {
        return tblInventoryMapper.selectTblInventoryById(inventoryId);
    }

    /**
     * 查询库存列表
     * 
     * @param tblInventory 库存
     * @return 库存
     */
    @Override
    public List<TblInventory> selectTblInventoryList(TblInventory tblInventory)
    {
        return tblInventoryMapper.selectTblInventoryList(tblInventory);
    }

    /**
     * 新增库存
     * 
     * @param tblInventory 库存
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblInventory(TblInventory tblInventory)
    {
        tblInventory.setCreateTime(DateUtils.getNowDate());
        int rows = tblInventoryMapper.insertTblInventory(tblInventory);
        insertTblInventoryItem(tblInventory);
        return rows;
    }

    /**
     * 修改库存
     * 
     * @param tblInventory 库存
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblInventory(TblInventory tblInventory)
    {
        tblInventory.setUpdateTimeSys(DateUtils.getNowDate());
        tblInventoryMapper.deleteTblInventoryItemByInventoryId(tblInventory.getInventoryId());
        insertTblInventoryItem(tblInventory);
        return tblInventoryMapper.updateTblInventory(tblInventory);
    }

    @Override
    public int updateTblInventoryOnly(TblInventory tblInventory) {
        tblInventory.setUpdateTimeSys(DateUtils.getNowDate());
        return tblInventoryMapper.updateTblInventory(tblInventory);
    }

    /**
     * 批量删除库存
     * 
     * @param inventoryIds 需要删除的库存ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblInventoryByIds(Long[] inventoryIds)
    {
        tblInventoryMapper.deleteTblInventoryItemByInventoryIds(inventoryIds);
        return tblInventoryMapper.deleteTblInventoryByIds(inventoryIds);
    }

    /**
     * 删除库存信息
     * 
     * @param inventoryId 库存ID
     * @return 结果
     */
    @Override
    public int deleteTblInventoryById(Long inventoryId)
    {
        tblInventoryMapper.deleteTblInventoryItemByInventoryId(inventoryId);
        return tblInventoryMapper.deleteTblInventoryById(inventoryId);
    }

    /**
     * 新增寄售行项目信息
     * 
     * @param tblInventory 库存对象
     */
    public void insertTblInventoryItem(TblInventory tblInventory)
    {
        List<TblInventoryItem> tblInventoryItemList = tblInventory.getDetail();
        Long inventoryId = tblInventory.getInventoryId();
        if (StringUtils.isNotNull(tblInventoryItemList))
        {
            List<TblInventoryItem> list = new ArrayList<TblInventoryItem>();
            for (TblInventoryItem tblInventoryItem : tblInventoryItemList)
            {
                tblInventoryItem.setInventoryId(inventoryId);
                list.add(tblInventoryItem);
            }
            if (list.size() > 0)
            {
                tblInventoryMapper.batchTblInventoryItem(list);
            }
        }
    }

    /**
     * 查询库存列表(接口专用)
     *
     * @param consignmentInventory 库存
     * @return 库存集合
     */
    @Override
    public List<TblInventory> selectTblInventoryFullList(TblInventory consignmentInventory){
        return tblInventoryMapper.selectTblInventoryFullList(consignmentInventory);
    }

    @Override
    public List<TblInventory> selectTblInventoryWithItemList(TblInventory inventory) {
        return tblInventoryMapper.selectTblInventoryWithItemList(inventory);
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId(){
        return tblInventoryMapper.selectLastId();
    }

    @Override
    public List<TblInventoryItem> selectTblInventoryItemList(TblInventoryItem inventoryItem) {
        return tblInventoryMapper.selectTblInventoryItemList(inventoryItem);
    }

    /**
     * 查询库存(不包含行项目)
     *
     * @param inventoryId 库存ID
     * @return 库存
     */
    @Override
    public TblInventory selectTblInventoryOnlyById(Long inventoryId) {
        TblInventory param = new TblInventory();
        param.setInventoryId(inventoryId);
        return tblInventoryMapper.selectTblInventoryOnlyById(param);
    }
}
