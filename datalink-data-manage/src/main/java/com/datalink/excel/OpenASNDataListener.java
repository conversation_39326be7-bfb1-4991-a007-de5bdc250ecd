package com.datalink.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.*;
import com.datalink.datamanage.service.ITblAsnService;
import com.datalink.datamanage.service.ITblOrderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Open ASN Excel导入监听器
 * 用于处理"4.6_購買計画及び検品_Open ASN"格式的Excel文件
 */
public class OpenASNDataListener extends AnalysisEventListener<Map<Integer, String>> {

    private final ITblOrderService tblOrderService;
    private final ITblAsnService tblAsnService;
    private static final Logger logger = LoggerFactory.getLogger(OpenASNDataListener.class);
    private List<TblAsn> asnList;
    private boolean isHeaderRow = true;
    private Map<Integer, String> headerMap;

    // 表头字段索引
    private int colDeliveryNoteNo = -1; // 納品書No
    private int colDeliveryDateTime = -1; // 納入日時
    private int colIssueNo = -1; // 発行No
    private int colDeliveryQty = -1; // 納入指示数量
    private int colDeliveryWeight = -1; // 納入指示重量
    private int colFromPlantCd = -1; // FROM_PLANT_CD
    private int colFromLineCls = -1; // FROM_LINE_CLS
    private int colFromPrc = -1; // FROM_PRC
    private int colActDeliYmd = -1; // ACT_DELI_YMD
    private int colDelisNo = -1; // DELIS_NO
    private int colResltCorFlg = -1; // RESLT_COR_FLG
    private int colRcvTrnsYmd = -1; // RCV_TRNS_YMD
    private int colOldPurchaseOrderNo = -1; // 旧購買伝票番号

    public OpenASNDataListener(ITblOrderService tblOrderService, ITblAsnService tblAsnService) {
        super();
        this.tblOrderService = tblOrderService;
        this.tblAsnService = tblAsnService;
        this.asnList = new ArrayList<>();
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // 获取当前行号
        int rowIndex = context.readRowHolder().getRowIndex();
        logger.debug("处理第 {} 行数据: {}", rowIndex, data);

        // 跳过空行
        if (isEmptyRow(data)) {
            logger.debug("第 {} 行为空行，跳过", rowIndex);
            return;
        }

        // 处理表头行
        if (isHeaderRow) {
            logger.info("处理表头行，行号: {}", rowIndex);
            processHeaderRow(data);
            validateHeaderColumns();
            isHeaderRow = false;
            return;
        }

        // 处理数据行
        processDataRow(data);
    }

    /**
     * 验证必要的表头列是否已找到
     */
    private void validateHeaderColumns() {
        List<String> missingColumns = new ArrayList<>();

        if (colDeliveryNoteNo == -1) missingColumns.add("納品書No(ASN)");
        if (colDeliveryDateTime == -1) missingColumns.add("納入日時");
        if (colIssueNo == -1) missingColumns.add("SAP PO.NO");
        if (colDeliveryQty == -1) missingColumns.add("納入指示数量（ASN交货数量）");

        if (!missingColumns.isEmpty()) {
            logger.warn("表头中缺少必要的列: {}", missingColumns);
        } else {
            logger.info("所有必要的表头列已找到");
        }

        // 记录找到的列索引
        logger.info("列索引映射: 納品書No={}, 納入日時={}, 発行No={}, 納入指示数量={}",
                colDeliveryNoteNo, colDeliveryDateTime, colIssueNo, colDeliveryQty);
    }

    /**
     * 处理表头行，找出各字段所在的列索引
     */
    private void processHeaderRow(Map<Integer, String> data) {
        headerMap = data;

        for (Map.Entry<Integer, String> entry : data.entrySet()) {
            int columnIndex = entry.getKey();
            String headerValue = entry.getValue();

            if (headerValue == null) {
                continue;
            }

            logger.debug("处理表头: 列={}, 值='{}'", columnIndex, headerValue.trim());

            switch (headerValue.trim()) {
                case "納品書No(ASN)":
                    colDeliveryNoteNo = columnIndex;
                    break;
                case "納入日時":
                    colDeliveryDateTime = columnIndex;
                    break;
                case "SAP PO.NO":
                    colIssueNo = columnIndex;
                    break;
                case "納入指示数量（ASN交货数量）":
                    colDeliveryQty = columnIndex;
                    break;
            }
        }
    }

    /**
     * 处理数据行，将每行数据转换为ASN对象
     */
    private void processDataRow(Map<Integer, String> data) {
        // 获取当前行的ASN编号和订单号
        String deliveryNoteNo = getValueOrEmpty(data, colDeliveryNoteNo);
        String deliveryDateTime = getValueOrEmpty(data, colDeliveryDateTime);
        String issueNo = getValueOrEmpty(data, colIssueNo);

        // 校验ASN编号是否已在数据库存在，如果存在则跳过
        TblAsn queryAsn = new TblAsn();
        queryAsn.setAsnCode(deliveryNoteNo);
        List<TblAsn> tblAsns = tblAsnService.selectTblAsnList(queryAsn);
        if (tblAsns != null && !tblAsns.isEmpty()) {
            logger.warn("ASN已存在，跳过处理: {}", deliveryNoteNo);
            return;
        }

        // 检查当前ASN是否已经存在于列表中
        TblAsn existingAsn = findAsnByCode(deliveryNoteNo);
        
        if (existingAsn != null) {
            // ASN已存在，添加新的行项目
            processOrderForExistingAsn(existingAsn, issueNo, data);
        } else {
            // 创建新ASN
            createNewAsn(deliveryNoteNo, deliveryDateTime, issueNo, data);
        }
    }

    /**
     * 根据ASN编号查找已有的ASN对象
     * @param asnCode ASN编号
     * @return 已存在的ASN对象，如不存在返回null
     */
    private TblAsn findAsnByCode(String asnCode) {
        if (StringUtils.isEmpty(asnCode)) {
            return null;
        }
        
        for (TblAsn asn : asnList) {
            if (asnCode.equals(asn.getAsnCode())) {
                return asn;
            }
        }
        return null;
    }

    /**
     * 处理已存在ASN的新订单
     * @param existingAsn 已存在的ASN对象
     * @param issueNo 订单号
     * @param data 当前行数据
     */
    private void processOrderForExistingAsn(TblAsn existingAsn, String issueNo, Map<Integer, String> data) {
        if (StringUtils.isEmpty(issueNo)) {
            logger.warn("订单号为空，无法处理该行数据");
            return;
        }
        
        // 查询订单信息
        TblOrder queryOrder = new TblOrder();
        queryOrder.setOrderCode(issueNo);
        List<TblOrder> orders = tblOrderService.selectTblOrderWithItemList(queryOrder);
        
        if (orders == null || orders.isEmpty()) {
            logger.warn("未找到订单: {}", issueNo);
            return;
        }
        
        TblOrder order = orders.get(0);
        
        // 创建ASN行项目
        TblAsnItem asnItem = new TblAsnItem();
        
        // 设置行项目信息
        asnItem.setOrderCode(order.getOrderCode());
        asnItem.setPlantCode(order.getPlantCode());
        asnItem.setPlantName(order.getPlantName());
        asnItem.setUnloadingNo(order.getDetail().get(0).getUnloadingNo());
        asnItem.setUnloadingName(order.getDetail().get(0).getUnloadingName());
        asnItem.setSendLocNo("");
        asnItem.setSendLocName("");
        asnItem.setRcvLocNo(order.getDetail().get(0).getStockLoc());
        asnItem.setRcvLocName("");
        
        // 创建物料信息
        List<TblAsnArticle> articles = new ArrayList<>();
        TblAsnArticle article = new TblAsnArticle();
        
        // 设置物料信息
        article.setArticleNo(order.getDetail().get(0).getArticleNo());
        article.setArticleName(order.getDetail().get(0).getArticleName());
        
        // 设置数量
        BigDecimal quantity = getNumberOrDefault(getValueOrEmpty(data, colDeliveryQty), BigDecimal.ONE);
        article.setQuantity(quantity);
        article.setUnit(order.getDetail().get(0).getUnit());
        
        // 设置包装信息
        article.setQtyPerPack(order.getDetail().get(0).getQtyPerPack());
        article.setPackQty(quantity.divide(order.getDetail().get(0).getQtyPerPack(), RoundingMode.UP));
        
        // 设置条码信息
        article.setStartWith("");
        article.setEndWith("");
        
        // 设置批次号
        article.setBatchNo(DateUtils.parseDateToStr("yyyyMMdd", existingAsn.getDeliveryDate()));
        
        // 设置非标准标志
        article.setNonStd("N");
        
        // 设置订单行项目
        article.setOrderLineNo(order.getDetail().get(0).getItemNo());
        
        // 添加物料到行项目
        articles.add(article);
        asnItem.setArticles(articles);
        
        // 将行项目添加到ASN
        existingAsn.getDetail().add(asnItem);
        
        // 更新dnNo，格式为：ASN编号-行索引
        updateDnNoForAllItems(existingAsn);
    }

    /**
     * 创建新的ASN对象
     * @param deliveryNoteNo ASN编号
     * @param deliveryDateTime 交货日期时间
     * @param issueNo 订单号
     * @param data 当前行数据
     */
    private void createNewAsn(String deliveryNoteNo, String deliveryDateTime, String issueNo, Map<Integer, String> data) {
        // 创建ASN对象
        TblAsn asn = new TblAsn();
        
        // 设置ASN基本信息
        asn.setAsnCode(deliveryNoteNo); // 使用納品書No作为ASN编号
        
        // 查询订单信息
        TblOrder queryOrder = new TblOrder();
        queryOrder.setOrderCode(issueNo);
        List<TblOrder> orders = tblOrderService.selectTblOrderWithItemList(queryOrder);
        
        if (orders == null || orders.isEmpty()) {
            logger.warn("未找到订单: {}", issueNo);
            return;
        }
        
        TblOrder order = orders.get(0);
        
        // 设置发货日期和计划交货日期
        if (!StringUtils.isEmpty(deliveryDateTime) && deliveryDateTime.length() >= 10) {
            // 从納入日時中提取日期部分(前10位 "yyyy-MM-dd")
            String deliveryDate = deliveryDateTime.substring(0, 10);
            asn.setDeliveryDate(DateUtils.parseDate(deliveryDate));
        }
        
        // 设置公司信息
        asn.setCompCode(order.getCompCode());
        
        // 设置供应商信息
        asn.setSuppCode(order.getSuppCode());
        asn.setSuppName(order.getSuppName());
        
        // 创建ASN行项目列表
        List<TblAsnItem> asnItems = new ArrayList<>();
        
        // 创建ASN行项目
        TblAsnItem asnItem = new TblAsnItem();
        
        // 设置行项目信息
        asnItem.setOrderCode(order.getOrderCode());
        asnItem.setPlantCode(order.getPlantCode());
        asnItem.setPlantName(order.getPlantName());
        asnItem.setUnloadingNo(order.getDetail().get(0).getUnloadingNo());
        asnItem.setUnloadingName(order.getDetail().get(0).getUnloadingName());
        asnItem.setSendLocNo("");
        asnItem.setSendLocName("");
        asnItem.setRcvLocNo(order.getDetail().get(0).getStockLoc());
        asnItem.setRcvLocName("");
        
        // 创建物料信息
        List<TblAsnArticle> articles = new ArrayList<>();
        TblAsnArticle article = new TblAsnArticle();
        
        // 设置物料信息
        article.setArticleNo(order.getDetail().get(0).getArticleNo());
        article.setArticleName(order.getDetail().get(0).getArticleName());
        
        // 设置数量
        BigDecimal quantity = getNumberOrDefault(getValueOrEmpty(data, colDeliveryQty), BigDecimal.ONE);
        article.setQuantity(quantity);
        article.setUnit(order.getDetail().get(0).getUnit());
        
        // 设置包装信息
        article.setQtyPerPack(order.getDetail().get(0).getQtyPerPack());
        article.setPackQty(quantity.divide(order.getDetail().get(0).getQtyPerPack(), RoundingMode.UP));
        
        // 设置条码信息
        article.setStartWith("");
        article.setEndWith("");
        
        // 设置批次号
        article.setBatchNo(DateUtils.parseDateToStr("yyyyMMdd", asn.getDeliveryDate()));
        
        // 设置非标准标志
        article.setNonStd("N");
        
        // 设置订单行项目
        article.setOrderLineNo(order.getDetail().get(0).getItemNo());
        
        // 添加物料到行项目
        articles.add(article);
        asnItem.setArticles(articles);
        
        // 添加行项目到ASN
        asnItems.add(asnItem);
        asn.setDetail(asnItems);
        
        // 设置dnNo，格式为：ASN编号-行索引
        updateDnNoForAllItems(asn);
        
        // 添加ASN到列表
        asnList.add(asn);
    }

    /**
     * 更新ASN中所有行项目的dnNo
     * @param asn ASN对象
     */
    private void updateDnNoForAllItems(TblAsn asn) {
        List<TblAsnItem> items = asn.getDetail();
        for (int i = 0; i < items.size(); i++) {
            items.get(i).setDnNo(asn.getAsnCode() + "-" + String.format("%04d", i + 1));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        logger.info("Excel解析完成，共生成 {} 条ASN记录", asnList.size());
        // 将ASN列表设置到上下文中
        analysisContext.readWorkbookHolder().setCustomObject(asnList);
    }
    
    /**
     * 判断是否为空行
     */
    private boolean isEmptyRow(Map<Integer, String> data) {
        if (data == null || data.isEmpty()) {
            return true;
        }
        
        for (String value : data.values()) {
            if (value != null && !value.trim().isEmpty()) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取单元格值，如果为空返回空字符串
     */
    private String getValueOrEmpty(Map<Integer, String> data, Integer key) {
        if (key == -1 || !data.containsKey(key)) {
            return "";
        }
        
        String value = data.get(key);
        return value != null ? value.trim() : "";
    }
    
    /**
     * 获取数字值，如果为空或非数字则返回默认值
     */
    private BigDecimal getNumberOrDefault(String value, BigDecimal defaultValue) {
        if (StringUtils.isEmpty(value)) {
            return defaultValue;
        }
        
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
} 