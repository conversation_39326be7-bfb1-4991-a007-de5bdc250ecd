<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TblMaterialPlantMapper">
    
    <resultMap type="TblMaterialPlant" id="TblMaterialPlantResult">
        <result property="materialPlantId"    column="Material_Plant_ID"    />
        <result property="materialId"    column="Material_ID"    />
        <result property="plantCode"    column="Plant_Code"    />
        <result property="packQuantity"    column="Pack_Quantity"    />
        <result property="createTime"    column="Create_Time"    />
        <result property="createBy"    column="Create_By"    />
        <result property="updateTime"    column="Update_Time"    />
        <result property="updateBy"    column="Update_By"    />
        <result property="delFlag"    column="Del_Flag"    />
    </resultMap>

    <sql id="selectTblMaterialPlantVo">
        select Material_Plant_ID, Material_ID, Plant_Code, Pack_Quantity, Create_Time, Create_By, Update_Time, Update_By, Del_Flag from tbl_material_plant
    </sql>

    <select id="selectTblMaterialPlantList" parameterType="TblMaterialPlant" resultMap="TblMaterialPlantResult">
        <include refid="selectTblMaterialPlantVo"/>
        <where>
            Del_Flag = '0'
            <if test="materialId != null"> and Material_ID = #{materialId}</if>
            <if test="plantCode != null  and plantCode != ''"> and Plant_Code = #{plantCode}</if>
        </where>
    </select>
    
    <select id="selectTblMaterialPlantById" parameterType="Long" resultMap="TblMaterialPlantResult">
        <include refid="selectTblMaterialPlantVo"/>
        where Material_Plant_ID = #{materialPlantId} and Del_Flag = '0'
    </select>
    
    <select id="selectTblMaterialPlantByMaterialId" parameterType="Long" resultMap="TblMaterialPlantResult">
        <include refid="selectTblMaterialPlantVo"/>
        where Material_ID = #{materialId} and Del_Flag = '0'
    </select>
    
    <insert id="insertTblMaterialPlant" parameterType="TblMaterialPlant" useGeneratedKeys="true" keyProperty="materialPlantId">
        insert into tbl_material_plant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="materialId != null">Material_ID,</if>
            <if test="plantCode != null">Plant_Code,</if>
            <if test="packQuantity != null">Pack_Quantity,</if>
            <if test="createTime != null">Create_Time,</if>
            <if test="createBy != null">Create_By,</if>
            <if test="updateTime != null">Update_Time,</if>
            <if test="updateBy != null">Update_By,</if>
            <if test="delFlag != null">Del_Flag,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="materialId != null">#{materialId},</if>
            <if test="plantCode != null">#{plantCode},</if>
            <if test="packQuantity != null">#{packQuantity},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="delFlag != null">#{delFlag},</if>
        </trim>
    </insert>
    
    <insert id="batchInsertTblMaterialPlant">
        insert into tbl_material_plant(Material_ID, Plant_Code, Pack_Quantity, Create_Time, Create_By, Del_Flag) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.materialId}, #{item.plantCode}, #{item.packQuantity}, #{item.createTime}, #{item.createBy}, '0')
        </foreach>
    </insert>
    
    <update id="updateTblMaterialPlant" parameterType="TblMaterialPlant">
        update tbl_material_plant
        <trim prefix="SET" suffixOverrides=",">
            <if test="materialId != null">Material_ID = #{materialId},</if>
            <if test="plantCode != null">Plant_Code = #{plantCode},</if>
            <if test="packQuantity != null">Pack_Quantity = #{packQuantity},</if>
            <if test="updateTime != null">Update_Time = #{updateTime},</if>
            <if test="updateBy != null">Update_By = #{updateBy},</if>
        </trim>
        where Material_Plant_ID = #{materialPlantId}
    </update>
    
    <update id="deleteTblMaterialPlantById" parameterType="Long">
        update tbl_material_plant set Del_Flag = '2' where Material_Plant_ID = #{materialPlantId}
    </update>
    
    <update id="deleteTblMaterialPlantByIds" parameterType="String">
        update tbl_material_plant set Del_Flag = '2' where Material_Plant_ID in 
        <foreach item="materialPlantId" collection="array" open="(" separator="," close=")">
            #{materialPlantId}
        </foreach>
    </update>
    
    <update id="deleteTblMaterialPlantByMaterialId" parameterType="Long">
        update tbl_material_plant set Del_Flag = '2' where Material_ID = #{materialId}
    </update>
</mapper> 