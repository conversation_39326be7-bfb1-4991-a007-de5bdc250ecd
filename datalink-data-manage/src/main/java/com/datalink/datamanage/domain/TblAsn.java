package com.datalink.datamanage.domain;

import java.util.List;
import java.util.Date;

import com.datalink.common.DataType;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;
import com.datalink.common.core.domain.BaseEntity;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * ASN对象 tbl_asn
 *
 * <AUTHOR>
 * @date 2021-08-04
 */
public class TblAsn extends BaseEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** ASN ID */
    private Long asnId;

    /** ASN编号 */
    @Excel(name = "ASN编号")
    @JsonAlias("asncode")
    @NotEmpty(message = "ASN代码不能为空")
    @Size(max = 30, message = "ASN代码超长")
    private String asnCode;

    /** 文档编号 */
    @Excel(name = "文档编号")
    private String docNo;

    /** 公司代码 */
    @Excel(name = "公司代码")
    @JsonAlias("compcode")
    @NotEmpty(message = "公司代码不能为空")
    @Size(max = 10, message = "公司代码超长")
    private String compCode;

    /** 供应商编号 */
    @Excel(name = "供应商编号")
    @JsonAlias("suppcode")
    @NotEmpty(message = "供应商代码不能为空")
    @Size(max = 40, message = "供应商代码超长")
    private String suppCode;

    /** 供应商名称 */
    @Excel(name = "供应商名称")
    @JsonAlias("suppname")
    @NotEmpty(message = "供应商名称不能为空")
    @Size(max = 40, message = "供应商名称超长")
    private String suppName;

    /** 文档日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "文档日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date docDate;

    /** 预计送达日期 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "预计送达日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonAlias("plandeliverydate")
    @NotNull(message = "预计送达日期不能为空")
    private Date planDeliveryDate;

    /** 发货日期 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC")
    @Excel(name = "发货日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonAlias("deliverydate")
    @NotNull(message = "发货日期不能为空")
    private Date deliveryDate;

    /** 收发标志 */
    @Excel(name = "收发标志")
    private String direction;

    /** KafKa发送状态 */
    @Excel(name = "KafKa发送状态")
    private String kafkaStatus;

    /** ASN行项目信息 */
    @Size(min=1, message = "行项目信息不能为空")
    private List<TblAsnItem> detail;

    public void setAsnId(Long asnId)
    {
        this.asnId = asnId;
    }

    public Long getAsnId()
    {
        return asnId;
    }
    public void setAsnCode(String asnCode)
    {
        this.asnCode = asnCode;
    }

    public String getAsnCode()
    {
        return asnCode;
    }
    public void setDocNo(String docNo)
    {
        this.docNo = docNo;
    }

    public String getDocNo()
    {
        return docNo;
    }
    public void setCompCode(String compCode)
    {
        this.compCode = compCode;
    }

    public String getCompCode()
    {
        return compCode;
    }
    public void setSuppCode(String suppCode)
    {
        this.suppCode = suppCode;
    }

    @Override
    public DataType getObjectType() {
        return DataType.ASN_TYPE;
    }

    public String getSuppCode()
    {
        return suppCode;
    }
    public void setSuppName(String suppName)
    {
        this.suppName = suppName;
    }

    public String getSuppName()
    {
        return suppName;
    }
    public void setDocDate(Date docDate)
    {
        this.docDate = docDate;
    }

    public Date getDocDate()
    {
        return docDate;
    }
    public void setPlanDeliveryDate(Date planDeliveryDate)
    {
        this.planDeliveryDate = planDeliveryDate;
    }

    public Date getPlanDeliveryDate()
    {
        return planDeliveryDate;
    }
    public void setDeliveryDate(Date deliveryDate)
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate()
    {
        return deliveryDate;
    }
    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }
    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    public List<TblAsnItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblAsnItem> detail)
    {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("asnId", getAsnId())
            .append("asnCode", getAsnCode())
            .append("docNo", getDocNo())
            .append("compCode", getCompCode())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("docDate", getDocDate())
            .append("planDeliveryDate", getPlanDeliveryDate())
            .append("deliveryDate", getDeliveryDate())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("tblAsnItemList", getDetail())
            .toString();
    }
}
