package com.datalink.datamanage.domain;

import java.io.Serializable;
import java.util.List;

public class DataStat implements Serializable {
    private static final long serialVersionUID = 1L;

    private String type;

    private List<DataStatItem> detail;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<DataStatItem> getDetail() {
        return detail;
    }

    public void setDetail(List<DataStatItem> detail) {
        this.detail = detail;
    }
}
