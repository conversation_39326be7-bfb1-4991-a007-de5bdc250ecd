<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="$t('system.user.profile.resetPwd.oldPassword')" prop="oldPassword">
      <el-input show-password v-model="user.oldPassword" :placeholder="$t('system.user.profile.resetPwd.oldPasswordPlaceholder')" type="password" />
    </el-form-item>
    <el-form-item :label="$t('system.user.profile.resetPwd.newPassword')" prop="newPassword">
      <el-input show-password v-model="user.newPassword" :placeholder="$t('system.user.profile.resetPwd.newPasswordPlaceholder')" type="password" />
    </el-form-item>
    <el-form-item :label="$t('system.user.profile.resetPwd.confirmPassword')" prop="confirmPassword">
      <el-input show-password v-model="user.confirmPassword" :placeholder="$t('system.user.profile.resetPwd.confirmPasswordPlaceholder')" type="password" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">{{ $t('system.user.profile.resetPwd.saveButton') }}</el-button>
      <el-button type="danger" size="mini" @click="close">{{ $t('system.user.profile.resetPwd.closeButton') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwd } from "@/api/system/user";

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error(this.$t('system.user.profile.resetPwd.passwordMismatch')));
      } else {
        callback();
      }
    };

    const validatePasswordStrength = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      }

      // Check minimum length
      if (value.length < 8) {
        callback(new Error(this.$t('system.user.profile.resetPwd.passwordMinLength')));
        return;
      }

      // Check for consecutive characters
      for (let i = 0; i < value.length - 2; i++) {
        if (value[i] === value[i + 1] && value[i] === value[i + 2]) {
          callback(new Error(this.$t('system.user.profile.resetPwd.noConsecutiveChars')));
          return;
        }
      }

      // Check character types
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumbers = /\d/.test(value);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value);
      const typesCount = [hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar].filter(Boolean).length;
      if (typesCount < 3) {
        callback(new Error(this.$t('system.user.profile.resetPwd.complexityRequirement')));
        return;
      }

      callback();
    };

    return {
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: this.$t('system.user.profile.resetPwd.oldPasswordRequired'), trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: this.$t('system.user.profile.resetPwd.newPasswordRequired'), trigger: "blur" },
          { validator: validatePasswordStrength, trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: this.$t('system.user.profile.resetPwd.confirmPasswordRequired'), trigger: "blur" },
          { validator: equalToPassword, trigger: "blur" }
        ]
      }
    };
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(
            response => {
              this.msgSuccess(this.$t('system.user.profile.resetPwd.updateSuccess'));
            }
          );
        }
      });
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/index" });
    }
  }
};
</script>
