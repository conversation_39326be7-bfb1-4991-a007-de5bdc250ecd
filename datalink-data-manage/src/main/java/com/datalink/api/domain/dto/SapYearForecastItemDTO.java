package com.datalink.api.domain.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
public class SapYearForecastItemDTO {
    @NotBlank(message = "取引先不能为空")
    @JsonProperty("FLIEF")
    private String flief;  // 取引先
    @JsonProperty("ZDEPOT")
    private String zdepot; // デポ
    @NotBlank(message = "取引先会社名不能为空")
    @JsonProperty("NAME1")
    private String name1;  // 取引先会社名
    @JsonProperty("SubItem")
    private List<SapYearForecastSubItemDTO> subItems;
} 