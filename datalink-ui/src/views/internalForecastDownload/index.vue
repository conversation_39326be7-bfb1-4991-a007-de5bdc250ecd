<!-- eslint-disable handle-callback-err -->
<template>
  <div class="app-container">
    <div class="card-header">
      <h2>{{ $t('internalForecast.title') }}</h2>
    </div>
    <div class="card-body">
      <el-row :gutter="20">
        <el-col :span="24">
          <!-- 搜索表单 -->
          <el-form :inline="true" class="query-form">
            <el-form-item required :label="$t('system.user.departmentLabel')">
              <el-select-tree
                v-model="client"
                node-key="code"
                :data="supplierTreeData"
                check-strictly
              />
            </el-form-item>
            <el-form-item v-if="!isCarrierWithDepot" :label="$t('supplyPlan.form.depot')">
              <!-- <el-select v-model="depot" clearable :placeholder="$t('supplyPlan.form.depotPlaceholder')">
                <el-option v-for="option in depotOptions" :label="option.dictLabel" :value="option.dictValue"></el-option>
              </el-select> -->
              <el-input
                v-model="depot"
                :placeholder="$t('supplyPlan.form.depotPlaceholder')"
                clearable
                @input="handleDepotInput"
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row :gutter="60">
        <!-- 每周数据按钮 -->
        <el-col :span="6" :offset="2" class="button-group">
          <el-badge v-if="downloadStatus.weekly" :value="$t('internalForecast.badge.undownloaded')" class="item">
            <el-button
              type="primary"
              :loading="loading"
              icon="el-icon-download"
              @click="download('weekly')"
            >
              {{ $t('internalForecast.buttons.downloadWeekly') }}
            </el-button>
          </el-badge>
          <el-button
            v-if="!downloadStatus.weekly"
            type="primary"
            :loading="loading"
            icon="el-icon-download"
            @click="download('weekly')"
          >
            {{ $t('internalForecast.buttons.downloadWeekly') }}
          </el-button>
          <el-button
            type="text"
            class="mt-1"
            icon="el-icon-document"
            @click="downloadTxt('weekly')"
          >
            {{ $t('internalForecast.buttons.downloadWeeklyTxt') }}
          </el-button>
        </el-col>

        <!-- 三个月数据按钮 -->
        <el-col :span="6" :offset="1" class="button-group">
          <el-badge v-if="downloadStatus.threeMonths" :value="$t('internalForecast.badge.undownloaded')" class="item">
            <el-button
              type="primary"
              icon="el-icon-download"
              @click="download('threeMonths')"
            >
              {{ $t('internalForecast.buttons.downloadThreeMonths') }}
            </el-button>
          </el-badge>
          <el-button
            v-if="!downloadStatus.threeMonths"
            type="primary"
            icon="el-icon-download"
            @click="download('threeMonths')"
          >
            {{ $t('internalForecast.buttons.downloadThreeMonths') }}
          </el-button>
          <el-button
            type="text"
            class="mt-1"
            icon="el-icon-document"
            @click="downloadTxt('threeMonths')"
          >
            {{ $t('internalForecast.buttons.downloadThreeMonthsTxt') }}
          </el-button>
        </el-col>

        <!-- 年度数据按钮 -->
        <el-col :span="6" :offset="1" class="button-group">
          <el-badge v-if="downloadStatus.yearly" :value="$t('internalForecast.badge.undownloaded')" class="item">
            <el-button
              type="primary"
              icon="el-icon-download"
              @click="download('yearly')"
            >
              {{ $t('internalForecast.buttons.downloadYearly') }}
            </el-button>
          </el-badge>
          <el-button
            v-if="!downloadStatus.yearly"
            type="primary"
            icon="el-icon-download"
            @click="download('yearly')"
          >
            {{ $t('internalForecast.buttons.downloadYearly') }}
          </el-button>
          <el-button
            type="text"
            class="mt-1"
            icon="el-icon-document"
            @click="downloadTxt('yearly')"
          >
            {{ $t('internalForecast.buttons.downloadYearlyTxt') }}
          </el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>

import { downloadForecastPdf, downloadForecastTxt } from '@/api/datamanage/internalForecast';
import { getLatestYearForecast, getLatestMonthForecast, getLatestWeekForecast } from '@/api/datamanage/forecast';
import ElSelectTree from "el-select-tree";
import { treeselect } from "@/api/system/dept";

export default {
  name: 'InternalForecastDownload',
  components: { ElSelectTree },
  data() {
    return {
      client: "",
      depot: "",
      supplierTreeData: [],
      depotOptions: [],
      loading: false,
      downloadStatus: {
        weekly: false,
        threeMonths: false,
        yearly: false
      },
      depotInputTimer: null
    }
  },
  computed: {
    isCarrierWithDepot() {
      const roles = this.$store.state.user.roles
      const remark = this.$store.state.user.remark
      return roles && roles.some(role => role === 'carrier') && remark
    }
  },
  watch: {
    client() {
      this.fetchAllDownloadStatus()
    },
    depot() {
      this.handleDepotInput()
    }
  },
  created() {
    this.getDicts('supply_plan_depot').then(response => {
      this.depotOptions = response.data
    })
    this.getSupplierTree()
  },
  methods: {
    getSupplierTree() {
      treeselect().then((res) => {
        this.supplierTreeData = res.data;
        this.client = this.supplierTreeData[0].code;
      })
    },
    async fetchAllDownloadStatus() {
      const params = {
        flief: this.client,
        zdepot: this.depot
      }
      try {
        const [yearRes, monthRes, weekRes] = await Promise.all([
          getLatestYearForecast(params),
          getLatestMonthForecast(params),
          getLatestWeekForecast(params)
        ])
        this.downloadStatus.yearly = yearRes.data
        this.downloadStatus.threeMonths = monthRes.data
        this.downloadStatus.weekly = weekRes.data
      } catch (error) {
        console.error('Failed to fetch download status:', error)
      }
    },
    download(type) {
      let url = '';
      switch (type) {
        case 'weekly':
          url = `/api/sapForecast/downloadWeekForecast?flief=${this.client}&zdepot=${this.depot}`;
          break;
        case 'threeMonths':
          url = `/api/sapForecast/downloadMonthForecast?flief=${this.client}&zdepot=${this.depot}`;

          break;
        case 'yearly':
          url = `/api/sapForecast/downloadYearForecast?flief=${this.client}&zdepot=${this.depot}`;

          break;
      }
      this.downloadFile(url);
    },

    downloadTxt(type) {
      let url = '';
      switch (type) {
        case 'weekly':
          url = `/api/sapForecast/downloadWeekForecastTxt?flief=${this.client}&zdepot=${this.depot}`;
          break;
        case 'threeMonths':
          url = `/api/sapForecast/downloadMonthForecastTxt?flief=${this.client}&zdepot=${this.depot}`;
          break;
        case 'yearly':
          url = `/api/sapForecast/downloadYearForecastTxt?flief=${this.client}&zdepot=${this.depot}`;
          break;
      }
      this.downloadTxtFile(url);
    },
    downloadFile(url) {
      this.loading = true;

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      const timeout = setTimeout(() => {
        loading.close();
      }, 15000);

      downloadForecastPdf(url)
        .then(response => {
          window.open(
            process.env.VUE_APP_BASE_API +
            "/common/downloadPdf?fileName=" +
            encodeURI(response.msg) +
            "&delete=false"
          );
          loading.close();
          this.loading = false;
          this.fetchAllDownloadStatus();
        })
        .catch((error) => {
          console.error(error);
          loading.close();
          this.loading = false;
        })
        .finally(() => {
          if (timeout) clearTimeout(timeout);
        });
    },

    downloadTxtFile(url) {
      this.loading = true;

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      const timeout = setTimeout(() => {
        loading.close();
      }, 15000);

      downloadForecastTxt(url)
        .then(response => {
          window.open(
            process.env.VUE_APP_BASE_API +
            "/common/download?fileName=" +
            encodeURI(response.msg) +
            "&delete=true"
          );
          loading.close();
          this.loading = false;
          this.fetchAllDownloadStatus();
        })
        .catch((error) => {
          console.error(error);
          loading.close();
          this.loading = false;
        })
        .finally(() => {
          if (timeout) clearTimeout(timeout);
        });
    },
    handleClientChange() {
      this.fetchAllDownloadStatus()
    },
    handleDepotInput() {
      if (this.depotInputTimer) {
        clearTimeout(this.depotInputTimer)
      }
      this.depotInputTimer = setTimeout(() => {
        this.fetchAllDownloadStatus()
      }, 1000)
    }
  }
};
</script>
<style scoped>
.app-container {
  padding: 24px
}

.card-header {
  padding: 0 48px
}

.card-body {
  padding: 0 48px
}

.el-button {
  width: 100%;
}

.mt-1 {
  margin-top: 4px;
}

.item {
  width: 100%;
  margin-bottom: 4px;
}

.el-badge {
  display: block;
}

:deep(.el-badge__content) {
  white-space: nowrap;
  transform: translateY(-50%);
  right: -15px;
  border: none;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
}
</style>
