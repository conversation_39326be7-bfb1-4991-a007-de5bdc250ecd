package com.datalink.datamanage.mapper;

import com.datalink.api.domain.SapWeekForecast;
import com.datalink.api.domain.SapWeekForecastItem;
import com.datalink.api.domain.SapWeekForecastItemDetail;
import com.datalink.api.domain.SapMonthForecast;
import com.datalink.api.domain.SapMonthForecastItem;
import com.datalink.api.domain.SapYearForecast;
import com.datalink.api.domain.SapYearForecastItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SapForecastMapper {
    
    /**
     * 插入周预测主表数据
     */
    int insertSapWeekForecast(SapWeekForecast forecast);
    
    /**
     * 插入周预测子项数据
     */
    int insertSapWeekForecastItem(SapWeekForecastItem item);
    
    /**
     * 批量插入周预测明细数据
     */
    int batchInsertWeekForecastItemDetails(@Param("details") List<SapWeekForecastItemDetail> details);
    
    /**
     * 获取最新的周预测数据
     */
    SapWeekForecast selectLatestWeekForecast(SapWeekForecast sapWeekForecast);
    
    /**
     * 根据预测ID获取子项列表
     */
    List<SapWeekForecastItem> selectWeekForecastItemsByForecastId(@Param("forecastId") Long forecastId);
    
    /**
     * 根据子项ID获取明细列表
     */
    List<SapWeekForecastItemDetail> selectWeekForecastItemDetailsByItemId(@Param("itemId") Long itemId);
    
    /**
     * 根据ID查询周预测数据
     */
    SapWeekForecast selectWeekForecastById(@Param("id") Long id);
    
    /**
     * 插入3个月预测主表数据
     */
    int insertSapMonthForecast(SapMonthForecast forecast);
    
    /**
     * 插入3个月预测子项数据
     */
    int insertSapMonthForecastItem(SapMonthForecastItem item);
    
    /**
     * 获取最新的3个月预测数据
     */
    SapMonthForecast selectLatestMonthForecast(SapMonthForecast sapMonthForecast);
    
    /**
     * 根据预测ID获取子项列表
     */
    List<SapMonthForecastItem> selectMonthForecastItemsByForecastId(@Param("forecastId") Long forecastId);
    
    /**
     * 根据ID查询3个月预测数据
     */
    SapMonthForecast selectMonthForecastById(@Param("id") Long id);
    
    /**
     * 保存年预测主表数据
     */
    int insertYearForecast(SapYearForecast forecast);
    
    /**
     * 保存年预测明细数据
     */
    int insertYearForecastItem(SapYearForecastItem item);
    
    /**
     * 查询最新年预测数据
     */
    SapYearForecast selectLatestYearForecast(SapYearForecast forecast);
    
    /**
     * 查询年预测明细数据
     */
    List<SapYearForecastItem> selectYearForecastItems(Long forecastId);
    
    /**
     * 更新周预测已读状态
     */
    int updateWeekForecastReadStatus(@Param("id") Long id, @Param("isRead") String isRead);
    
    /**
     * 更新月预测已读状态
     */
    int updateMonthForecastReadStatus(@Param("id") Long id, @Param("isRead") String isRead);
    
    /**
     * 更新年预测已读状态
     */
    int updateYearForecastReadStatus(@Param("id") Long id, @Param("isRead") String isRead);
} 