<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form :inline="true" class="search-form">
        <!-- <el-form-item :label="$t('loadProposalVehicle.function')">
            <el-select v-model="selectedFunction">
              <el-option :label="$t('loadProposalVehicle.functionSearch')" value="search"></el-option>
              <el-option :label="$t('loadProposalVehicle.functionStatusChange')" value="statusChange"></el-option>
              <el-option :label="$t('loadProposalVehicle.functionShipment')" value="shipment"></el-option>
            </el-select>
          </el-form-item> -->
        <el-form-item :label="$t('loadProposalVehicle.status')">
          <el-select clearable v-model="form.status">
            <el-option :label="$t('loadProposalVehicle.statusAll')" value=""></el-option>
            <el-option :label="$t('loadProposalVehicleTable.statusConfirmed')" value="Confirmed"></el-option>
            <el-option :label="$t('loadProposalVehicleTable.statusAssigned')" value="Assigned"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.materialFactor')">
          <el-input v-model="form.materialFactor" :placeholder="$t('loadProposalVehicle.placeholder.materialFactorPlaceholder')" clearable
          />
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.transportCompany')">
          <el-select clearable v-model="form.company">
            <el-option 
              v-for="item in postOptions" 
              :key="item.postId"
              :label="item.postName"
              :value="item.postId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.client')">
          <el-select-tree node-key="code" :data="supplierTreeData" v-model="form.clientCode" clearable
            check-strictly></el-select-tree>
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.pickupDate')">
          <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="form.pickupDate" type="datetimerange"
            :range-separator="$t('loadProposalVehicle.to')" :start-placeholder="$t('loadProposalVehicle.startDate')"
            :end-placeholder="$t('loadProposalVehicle.endDate')" />
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.deliveryDate')">
          <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="form.deliveryDate" type="datetimerange"
            :range-separator="$t('loadProposalVehicle.to')" :start-placeholder="$t('loadProposalVehicle.startDate')"
            :end-placeholder="$t('loadProposalVehicle.endDate')" />
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.depot')">
          <el-input v-model="form.depot" :placeholder="$t('loadProposalVehicle.placeholder.depot')" clearable/>
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.factory')">
          <el-input v-model="form.factory" :placeholder="$t('loadProposalVehicle.placeholder.factory')" clearable />
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.deliveryLocation')">
          <el-input v-model="form.deliveryLocation" :placeholder="$t('loadProposalVehicle.placeholder.deliveryLocation')" clearable />
        </el-form-item>
        <el-form-item :label="$t('loadProposalVehicle.ticketNo')">
          <el-input v-model="form.ticketNo" :placeholder="$t('loadProposalVehicle.placeholder.ticketNo')" clearable />
        </el-form-item>
        <!-- Buttons -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="searchData">{{ $t('loadProposalVehicle.search')
            }}</el-button>
          <!-- <el-button type="success" @click="reloadPage">{{ $t('loadProposalVehicle.reload') }}</el-button> -->
          <el-button icon="el-icon-refresh" @click="clearFields">{{ $t('loadProposalVehicle.clear')
            }}</el-button>
          <el-button v-hasPermi="['loadProposalVehicleRegistration:add']" v-if="isEditable" type="primary" icon="el-icon-plus" @click="addRow" style="margin-bottom: 10px;">
            {{ $t('loadProposalVehicle.addRow') }}
          </el-button>
          <el-button v-hasPermi="['loadProposalVehicleRegistration:remove']" v-if="isEditable" type="danger" icon="el-icon-delete" @click="deleteSelectedRows"
            style="margin-bottom: 10px;" :disabled="selectedRows.length === 0">
            {{ $t('loadProposalVehicle.deleteSelected') }}
          </el-button>
        </el-form-item>
      </el-form>
      <!-- 表格 -->

      <el-table v-loading="loading" v-if="selectedFunction" :data="tableData" border stripe :cell-style="getCellStyle"
        @selection-change="handleSelectionChange">
        <el-table-column align="center" fixed type="selection"></el-table-column>
        <el-table-column width="60px" align="center"  prop="transportPlanId"
          :label="$t('loadProposalVehicleTable.transportPlanId')"></el-table-column>

        <el-table-column width="120px" align="center"  prop="status"
          :label="$t('loadProposalVehicleTable.status')">
          <template v-slot="scope">
            <el-select v-if="isEditable && showCarrier && !scope.row.isAssigned" v-model="scope.row.status"
              @change="handleRowEdit(scope.row)">
              <el-option :label="$t('loadProposalVehicleTable.statusConfirmed')" value="Confirmed"></el-option>
              <!-- <el-option :label="$t('loadProposalVehicleTable.statusConfirmed')" value="Confirmed"></el-option> -->
            </el-select>
            <span v-else>{{ scope.row.status ? $t('loadProposalVehicleTable.status' + scope.row.status) : '' }}</span>
          </template>
        </el-table-column>

        <el-table-column width="80px" align="center"  prop="materialFactor"
          :label="$t('loadProposalVehicleTable.materialFactor')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.materialFactor"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.materialFactor }}</span>
          </template>
        </el-table-column>

        <el-table-column width="120px" align="center"  prop="way" :label="$t('loadProposalVehicleTable.way')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.way"
            @input="handleRowEdit(scope.row)" />
            <!-- <el-select v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.way"
              @change="handleRowEdit(scope.row)">
              <el-option v-for="wayType of wayTypes" :label="wayType.dictLabel" :value="wayType.dictValue"></el-option>
            </el-select> -->
            <!-- <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.way"
              @input="handleRowEdit(scope.row)" /> -->
            <span v-else>{{ scope.row.way }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" width="340px"  :label="$t('loadProposalVehicleTable.pickupDateTime')">
          <el-table-column width="180px" align="center" prop="pickupDate"
            :label="$t('loadProposalVehicleTable.pickupDate')">
            <template v-slot="scope">
              <el-date-picker class="date-picker-class" v-if="isEditable && !scope.row.isAssigned"
                v-model="scope.row.pickupDate" type="date" @change="handleRowEdit(scope.row)"
                value-format="yyyy-MM-dd" />
              <!-- <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.pickupDate" @input="handleRowEdit(scope.row)" /> -->
              <span v-else>{{ scope.row.pickupDate }}</span>
            </template>
          </el-table-column>

          <el-table-column width="160px" align="center" prop="pickupTime"
            :label="$t('loadProposalVehicleTable.pickupTime')">
            <template v-slot="scope">
              <el-time-picker class="time-picker-class" @change="handleRowEdit(scope.row)"
                v-if="isEditable && !scope.row.isAssigned" value-format="HH:mm:ss" v-model="scope.row.pickupTime">
              </el-time-picker>
              <!-- <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.pickupTime" @input="handleRowEdit(scope.row)" /> -->
              <span v-else>{{ scope.row.pickupTime }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column width="180px" align="center" prop="clientCode"
          :label="$t('loadProposalVehicle.client')">
          <template v-slot="scope">
            <el-select-tree
              v-if="isEditable && !scope.row.isAssigned && supplierTreeData && supplierTreeData.length"
              node-key="code"
              :data="supplierTreeData"
              v-model="scope.row.clientCode"
              clearable
              @change="handleRowEdit(scope.row)"
              check-strictly>
            </el-select-tree>
            <span v-else>{{ getSupplierName(scope.row.clientCode, supplierTreeData) }}</span>
          </template>
        </el-table-column>

        <el-table-column width="80px" align="center" prop="depot" :label="$t('loadProposalVehicleTable.depot')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.depot"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.depot }}</span>
          </template>
        </el-table-column>

        <el-table-column width="120px" align="center" prop="deliveryLocation"
          :label="$t('loadProposalVehicleTable.deliveryLocation')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.deliveryLocation"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.deliveryLocation }}</span>
          </template>
        </el-table-column>

        <el-table-column width="100px" align="center" prop="factory" :label="$t('loadProposalVehicleTable.factory')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.factory"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.factory }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" :label="$t('loadProposalVehicleTable.deliveryDateTime')">
          <el-table-column width="180px" align="center" prop="deliveryDate"
            :label="$t('loadProposalVehicleTable.deliveryDate')">
            <template v-slot="scope">
              <el-date-picker class="date-picker-class" v-if="isEditable && !scope.row.isAssigned"
                v-model="scope.row.deliveryDate" type="date" @change="handleRowEdit(scope.row)"
                value-format="yyyy-MM-dd" />
              <!-- <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.deliveryDate" @input="handleRowEdit(scope.row)" /> -->
              <span v-else>{{ scope.row.deliveryDate }}</span>
            </template>
          </el-table-column>

          <el-table-column width="160px" align="center" prop="deliveryTime"
            :label="$t('loadProposalVehicleTable.deliveryTime')">
            <template v-slot="scope">
              <el-time-picker class="time-picker-class" @change="handleRowEdit(scope.row)"
                v-if="isEditable && !scope.row.isAssigned" value-format="HH:mm:ss" v-model="scope.row.deliveryTime">
              </el-time-picker>
              <!-- <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.deliveryTime" @input="handleRowEdit(scope.row)" /> -->
              <span v-else>{{ scope.row.deliveryTime }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column width="120px" align="center" prop="port" :label="$t('loadProposalVehicleTable.port')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.port"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.port }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" :label="$t('loadProposalVehicleTable.weight')">
          <el-table-column width="120px" align="center" prop="weightTotal"
            :label="$t('loadProposalVehicleTable.weightTotal')">
            <template v-slot="scope">
              <!-- <el-input-number v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.weightTotal" @input="handleRowEdit(scope.row)" :precision="2" :step="1"></el-input-number> -->

              <el-input type="number" v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.weightTotal"
                @input="handleRowEdit(scope.row)" />
              <span v-else>{{ scope.row.weightTotal }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" :label="$t('loadProposalVehicleTable.palletQuantity')">
            <el-table-column width="120px" align="center" prop="totalQuantity"
              :label="$t('loadProposalVehicleTable.totalQuantity')">
              <template v-slot="scope">
                <el-input type="number" v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.totalQuantity"
                  @input="handleRowEdit(scope.row)" />
                <span v-else>{{ scope.row.totalQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column width="120px" align="center" prop="average"
              :label="$t('loadProposalVehicleTable.average')">
              <template v-slot="scope">
                <el-input type="number" v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.average"
                  @input="handleRowEdit(scope.row)" />
                <span v-else>{{ scope.row.average }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>

        <el-table-column width="120px" align="center" prop="ticketNo" :label="$t('loadProposalVehicleTable.ticketNo')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.ticketNo"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.ticketNo }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" :label="$t('loadProposalVehicleTable.transportPlan')">
          <el-table-column width="180px" align="center" prop="company" :label="$t('loadProposalVehicleTable.company')">
            <template v-slot="scope">

                <el-select
                  v-if="isEditable && !scope.row.isAssigned"
                  :value="scope.row.company ? Number(scope.row.company):''"
                  @input="val => { scope.row.company = String(val); handleRowEdit(scope.row); }"
                  :placeholder="$t('system.user.positionPlaceholder')"
                >
                  <el-option
                    v-for="item in postOptions"
                    :key="item.postId"
                    :label="item.postName"
                    :value="item.postId"
                    :disabled="item.status == 1"
                  ></el-option>
                </el-select>
              <span v-else>{{ scope.row.company }}</span>
            </template>
          </el-table-column>
          <el-table-column width="160px" align="center" prop="carrierPickupTime"
            :label="$t('loadProposalVehicleTable.pickupTimeShort')">
            <template v-slot="scope">
              <el-time-picker v-if="isEditable && !scope.row.isAssigned && showCarrier" class="time-picker-class"
                @change="handleRowEdit(scope.row)" value-format="HH:mm:ss" v-model="scope.row.carrierPickupTime">
              </el-time-picker>
              <!-- <el-input v-if="isEditable && !scope.row.isAssigned" v-model="scope.row.pickupTime" @input="handleRowEdit(scope.row)" /> -->
              <span v-else>{{ scope.row.carrierPickupTime }}</span>
            </template>
          </el-table-column>
          <el-table-column width="120px" align="center" prop="pickupQuantity"
            :label="$t('loadProposalVehicleTable.pickupQuantity')">
            <template v-slot="scope">
              <el-input v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.pickupQuantity"
                @input="handleRowEdit(scope.row)" />
              <span v-else>{{ scope.row.pickupQuantity }}</span>
            </template>
          </el-table-column>
          <el-table-column width="140px" align="center" prop="carType" :label="$t('loadProposalVehicleTable.carType')">
            <template v-slot="scope">
              <el-select v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.carType"
                @change="handleRowEdit(scope.row)">
                <el-option 
                  v-for="carType in carTypes" 
                  :key="carType.dictValue"
                  :label="carType.dictLabel"
                  :value="carType.dictValue">
                </el-option>
                <!-- <el-option :label="$t('loadProposalVehicleTable.mediumCar')" value="mediumCar"></el-option> -->
                <!-- <el-option :label="$t('loadProposalVehicleTable.largeCar')" value="largeCar"></el-option> -->
              </el-select>
              <span v-else>{{ scope.row.carType ? $t('loadProposalVehicleTable.' + scope.row.carType) : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column width="120px" align="center" prop="carNo" :label="$t('loadProposalVehicleTable.carNo')">
            <template v-slot="scope">
              <el-input v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.carNo"
                @input="handleRowEdit(scope.row)" />
              <span v-else>{{ scope.row.carNo }}</span>
            </template>
          </el-table-column>
          <el-table-column width="120px" align="center" prop="driver" :label="$t('loadProposalVehicleTable.driver')">
            <template v-slot="scope">
              <el-input v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.driver"
                @input="handleRowEdit(scope.row)" />
              <span v-else>{{ scope.row.driver }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column width="120px" align="center" prop="deliveryLocationName"
          :label="$t('loadProposalVehicleTable.deliveryLocationName')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.deliveryLocationName"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.deliveryLocationName }}</span>
          </template>
        </el-table-column>

        <el-table-column width="144px" align="center" prop="companyName"
          :label="$t('loadProposalVehicleTable.companyName')">
          <template v-slot="scope">
            <el-input v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.companyName"
              @input="handleRowEdit(scope.row)" />
            <span v-else>{{ scope.row.companyName }}</span>
          </template>
        </el-table-column>

        <el-table-column align="center" :label="$t('loadProposalVehicleTable.recordMaintenance')">
          <el-table-column align="center" :label="$t('loadProposalVehicleTable.registrationInfo')">
            <el-table-column width="180px" align="center" prop="createTime"
              :label="$t('loadProposalVehicleTable.createTime')">
              <template v-slot="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column width="120px" align="center" prop="createBy"
              :label="$t('loadProposalVehicleTable.createBy')">
              <template v-slot="scope">
                <span>{{ scope.row.createBy }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" :label="$t('loadProposalVehicleTable.updateInfo')">
            <el-table-column width="180px" align="center" prop="updateTime"
              :label="$t('loadProposalVehicleTable.updateTime')">
              <template v-slot="scope">

                <span>{{ scope.row.updateTime }}</span>
              </template>
            </el-table-column>
            <el-table-column width="120px" align="center" prop="updateBy"
              :label="$t('loadProposalVehicleTable.updateBy')">
              <template v-slot="scope">
                <!-- <el-input v-if="isEditable && !scope.row.isAssigned && showCarrier" v-model="scope.row.updateBy"
                  @input="handleRowEdit(scope.row)" /> -->
                <span>{{ scope.row.updateBy }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>

        <el-table-column v-if="isEditable" fixed="right" align="center" :label="$t('loadProposalVehicleTable.actions')"
          width="100px">
          <template v-slot="scope">
            <el-button v-if="!scope.row.isAssigned" link type="success" size="small" @click="handleConfirm(scope.row)"
              :disabled="!scope.row.editable">
              {{ $t('loadProposalVehicleTable.confirm') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="form.pageNum" :limit.sync="form.pageSize"
      @pagination="query" />
    </el-card>
  </div>
</template>

<script>
import { add, query, remove, update } from "@/api/datamanage/vehicleRegistration";
import { treeselect } from "@/api/system/dept";
import ElSelectTree from "el-select-tree";

import {
  getCarrier,
} from "@/api/system/user";

export default {
  name: 'LoadProposalVehicleRegistration',
  components: { ElSelectTree },
  data() {
    return {
      loading: true,
      selectedFunction: "shipment",
      selectedClient: '',
      showCarrier: false,
      total: 0,
      form: {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: 'createTime',
        isAsc: 'desc',
        isComplete: '',
        params: {
        }
      },
      transportCompanys: [
        { clientId: '2230', clientName: '日本本社' },
        { clientId: '2231', clientName: '佐野工場' },
        { clientId: '2233', clientName: '下拝田工場' },
        { clientId: '2234', clientName: '栃木工場' },
        { clientId: '2236', clientName: '東海工場' },
        { clientId: '223A', clientName: 'みやこ外部倉庫' },
        { clientId: '223B', clientName: '鈴与外部倉庫' },
        { clientId: '223X', clientName: '佐野試作工場' },
      ],

      form2: {
        status: "荷確",
        depot: "02",
        materialFactor: "P",
        way: "G",
        clientCode: "BS100",
        deliveryLocation: "HS134",
        factory: "F101",
        pickupDate: "10/11/08",
        pickupTime: "14:30",
        deliveryDate: "10/11/09",
        deliveryTime: "15:00",
        port: "P01",
        weightTotal: "20",
        totalQuantity: "100",
        average: "2",
        ticketNo: "T1234",
        company: "A4027",
        pickupQuantity: "20",
        carType: "medium",
        carNo: "ABC123",
        driver: "山田太郎",
        deliveryLocationName: "佐野工場",
        companyName: "臼杵運送株式会社",
        createDate: "2024-10-01",
        createTime: "12:00",
        createBy: "user001",
        updateDate: "2024-10-02",
        updateTime: "13:00",
        updateBy: "user002",
      },
      tableData: [
      ],
      newRowTemplate: {
        no: null,
        select: "",
        status: "Confirmed",
        materialFactor: "",
        way: "",
        clientCode: "",
        depot: "",
        deliveryLocation: "",
        factory: "",
        pickupDate: "",
        pickupTime: "",
        deliveryDate: "",
        deliveryTime: "",
        port: "",
        weightTotal: "",
        totalQuantity: "",
        average: "",
        ticketNo: "",
        company: "",
        carrierPickupTime: "",
        pickupQuantity: "",
        carType: "",
        carNo: "",
        driver: "",
        deliveryLocationName: "",
        companyName: "",
        createDate: "",
        createTime: "",
        createBy: "",
        updateDate: "",
        updateTime: "",
        updateBy: "",
      },
      selectedRows: [],
      rowEdited: false,
      lastEditedRowNo: null,
      carTypes: [],
      wayTypes: [],
      supplierTreeData: [], // Initialize supplierTreeData with an empty array
      // 岗位选项
      postOptions: [],
    };
  },
  created() {

    // this.query();
    this.getDicts('load_proposal_vehicle_carType').then(res => {
      this.carTypes = res.data
    })

    this.getDicts('load_proposal_vehicle_way').then(res => {
      this.wayTypes = res.data
    })
    this.getSupplierTree();
    this.getPosts();
  },
  computed: {
    isEditable() {
      return this.selectedFunction === "shipment";
    },
    isDraft(row) {
      return row.status === "Draft";
    },
    isButtonDisabled() {
      return !this.rowEdited;
    },
    dept() {
      return this.$store.state.user.dept;
    },
  userId() {
    return this.$store.state.user.userId; // Adjust the path based on your store structure
  }
  },
  methods: {
    getPosts(){
      getCarrier().then(response => {
        this.postOptions = response.data || [];
      });
    },
    getSupplierTree() {
      treeselect().then((res) => {
        console.log('Supplier Tree Data:', res.data); // Added console log here
        this.supplierTreeData = res.data;
        if (this.supplierTreeData && this.supplierTreeData.length > 0) {
          this.client = this.supplierTreeData[0].code;
        }
        this.query();
      })
    },
    async query() {
      try {

        if (this.form.pickupDate && this.form.pickupDate.length) {
          this.form.params['pickupDateBegin'] = this.form.pickupDate[0]
          this.form.params['pickupDateEnd'] = this.form.pickupDate[1]
        }

        if (this.form.deliveryDate && this.form.deliveryDate.length) {
          this.form.params['deliveryDateBegin'] = this.form.deliveryDate[0]
          this.form.params['deliveryDateEnd'] = this.form.deliveryDate[1]
        }
        const queryForm = {
          ...this.form
        }
        delete queryForm.pickupDate;
        delete queryForm.deliveryDate;
        const requestData = {
          ...queryForm
        };
        this.loading = true
        const response = await query(requestData);

        if (response.code === 200) {
          response.rows.map(d => {
            if (d.status === 'Assigned') {
              d['isAssigned'] = true;
            } else {
              d['isAssigned'] = false;
            }
            return d
          })
          this.tableData = response.rows;
          this.total = response.total
          this.originalData = JSON.parse(JSON.stringify(response.rows));
        }

        this.loading = false
      } catch (error) {
        this.loading = false
        console.error("error:", error);
      }
    },
    getCellStyle({ row, column }) {
      return {
        cursor: this.isEditable ? "text" : "not-allowed",
        backgroundColor: this.selectedFunction === "statusChange" ? "#f5f7fa" : "",
      };
    },
    addRow() {
      if (this.lastEditedRowNo && this.rowEdited) {
        this.$message({
          type: 'warning',
          message: this.$t('loadProposalVehicle.addRowWarning')
        });
        // 滚动到底部
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth'
        });
        return;
      }

      const maxNo = this.tableData.reduce((max, row) => (row.transportPlanId > max ? row.transportPlanId : max), 1);
      const newRow = { ...this.newRowTemplate, no: maxNo + 1, editable: true };
      this.tableData.forEach(row => row.editable = false);
      this.tableData.push(newRow);
      this.lastEditedRowNo = newRow.transportPlanId||maxNo + 1;
      this.rowEdited = true;

      // 添加新行后滚动到底部
      this.$nextTick(() => {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth'
        });
      });
    },
    isEqualIgnoringEditable(obj1, obj2) {
      if (obj1 === obj2) return true;
      if (typeof obj1 !== 'object' || typeof obj2 !== 'object' || obj1 === null || obj2 === null) return false;

      const keys1 = Object.keys(obj1).filter(key => key !== 'editable');
      const keys2 = Object.keys(obj2).filter(key => key !== 'editable');

      // 如果属性数量不同，则对象不相等
      if (keys1.length !== keys2.length) return false;

      // 比较每个属性
      for (const key of keys1) {
        if (!keys2.includes(key) || !this.isEqualIgnoringEditable(obj1[key], obj2[key])) {
          return false;
        }
      }
      return true;
    },

    handleRowEdit(row) {
      console.log('Current row:', row);
      const originalRow = this.originalData.find((r) => r.transportPlanId === row.transportPlanId);
      console.log('Original row:', originalRow);
      // 使用新的 isEqualIgnoringEditable 函数进行比较
      const hasChanged = !this.isEqualIgnoringEditable(row, originalRow);
      console.log('Has changed:', hasChanged);

      // 新按钮状态
      this.rowEdited = hasChanged;
      this.lastEditedRowNo = hasChanged ? row.transportPlanId : null;
      this.$set(row, 'editable', hasChanged);

    },

    handleConfirm(row) {

      this.add(row);
    },
    async add(row) {
      const rowData = this.lastEditedRowNo ? this.tableData.find(d => d.transportPlanId === this.lastEditedRowNo) : row
      try {
        const requestData = {
          ...Object.fromEntries(
            Object.entries({
              ...rowData
            }).filter(
              ([key, value]) =>
                value !== "" && // 非空字符串
                value !== null && // 非null
                value !== undefined && // 非undefined
                !(typeof value === "object" && !Array.isArray(value) && Object.keys(value).length === 0) // 非空对象
            )
          )
        };
        let response
        if (rowData.transportPlanId) {
          response = await update(requestData);
        } else {
          response = await add(requestData);
        }
        if (response.code === 200) {
          // this.query();
          if (rowData.transportPlanId) {
            this.msgSuccess(this.$t('order.success.update'))
          } else {
            this.msgSuccess(this.$t('order.success.add'))
          }
          row.editable = false; // 确认后禁用该行的确认按钮
          this.rowEdited = false;
          this.lastEditedRowNo = null;
        }
      } catch (error) {
        console.error("error:", error);
      }

    },
    async deleteSelectedRows() {
      const ids = this.selectedRows.filter(row => !!row.transportPlanId).map(row => row.transportPlanId);
      this.$confirm(this.$t('order.confirm.delete', { ids }), this.$t('order.button.warning'), {
        confirmButtonText: this.$t('order.button.confirm'),
        cancelButtonText: this.$t('order.button.cancel'),
        type: 'warning'
      })
        .then(() => {
          if (ids.length > 0) {
            remove(ids)
          } else {
            this.tableData = this.tableData.filter(row => !this.selectedRows.includes(row));
            this.originalData = this.originalData.filter((row) => !this.selectedRows.includes(row));

            this.selectedRows = [];
          }
        })
        .then(() => {
          setTimeout(() => {
            this.query()
            this.msgSuccess(this.$t('order.success.delete'))
            this.selectedRows = [];
            this.lastEditedRowNo = null;
            this.rowEdited = false;
          }
          )
        })


    },
    handleSelectionChange(val) {
      this.selectedRows = val;
    },
    clearFields() {
      this.form = {
        params: {
        }
      }
      this.query();

    },
    reloadPage() {

    },
    searchData() {
      this.query();

    },

    handleSelectChange(value) {
      console.log(222)
      this.limitSelection(value, this.scope.row);
      this.handleRowEdit(this.scope.row);
    },
    limitSelection(value,row) {
      if (value.length > 1) {
        row.postIds = [value[value.length - 1]];
      }
    },

    getSupplierName(clientCode, supplierTreeData) {
      const findSupplier = (nodes) => {
        for (const node of nodes) {
          if (String(node.code) === String(clientCode)) {
            return node.label;
          }
          if (node.children) {
            const childResult = findSupplier(node.children);
            if (childResult) {
              return childResult;
            }
          }
        }
        return null;
      };
      return findSupplier(supplierTreeData) || clientCode;
    },
  },
};
</script>

<style scoped>
.date-picker-class {
  width: 140px
}

.time-picker-class {
  width: 120px
}

.app-container {
  padding: 20px;
}

.card-header {
  text-align: center;
  margin-bottom: 20px;
}

.buttons {
  margin-top: 20px;
  text-align: center;
}

.pagination {
  text-align: right;
  margin-top: 20px;
}

.pagination span {
  margin: 0 10px;
}
</style>
