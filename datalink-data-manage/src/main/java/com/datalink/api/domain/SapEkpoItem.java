package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

/**
 * SAP订单行项目DTO
 */
@Setter
@Getter
public class SapEkpoItem {

    /** 采购订单行项目编号 */
    @JsonProperty("EBELP")
    private String EBELP;

    /** 删除标识 */
    @JsonProperty("LOEKZ")
    private String LOEKZ;

    /** 物料编号 */
    @JsonProperty("MATNR")
    private String MATNR;

    /** 物料描述 */
    @JsonProperty("TXZ01")
    private String TXZ01;

    /** 工厂 */
    @JsonProperty("WERKS")
    private String WERKS;

    /** 库存地点 */
    @JsonProperty("LGORT")
    private String LGORT;

    /** 订单数量 */
    @JsonProperty("MENGE")
    private String MENGE;

    /** 订单单位 */
    @JsonProperty("MEINS")
    private String MEINS;

    /** 交货计划 */
    @JsonProperty("EKET")
    @Valid
    private List<SapEketItem> EKET;

    @Override
    public String toString() {
        return "SapEkpoItem{" +
                "EBELP='" + EBELP + '\'' +
                ", LOEKZ='" + LOEKZ + '\'' +
                ", MATNR='" + MATNR + '\'' +
                ", TXZ01='" + TXZ01 + '\'' +
                ", WERKS='" + WERKS + '\'' +
                ", LGORT='" + LGORT + '\'' +
                ", MENGE='" + MENGE + '\'' +
                ", MEINS='" + MEINS + '\'' +
                ", EKET=" + EKET +
                '}';
    }
}
