import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import ParentView from '@/components/ParentView'

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: resolve => require(['@/views/redirect'], resolve)
      }
    ]
  },
  {
    path: '/login',
    component: resolve => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: resolve => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: resolve => require(['@/views/error/401'], resolve),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: resolve => require(['@/views/index_data'], resolve),
        name: '首页',
        meta: { title: '首页', icon: 'dashboard', noCache: true, affix: true }
      }
    ]
  },

  {
    path: 'paymentPlan',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: resolve =>
          require(['@/views/system/user/profile/index'], resolve),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: resolve =>
          require(['@/views/system/user/profile/index'], resolve),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  },
  {
    path: '/dict',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'type/data/:dictId(\\d+)',
        component: resolve => require(['@/views/system/dict/data'], resolve),
        name: 'Data',
        meta: { title: '字典数据', icon: '' }
      }
    ]
  },
  {
    path: '/job',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'log',
        component: resolve => require(['@/views/monitor/job/log'], resolve),
        name: 'JobLog',
        meta: { title: '调度日志' }
      }
    ]
  },
  {
    path: '/gen',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'edit/:tableId(\\d+)',
        component: resolve => require(['@/views/tool/gen/editTable'], resolve),
        name: 'GenEdit',
        meta: { title: '修改生成配置' }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:orderId',
        component: resolve =>
          require(['@/views/datamanage/order/detail'], resolve),
        name: 'OrderDetail',
        meta: { title: '订单详情' }
      }
    ]
  },
  {
    path: '/forecast',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:forecastId',
        component: resolve =>
          require(['@/views/datamanage/forecast/detail'], resolve),
        name: 'ForecastDetail',
        meta: { title: '预测详情' }
      }
    ]
  },
  {
    path: '/consignment',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:consignmentId',
        component: resolve =>
          require(['@/views/datamanage/consignment/detail'], resolve),
        name: 'ConsignmentDetail',
        meta: { title: '寄售库存详情' }
      }
    ]
  },
  {
    path: '/inventory',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:inventoryId',
        component: resolve =>
          require(['@/views/datamanage/inventory/detail'], resolve),
        name: 'InventoryDetail',
        meta: { title: '库存详情' }
      }
    ]
  },
  {
    path: '/feedback',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:feedbackId',
        component: resolve =>
          require(['@/views/datamanage/feedback/detail'], resolve),
        name: 'FeedbackDetail',
        meta: { title: '收货反馈详情' }
      }
    ]
  },
  {
    path: '/asn',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'detail/:asnId',
        component: resolve =>
          require(['@/views/datamanage/asn/detail'], resolve),
        name: 'AsnDetail',
        meta: { title: 'ASN详情' }
      },
      {
        path: 'edit',
        component: resolve => require(['@/views/datamanage/asn/edit'], resolve),
        name: 'AsnEdit',
        meta: { title: 'ASN编辑' }
      }
    ]
  },
  {
    path: '/internalForecastDownload',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        component: resolve =>
          require(['@/views/internalForecastDownload/index'], resolve),
        name: 'internalForecastDownload',
        meta: { title: '内部预测' }
      }
    ]
  },
  {
    path: '/loadProposalVehicleRegistration',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        name: 'loadProposalVehicleRegistration',
        component: resolve =>
          require(['@/views/loadProposalVehicleRegistration/index'], resolve),
        meta: { title: '货量提示配车登记' }
      }
    ]
  },
  {
    path: '/loadProposalVehicleConfirmation',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        name: 'loadProposalVehicleConfirmation',
        component: resolve =>
          require(['@/views/loadProposalVehicleConfirmation/index'], resolve),
        meta: { title: '货量提示配车确认' }
      }
    ]
  }
]

export default new Router({
  base: process.env.VUE_APP_SUB_PATH,
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
