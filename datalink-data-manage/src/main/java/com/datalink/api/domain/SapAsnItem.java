package com.datalink.api.domain;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public class SapAsnItem {
    /** PO编号 */
    @JsonProperty("VGBEL")
    private String VGBEL;

    /** PO计划交货日期 */
    @JsonProperty("LFDAT")
    private String LFDAT;

    /** 收货点 */
    @JsonProperty("VSTEL")
    private String VSTEL;

    /** PO行项目 */
    @JsonProperty("VGPOS")
    private String VGPOS;

    /** 物料编码 */
    @JsonProperty("MATNR")
    private String MATNR;

    /** 数量 */
    @JsonProperty("LFIMG")
    private BigDecimal LFIMG;

    /** 到货日期 */
    @JsonProperty("WADAT")
    private String WADAT;

    /** 到货时间 */
    @JsonProperty("LFUHR")
    private String LFUHR = "000000";

    /** 工厂代码 */
    @JsonProperty("WERKS")
    private String WERKS;

    /** 库存地点 */
    @JsonProperty("LGORT")
    private String LGORT;

    /** ASN编号 */
    @JsonProperty("VERUR_LA")
    private String VERUR_LA;

    /** 包数 */
    @JsonProperty("BSTRF")
    private int BSTRF;

    /** 单位 */
    @JsonProperty("MEINS")
    private String MEINS;

    public String getVGBEL() {
        return VGBEL;
    }

    public void setVGBEL(String VGBEL) {
        this.VGBEL = VGBEL;
    }

    public String getLFDAT() {
        return LFDAT;
    }

    public void setLFDAT(String LFDAT) {
        this.LFDAT = LFDAT;
    }

    public String getVSTEL() {
        return VSTEL;
    }

    public void setVSTEL(String VSTEL) {
        this.VSTEL = VSTEL;
    }

    public String getVGPOS() {
        return VGPOS;
    }

    public void setVGPOS(String VGPOS) {
        this.VGPOS = VGPOS;
    }

    public String getMATNR() {
        return MATNR;
    }

    public void setMATNR(String MATNR) {
        this.MATNR = MATNR;
    }

    public BigDecimal getLFIMG() {
        return LFIMG;
    }

    public void setLFIMG(BigDecimal LFIMG) {
        this.LFIMG = LFIMG;
    }

    public String getWADAT() {
        return WADAT;
    }

    public void setWADAT(String WADAT) {
        this.WADAT = WADAT;
    }

    public String getLFUHR() {
        return LFUHR;
    }

    public void setLFUHR(String LFUHR) {
        this.LFUHR = LFUHR;
    }

    public String getWERKS() {
        return WERKS;
    }

    public void setWERKS(String WERKS) {
        this.WERKS = WERKS;
    }

    public String getLGORT() {
        return LGORT;
    }

    public void setLGORT(String LGORT) {
        this.LGORT = LGORT;
    }

    public String getVERUR_LA() {
        return VERUR_LA;
    }

    public void setVERUR_LA(String VERUR_LA) {
        this.VERUR_LA = VERUR_LA;
    }

    public int getBSTRF() {
        return BSTRF;
    }

    public void setBSTRF(int BSTRF) {
        this.BSTRF = BSTRF;
    }

    public String getMEINS() {
        return MEINS;
    }

    public void setMEINS(String MEINS) {
        this.MEINS = MEINS;
    }
}
