import request from '@/utils/request'


export function query (data) {
  return request({
    url: '/datamanage/loadProposal/list',
    method: 'get',
    params: data
  })
}

export function add (data) {
  return request({
    url: '/datamanage/loadProposal',
    method: 'post',
    data: data
  })
}

export function update (data) {
  return request({
    url: '/datamanage/loadProposal',
    method: 'put',
    data: data
  })
}
export function remove (ids) {
  return request({
    url: '/datamanage/loadProposal/'+ids,
    method: 'delete'

  })
}
