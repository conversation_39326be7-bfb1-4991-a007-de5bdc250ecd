package com.datalink.api.controller;

import com.datalink.api.common.SapApiResult;
import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.*;
import com.datalink.api.domain.dto.*;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.JasperReportUtil;
import com.datalink.datamanage.domain.TblSapKanbanKd;
import com.datalink.datamanage.domain.TblSapKanbanSv;
import com.datalink.datamanage.service.ISapForecastService;
import com.datalink.datamanage.service.ITblSapKanbanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Api("SAP相关Kanban接口")
@Validated
@RestController
@RequestMapping("/api/sapKanban")
public class SapKanbanApi {

    @Autowired
    private ITblSapKanbanService sapKanbanService;

    private static final Logger logger = LoggerFactory.getLogger(SapKanbanApi.class);

    //    @ApiOperation("接收Kanban-KD")
    @PostMapping("/receiveKd")
    @Log(title = "接收Kanban-KD", businessType = BusinessType.IMPORT)
    @Transactional(rollbackFor = Exception.class)
    public SapApiResult receiveKd(@Valid @RequestBody SapRequestDTO<TblSapKanbanKd> requestDTO) {
        try {
            List<TblSapKanbanKd> items = requestDTO.getItems();
            if (items != null && !items.isEmpty()) {
                sapKanbanService.batchInsertTblSapKanbanKd(items);
            }
            return SapApiResult.success();
        } catch (Exception e) {
            logger.error("处理Kanban-KD数据失败：", e);
            return SapApiResult.error("处理Kanban-KD数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("接收Kanban-LINE")
    @PostMapping("/receiveLine")
    @Log(title = "接收Kanban-LINE", businessType = BusinessType.IMPORT)
    @Transactional(rollbackFor = Exception.class)
    public SapApiResult receiveLine(@Valid @RequestBody SapRequestDTO<TblSapKanbanKd> requestDTO) {
        try {
            List<TblSapKanbanKd> items = requestDTO.getItems();
            if (items != null && !items.isEmpty()) {
                sapKanbanService.batchInsertTblSapKanbanLine(items);
            }
            return SapApiResult.success();
        } catch (Exception e) {
            logger.error("处理Kanban-LINE数据失败：", e);
            return SapApiResult.error("处理Kanban-LINE数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("接收Kanban-SV")
    @PostMapping("/receiveSv")
    @Log(title = "接收Kanban-SV", businessType = BusinessType.IMPORT)
    @Transactional(rollbackFor = Exception.class)
    public SapApiResult receiveSv(@Valid @RequestBody SapRequestDTO<TblSapKanbanSv> requestDTO) {
        try {
            List<TblSapKanbanSv> items = requestDTO.getItems();
            if (items != null && !items.isEmpty()) {
                sapKanbanService.batchInsertTblSapKanbanSv(items);
            }
            return SapApiResult.success();
        } catch (Exception e) {
            logger.error("处理Kanban-SV数据失败：", e);
            return SapApiResult.error("处理Kanban-SV数据失败：" + e.getMessage());
        }
    }
}
