package com.datalink.datamanage.mapper;

import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.domain.TblFeedbackItem;
import org.apache.ibatis.annotations.Mapper;

/**
 * 收货反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-07-06
 */
@Mapper
public interface TblFeedbackMapper 
{
    /**
     * 查询收货反馈
     * 
     * @param feedId 收货反馈ID
     * @return 收货反馈
     */
    public TblFeedback selectTblFeedbackById(Long feedId);

    /**
     * 查询收货反馈列表
     * 
     * @param tblFeedback 收货反馈
     * @return 收货反馈集合
     */
    public List<TblFeedback> selectTblFeedbackList(TblFeedback tblFeedback);

    /**
     * 查询收货反馈列表(包含行项目)
     *
     * @param tblFeedback 收货反馈
     * @return 收货反馈集合
     */
    public List<TblFeedback> selectTblFeedbackWithItemList(TblFeedback tblFeedback);

    /**
     * 查询收货反馈列表（包含行项目）--接口专用
     *
     * @param tblFeedback 收货反馈
     * @return 收货反馈集合
     */
    public List<TblFeedback> selectTblFeedbackFullList(TblFeedback tblFeedback);

    /**
     * 新增收货反馈
     * 
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    public int insertTblFeedback(TblFeedback tblFeedback);

    /**
     * 修改收货反馈
     * 
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    public int updateTblFeedback(TblFeedback tblFeedback);

    /**
     * 删除收货反馈
     * 
     * @param feedId 收货反馈ID
     * @return 结果
     */
    public int deleteTblFeedbackById(Long feedId);

    /**
     * 批量删除收货反馈
     * 
     * @param feedIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblFeedbackByIds(Long[] feedIds);

    /**
     * 批量删除收货反馈行项目
     * 
     * @param feedIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblFeedbackItemByFeedIds(Long[] feedIds);
    
    /**
     * 批量新增收货反馈行项目
     * 
     * @param tblFeedbackItemList 收货反馈行项目列表
     * @return 结果
     */
    public int batchTblFeedbackItem(List<TblFeedbackItem> tblFeedbackItemList);
    

    /**
     * 通过收货反馈ID删除收货反馈行项目信息
     * 
     * @param feedId 收货反馈ID
     * @return 结果
     */
    public int deleteTblFeedbackItemByFeedId(Long feedId);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询收货反馈行项目列表
     *
     * @param feedbackItem 收货反馈行项目
     * @return 收货行项目反馈集合
     */
    public List<TblFeedbackItem> selectTblFeedbackItemList(TblFeedbackItem feedbackItem);

    /**
     * 查询收货反馈(不包含行项目)
     *
     * @param feed 收货反馈
     * @return 收货反馈
     */
    @DataScope(supplierAlias = "a")
    public TblFeedback selectTblFeedbackOnlyById(TblFeedback feed);

    /**
     * 更新订单物料剩余数量
     *
     * @param feedbackItem 反馈明细
     * @return 结果
     */
    public int updateOrderAsnQuantity(TblFeedbackItem feedbackItem);

    /**
     * 更新订单完成状态
     *
     * @param orderCode 订单编号
     * @return 结果
     */
    public int updateOrderComplete(String orderCode);
}
