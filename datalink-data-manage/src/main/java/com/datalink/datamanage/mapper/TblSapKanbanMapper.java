package com.datalink.datamanage.mapper;

import java.util.List;
import com.datalink.datamanage.domain.TblSapKanbanKd;
import com.datalink.datamanage.domain.TblSapKanbanSv;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 看板KDMapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@Mapper
public interface TblSapKanbanMapper
{
    /**
     * 查询看板KD
     * 
     * @param id 看板KDID
     * @return 看板KD
     */
    public TblSapKanbanKd selectTblSapKanbanKdById(Long id);

    /**
     * 查询看板LINE
     *
     * @param id 看板LINEID
     * @return 看板LINE
     */
    public TblSapKanbanKd selectTblSapKanbanLineById(Long id);

    /**
     * 查询看板SV
     *
     * @param id 看板SVID
     * @return 看板SV
     */
    public TblSapKanbanSv selectTblSapKanbanSvById(Long id);

    /**
     * 查询看板KD列表
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 看板KD集合
     */
    public List<TblSapKanbanKd> selectTblSapKanbanKdList(TblSapKanbanKd tblSapKanbanKd);

    /**
     * 新增看板KD
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 结果
     */
    public int insertTblSapKanbanKd(TblSapKanbanKd tblSapKanbanKd);

    /**
     * 修改看板KD
     * 
     * @param tblSapKanbanKd 看板KD
     * @return 结果
     */
    public int updateTblSapKanbanKd(TblSapKanbanKd tblSapKanbanKd);

    /**
     * 删除看板KD
     * 
     * @param fileNo 看板KDID
     * @return 结果
     */
    public int deleteTblSapKanbanKdById(String fileNo);

    /**
     * 批量删除看板KD
     * 
     * @param fileNos 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblSapKanbanKdByIds(String[] fileNos);

    /**
     * 新增看板LINE
     *
     * @param tblSapKanbanLine 看板LINE
     * @return 结果
     */
    int insertTblSapKanbanLine(TblSapKanbanKd tblSapKanbanLine);

    /**
     * 新增看板SV
     *
     * @param tblSapKanbanSv 看板SV
     * @return 结果
     */
    public int insertTblSapKanbanSv(TblSapKanbanSv tblSapKanbanSv);

    /**
     * 批量新增看板KD
     *
     * @param list 看板KD列表
     * @return 结果
     */
    public int batchInsertTblSapKanbanKd(@Param("list") List<TblSapKanbanKd> list);

    /**
     * 批量新增看板LINE
     *
     * @param list 看板LINE列表
     * @return 结果
     */
    public int batchInsertTblSapKanbanLine(@Param("list") List<TblSapKanbanKd> list);

    /**
     * 批量新增看板SV
     *
     * @param list 看板SV列表
     * @return 结果
     */
    public int batchInsertTblSapKanbanSv(@Param("list") List<TblSapKanbanSv> list);

    /**
     * 根据业务键查询看板KD
     */
    TblSapKanbanKd selectTblSapKanbanKdByBizKey(@Param("demandCode") String demandCode, 
        @Param("issueNo") String issueNo, @Param("customerPartsNo") String customerPartsNo);

    /**
     * 根据业务键查询看板LINE
     */
    TblSapKanbanKd selectTblSapKanbanLineByBizKey(@Param("demandCode") String demandCode,
        @Param("issueNo") String issueNo, @Param("customerPartsNo") String customerPartsNo);

    /**
     * 根据业务键查询看板SV
     */  
    TblSapKanbanSv selectTblSapKanbanSvByBizKey(@Param("demandCode") String demandCode,
        @Param("issueNoId") String issueNoId, @Param("customerPartsNo") String customerPartsNo);

    /**
     * 更新看板LINE
     */
    int updateTblSapKanbanLine(TblSapKanbanKd tblSapKanbanLine);

    /**
     * 更新看板SV
     */
    int updateTblSapKanbanSv(TblSapKanbanSv tblSapKanbanSv);

    List<TblSapKanbanKd> selectTblSapKanbanLineList(TblSapKanbanKd tblSapKanbanLine);

    List<TblSapKanbanSv> selectTblSapKanbanSvList(TblSapKanbanSv tblSapKanbanSv);
}
