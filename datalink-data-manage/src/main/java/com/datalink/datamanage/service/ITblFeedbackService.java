package com.datalink.datamanage.service;

import java.util.List;

import com.datalink.common.core.domain.AjaxResult;
import com.datalink.datamanage.domain.TblFeedback;
import com.datalink.datamanage.domain.TblFeedbackItem;

/**
 * 收货反馈Service接口
 * 
 * <AUTHOR>
 * @date 2021-07-06
 */
public interface ITblFeedbackService 
{
    /**
     * 查询收货反馈
     * 
     * @param feedId 收货反馈ID
     * @return 收货反馈
     */
    public TblFeedback selectTblFeedbackById(Long feedId);

    /**
     * 查询收货反馈列表
     * 
     * @param tblFeedback 收货反馈
     * @return 收货反馈集合
     */
    public List<TblFeedback> selectTblFeedbackList(TblFeedback tblFeedback);

    /**
     * 查询收货反馈列表(包含行项目)
     *
     * @param tblFeedback 收货反馈
     * @return 收货反馈集合
     */
    public List<TblFeedback> selectTblFeedbackWithItemList(TblFeedback tblFeedback);

    /**
     * 查询收货反馈列表（包含行项目）--接口专用
     *
     * @param tblFeedback 收货反馈
     * @return 收货反馈集合
     */
    public List<TblFeedback> selectTblFeedbackFullList(TblFeedback tblFeedback);

    /**
     * 新增收货反馈
     * 
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    public int insertTblFeedback(TblFeedback tblFeedback);

    /**
     * 修改收货反馈
     * 
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    public int updateTblFeedback(TblFeedback tblFeedback);

    /**
     * 批量删除收货反馈
     * 
     * @param feedIds 需要删除的收货反馈ID
     * @return 结果
     */
    public int deleteTblFeedbackByIds(Long[] feedIds);

    /**
     * 删除收货反馈信息
     * 
     * @param feedId 收货反馈ID
     * @return 结果
     */
    public int deleteTblFeedbackById(Long feedId);

    /**
     * 仅更收货反馈信息
     *
     * @param tblFeedback 收货反馈
     * @return 结果
     */
    public int updateTblFeedbackOnly(TblFeedback tblFeedback);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询收货反馈行项目列表
     *
     * @param feedbackItem 收货反馈行项目
     * @return 收货行项目反馈集合
     */
    public List<TblFeedbackItem> selectTblFeedbackItemList(TblFeedbackItem feedbackItem);

    /**
     * 查询收货反馈(不包含行项目)
     *
     * @param feedId 收货反馈ID
     * @return 收货反馈
     */
    public TblFeedback selectTblFeedbackOnlyById(Long feedId);

    AjaxResult downloadFeedbackTxt(List<Long> feedbackIds, String tz);

    void updateOrderStatus(TblFeedback feedback);

    /**
     * 确认结算单
     *
     * @param feedIds 结算单ID列表
     * @return 结果
     */
    public AjaxResult confirmFeedback(List<Long> feedIds);
}
