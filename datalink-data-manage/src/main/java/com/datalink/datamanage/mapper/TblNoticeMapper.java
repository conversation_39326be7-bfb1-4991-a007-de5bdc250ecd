package com.datalink.datamanage.mapper;

import com.datalink.datamanage.domain.NoticeReply;
import com.datalink.datamanage.domain.TblNotice;
import com.datalink.datamanage.domain.TblNoticeReply;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 公告Mapper接口
 *
 * <AUTHOR>
 * @date 2022-05-02
 */
public interface TblNoticeMapper
{
    /**
     * 查询公告
     *
     * @param noticeId 公告ID
     * @return 公告
     */
    public TblNotice selectTblNoticeById(Long noticeId);

    /**
     * 查询公告列表
     *
     * @param tblNotice 公告
     * @return 公告集合
     */
    public List<TblNotice> selectTblNoticeList(TblNotice tblNotice);

    /**
     * 新增公告
     *
     * @param tblNotice 公告
     * @return 结果
     */
    public int insertTblNotice(TblNotice tblNotice);

    /**
     * 修改公告
     *
     * @param tblNotice 公告
     * @return 结果
     */
    public int updateTblNotice(TblNotice tblNotice);

    /**
     * 删除公告
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteTblNoticeById(Long noticeId);

    /**
     * 批量删除公告
     *
     * @param noticeIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblNoticeByIds(Long[] noticeIds);

    /**
     * 批量删除公告回复
     *
     * @param customerIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblNoticeReplyByNoticeIds(Long[] noticeIds);

    /**
     * 批量新增公告回复
     *
     * @param tblNoticeReplyList 公告回复列表
     * @return 结果
     */
    public int batchTblNoticeReply(List<TblNoticeReply> tblNoticeReplyList);


    /**
     * 通过公告ID删除公告回复信息
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteTblNoticeReplyByNoticeId(Long noticeId);

    public List<NoticeReply> selectToSendNoticeReplyList();

    public int updateTblNoticeReply(TblNoticeReply tblNoticeReply);

    public List<TblNoticeReply> selectTblNoticeReplyList(TblNoticeReply tblNoticeReply);

    public List<NoticeReply> selectNoticeReplyList(NoticeReply noticeReply);

    public int updateTblNoticeReplyByNoticeCodeMap(Map<String, Object> map);

    public TblNoticeReply selectTblNoticeReplyByNoticeCodeAndSuppCode(@Param("noticeCode") String noticeCode, @Param("suppCode") String suppCode);
}
