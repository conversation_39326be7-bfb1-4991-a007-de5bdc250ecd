package com.datalink.datamanage.service;

import com.datalink.datamanage.domain.NoticeReply;
import com.datalink.datamanage.domain.TblNotice;
import com.datalink.datamanage.domain.TblNoticeReply;

import java.util.List;
import java.util.Map;

/**
 * 公告Service接口
 * 
 * <AUTHOR>
 * @date 2022-05-02
 */
public interface ITblNoticeService 
{
    /**
     * 查询公告
     * 
     * @param noticeId 公告ID
     * @return 公告
     */
    public TblNotice selectTblNoticeById(Long noticeId);

    /**
     * 查询公告列表
     * 
     * @param tblNotice 公告
     * @return 公告集合
     */
    public List<TblNotice> selectTblNoticeList(TblNotice tblNotice);

    /**
     * 新增公告
     * 
     * @param tblNotice 公告
     * @return 结果
     */
    public int insertTblNotice(TblNotice tblNotice);

    /**
     * 修改公告
     * 
     * @param tblNotice 公告
     * @return 结果
     */
    public int updateTblNotice(TblNotice tblNotice);

    /**
     * 批量删除公告
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteTblNoticeByIds(Long[] noticeIds);

    /**
     * 删除公告信息
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteTblNoticeById(Long noticeId);

    public List<NoticeReply> selectToSendNoticeReplyList();

    public int updateTblNoticeReply(TblNoticeReply tblNoticeReply);

    public List<TblNoticeReply> selectTblNoticeReplyList(TblNoticeReply tblNoticeReply);

    public List<NoticeReply> selectNoticeReplyList(NoticeReply noticeReply);

    public int updateTblNoticeReplyByNoticeCodeMap(Map<String, Object> map);
}
