import request from '@/utils/request'

// 查询附件列表
export function listAttachment(query) {
  return request({
    url: '/datamanage/attachment/list',
    method: 'get',
    params: query
  })
}

// 查询附件详细
export function getAttachment(attachmentId) {
  return request({
    url: '/datamanage/attachment/' + attachmentId,
    method: 'get'
  })
}

// 新增附件
export function addAttachment(data) {
  return request({
    url: '/datamanage/attachment',
    method: 'post',
    data: data
  })
}

// 修改附件
export function updateAttachment(data) {
  return request({
    url: '/datamanage/attachment',
    method: 'put',
    data: data
  })
}

// 删除附件
export function delAttachment(attachmentId) {
  return request({
    url: '/datamanage/attachment/' + attachmentId,
    method: 'delete'
  })
}

// 导出附件
export function exportAttachment(query) {
  return request({
    url: '/datamanage/attachment/export',
    method: 'get',
    params: query
  })
}