import request from '@/utils/request'

export function queryStats(direction) {
  return request({
    url: '/datamanage/dashboard/query',
    method: 'get',
    params: { direction: direction }
  }).then(response => {
    // 遍历所有的类型并对每个类型的 detail 进行 reverse 操作
    response.data.forEach(item => {
      if (item.detail && Array.isArray(item.detail)) {
        item.detail.reverse(); // 反转 detail 数组，将时间从旧到新排列
      }
    });

    return response; // 返回处理后的数据
  });
}
