package com.datalink.datamanage.controller;

import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblAttachment;
import com.datalink.datamanage.service.ITblAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 附件Controller
 *
 * <AUTHOR>
 * @date 2023-01-24
 */
@RestController
@RequestMapping("/datamanage/attachment")
public class TblAttachmentController extends BaseController
{
    @Autowired
    private ITblAttachmentService tblAttachmentService;

    /**
     * 查询附件列表
     */
//    @PreAuthorize("@ss.hasPermi('datamanage:attachment:list')")
    @GetMapping("/list")
    public TableDataInfo list(TblAttachment tblAttachment)
    {
        startPage();
        List<TblAttachment> list = tblAttachmentService.selectTblAttachmentList(tblAttachment);
        return getDataTable(list);
    }

    /**
     * 导出附件列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:attachment:export')")
    @Log(title = "附件", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblAttachment tblAttachment)
    {
        List<TblAttachment> list = tblAttachmentService.selectTblAttachmentList(tblAttachment);
        ExcelUtil<TblAttachment> util = new ExcelUtil<TblAttachment>(TblAttachment.class);
        return util.exportExcel(list, "附件数据");
    }

    /**
     * 获取附件详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:attachment:query')")
    @GetMapping(value = "/{attachmentId}")
    public AjaxResult getInfo(@PathVariable("attachmentId") Long attachmentId)
    {
        return AjaxResult.success(tblAttachmentService.selectTblAttachmentById(attachmentId));
    }

    /**
     * 新增附件
     */
    @PreAuthorize("@ss.hasPermi('datamanage:attachment:add')")
    @Log(title = "附件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblAttachment tblAttachment)
    {
        return toAjax(tblAttachmentService.insertTblAttachment(tblAttachment));
    }

    /**
     * 修改附件
     */
    @PreAuthorize("@ss.hasPermi('datamanage:attachment:edit')")
    @Log(title = "附件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblAttachment tblAttachment)
    {
        return toAjax(tblAttachmentService.updateTblAttachment(tblAttachment));
    }

    /**
     * 删除附件
     */
    @PreAuthorize("@ss.hasPermi('datamanage:attachment:remove')")
    @Log(title = "附件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attachmentIds}")
    public AjaxResult remove(@PathVariable Long[] attachmentIds)
    {
        return toAjax(tblAttachmentService.deleteTblAttachmentByIds(attachmentIds));
    }
}
