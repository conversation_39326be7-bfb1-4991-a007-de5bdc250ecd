package com.datalink.api.domain.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@Data
public class SapYearForecastSubItemDTO {
    @NotBlank(message = "部品番号不能为空")
    @JsonProperty("MATNR")
    private String matnr;   // 部品番号
    @JsonProperty("PLWRK")
    private String plwrk;   // 要求工場
    @JsonProperty("ZMETHD")
    private String zmethd;  // 納入方式
    @JsonProperty("WANST")
    private String wanst;   // 納入場所
    @JsonProperty("DISPO")
    private String dispo;   // 計画担当
    @JsonProperty("BESKZ")
    private String beskz;   // 自己
    @JsonProperty("BSTRF")
    private String bstrf;   // SNEP
    @JsonProperty("ZMONTH")
    private String zmonth;  // 开始月份
    @JsonProperty("GSMNG1")
    private BigDecimal gsmng1;  // 1月数量
    @JsonProperty("GSMNG2")
    private BigDecimal gsmng2;  // 2月数量
    @JsonProperty("GSMNG3")
    private BigDecimal gsmng3;  // 3月数量
    @JsonProperty("GSMNG4")
    private BigDecimal gsmng4;  // 4月数量
    @JsonProperty("GSMNG5")
    private BigDecimal gsmng5;  // 5月数量
    @JsonProperty("GSMNG6")
    private BigDecimal gsmng6;  // 6月数量
    @JsonProperty("GSMNG7")
    private BigDecimal gsmng7;  // 7月数量
    @JsonProperty("GSMNG8")
    private BigDecimal gsmng8;  // 8月数量
    @JsonProperty("GSMNG9")
    private BigDecimal gsmng9;  // 9月数量
    @JsonProperty("GSMNG10")
    private BigDecimal gsmng10; // 10月数量
    @JsonProperty("GSMNG11")
    private BigDecimal gsmng11; // 11月数量
    @JsonProperty("GSMNG12")
    private BigDecimal gsmng12; // 12月数量
    @JsonProperty("MEINS")
    private String meins;   // 発注単位
} 