<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.DataStatMapper">
    <resultMap type="DataStat" id="DataStatResult">
        <result property="type" column="type"/>
        <collection property="detail" notNullColumn="date"  javaType="java.util.List" resultMap="DataStatItemResult" />
    </resultMap>

    <resultMap type="DataStatItem" id="DataStatItemResult">
        <result property="date"    column="date"    />
        <result property="count"    column="num"    />
    </resultMap>

    <select id="selectDataStats" parameterType="String" resultMap="DataStatResult">
        select "Order" as type, DATE_FORMAT(Create_Time, "%Y-%m-%d") as date, count(0) as num
        from tbl_order
        where create_time > DATE_SUB(SYSDATE(), INTERVAL 7 DAY)
          and Direction = #{direction}
        group by date
        UNION
        select "Feedback" as type, DATE_FORMAT(Create_Time, "%Y-%m-%d") as date, count(0) as num
        from tbl_feedback
        where create_time > DATE_SUB(SYSDATE(), INTERVAL 7 DAY)
          and Direction = #{direction}
        group by date
        UNION
        select "Consignment" as type, DATE_FORMAT(Create_Time, "%Y-%m-%d") as date, count(0) as num
        from tbl_consignment_inventory
        where create_time > DATE_SUB(SYSDATE(), INTERVAL 7 DAY)
          and Direction = #{direction}
        group by date
        UNION
        select "Forecast" as type, DATE_FORMAT(Create_Time, "%Y-%m-%d") as date, count(0) as num
        from tbl_forecast
        where create_time > DATE_SUB(SYSDATE(), INTERVAL 7 DAY)
          and Direction = #{direction}
        group by date
        UNION
        select "Inventory" as type, DATE_FORMAT(Create_Time, "%Y-%m-%d") as date, count(0) as num
        from tbl_inventory
        where create_time > DATE_SUB(SYSDATE(), INTERVAL 7 DAY)
          and Direction = #{direction}
        group by date
        order by date asc
    </select>

</mapper>