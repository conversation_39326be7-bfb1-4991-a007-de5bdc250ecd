package com.datalink.datamanage.controller;

import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.datamanage.domain.TblEvent;
import com.datalink.datamanage.service.ITblEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 事件Controller
 * 
 * <AUTHOR>
 * @date 2022-04-20
 */
@RestController
@RequestMapping("/datamanage/event")
public class TblEventController extends BaseController
{
    @Autowired
    private ITblEventService tblEventService;

    /**
     * 查询事件列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(TblEvent tblEvent)
    {
        startPage();
        List<TblEvent> list = tblEventService.selectTblEventList(tblEvent);
        return getDataTable(list);
    }

    /**
     * 导出事件列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:event:export')")
    @Log(title = "事件", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblEvent tblEvent)
    {
        List<TblEvent> list = tblEventService.selectTblEventList(tblEvent);
        ExcelUtil<TblEvent> util = new ExcelUtil<TblEvent>(TblEvent.class);
        return util.exportExcel(list, "事件数据");
    }

    /**
     * 获取事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:event:query')")
    @GetMapping(value = "/{eventId}")
    public AjaxResult getInfo(@PathVariable("eventId") Long eventId)
    {
        return AjaxResult.success(tblEventService.selectTblEventById(eventId));
    }

    /**
     * 新增事件
     */
    @PreAuthorize("@ss.hasPermi('datamanage:event:add')")
    @Log(title = "事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblEvent tblEvent)
    {
        return toAjax(tblEventService.insertTblEvent(tblEvent));
    }

    /**
     * 修改事件
     */
    @PreAuthorize("@ss.hasPermi('datamanage:event:edit')")
    @Log(title = "事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblEvent tblEvent)
    {
        return toAjax(tblEventService.updateTblEvent(tblEvent));
    }

    /**
     * 删除事件
     */
    @PreAuthorize("@ss.hasPermi('datamanage:event:remove')")
    @Log(title = "事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{eventIds}")
    public AjaxResult remove(@PathVariable Long[] eventIds)
    {
        return toAjax(tblEventService.deleteTblEventByIds(eventIds));
    }
}
