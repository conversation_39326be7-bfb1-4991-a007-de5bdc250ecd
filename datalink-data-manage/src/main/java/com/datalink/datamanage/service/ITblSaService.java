package com.datalink.datamanage.service;

import com.datalink.datamanage.domain.TblSa;

import java.util.List;

/**
 * 协议Service接口
 *
 * <AUTHOR>
 * @date 2023-08-31
 */
public interface ITblSaService
{
    /**
     * 查询协议
     *
     * @param saId 协议ID
     * @return 协议
     */
    public TblSa selectTblSaById(Long saId);

    /**
     * 查询协议列表
     *
     * @param tblSa 协议
     * @return 协议集合
     */
    public List<TblSa> selectTblSaList(TblSa tblSa);

    /**
     * 新增协议
     *
     * @param tblSa 协议
     * @return 结果
     */
    public int insertTblSa(TblSa tblSa);

    /**
     * 修改协议
     *
     * @param tblSa 协议
     * @return 结果
     */
    public int updateTblSa(TblSa tblSa);

    /**
     * 批量删除协议
     *
     * @param saIds 需要删除的协议ID
     * @return 结果
     */
    public int deleteTblSaByIds(Long[] saIds);

    /**
     * 删除协议信息
     *
     * @param saId 协议ID
     * @return 结果
     */
    public int deleteTblSaById(Long saId);

    public List<TblSa> selectTblSaFullList(TblSa tblSa);

    public Long selectLastId();

    public int updateTblSaOnly(TblSa sa);

    public List<TblSa> selectTblSaWithItemList(TblSa tblSa);
}
