package com.datalink.datamanage.controller;

import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblConsignmentInventoryItem;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblConsignmentInventory;
import com.datalink.datamanage.service.ITblConsignmentInventoryService;
import com.datalink.common.utils.poi.ExcelUtil;
import com.datalink.common.core.page.TableDataInfo;

/**
 * 寄售库存Controller
 * 
 * <AUTHOR>
 * @date 2021-06-24
 */
@RestController
@RequestMapping("/datamanage/consignment")
public class TblConsignmentInventoryController extends BaseController
{
    @Autowired
    private ITblConsignmentInventoryService tblConsignmentInventoryService;

    /**
     * 查询寄售库存列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:list')")
    @DataScope(supplierAlias = "a")
    @GetMapping("/list")
    public TableDataInfo list(TblConsignmentInventory tblConsignmentInventory)
    {
        startPage();
        List<TblConsignmentInventory> list = tblConsignmentInventoryService.selectTblConsignmentInventoryList(tblConsignmentInventory);
        return getDataTable(list);
    }

    /**
     * 导出寄售库存列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:export')")
    @DataScope(supplierAlias = "a")
    @Log(title = "寄售库存", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TblConsignmentInventory tblConsignmentInventory)
    {
        List<TblConsignmentInventory> list = tblConsignmentInventoryService.selectTblConsignmentInventoryList(tblConsignmentInventory);
        ExcelUtil<TblConsignmentInventory> util = new ExcelUtil<TblConsignmentInventory>(TblConsignmentInventory.class);
        return util.exportExcel(list, "寄售库存数据");
    }

    /**
     * 获取寄售库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:query')")
    @GetMapping(value = "/{consignmentId}")
    public AjaxResult getInfo(@PathVariable("consignmentId") Long consignmentId)
    {
        return AjaxResult.success(tblConsignmentInventoryService.selectTblConsignmentInventoryById(consignmentId));
    }

    /**
     * 新增寄售库存
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:add')")
    @Log(title = "寄售库存", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TblConsignmentInventory tblConsignmentInventory)
    {
        return toAjax(tblConsignmentInventoryService.insertTblConsignmentInventory(tblConsignmentInventory));
    }

    /**
     * 修改寄售库存
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:edit')")
    @Log(title = "寄售库存", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TblConsignmentInventory tblConsignmentInventory)
    {
        return toAjax(tblConsignmentInventoryService.updateTblConsignmentInventory(tblConsignmentInventory));
    }

    /**
     * 删除寄售库存
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:remove')")
    @Log(title = "寄售库存", businessType = BusinessType.DELETE)
	@DeleteMapping("/{consignmentIds}")
    public AjaxResult remove(@PathVariable Long[] consignmentIds)
    {
        return toAjax(tblConsignmentInventoryService.deleteTblConsignmentInventoryByIds(consignmentIds));
    }

    /**
     * 查询寄售库存行项目列表
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:list')")
    @GetMapping("/listItems")
    public TableDataInfo listItems(TblConsignmentInventoryItem tblConsignmentInventoryItem)
    {
        startPage();
        List<TblConsignmentInventoryItem> list = tblConsignmentInventoryService.selectTblConsignmentInventoryItemList(tblConsignmentInventoryItem);
        return getDataTable(list);
    }

    /**
     * 获取寄售库存详细信息(不包含行项目)
     */
    @PreAuthorize("@ss.hasPermi('datamanage:inventory:query')")
    @GetMapping(value = "/head/{consignmentId}")
    public AjaxResult getConsignmentOnly(@PathVariable("consignmentId") Long consignmentId)
    {
        return AjaxResult.success(tblConsignmentInventoryService.selectTblConsignmentInventoryOnlyById(consignmentId));
    }
}
