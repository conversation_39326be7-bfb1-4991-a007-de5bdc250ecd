package com.datalink.datamanage.controller;

import com.datalink.common.core.controller.BaseController;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.datamanage.domain.DataStat;
import com.datalink.datamanage.domain.DataStatItem;
import com.datalink.datamanage.service.IDataStatService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

/**
 * Dashboard Controller
 *
 * <AUTHOR>
 * @date 2021-06-24
 */
@RestController
@RequestMapping("/datamanage/dashboard")
public class DashboardController extends BaseController {
    @Autowired
    private IDataStatService dataStatService;

    /**
     * 导出寄售库存列表
     */
    @GetMapping("/query")
    public AjaxResult export(String direction)
    {
        List<DataStat> stats = dataStatService.selectDataStats(direction);
        List<String> types = new ArrayList<String>(Arrays.asList("Order", "Consignment", "Inventory", "Forecast", "Feedback"));
        for (DataStat stat : stats){
            types.remove(stat.getType());
            List<DataStatItem> items = stat.getDetail();
            List<DataStatItem> fullStats = getEmptyItems();
            for (DataStatItem item: items){
                for (DataStatItem newItem: fullStats){
                    if (newItem.getDate().equals(item.getDate())){
                        newItem.setCount(item.getCount());
                        break;
                    }
                }
            }
            stat.setDetail(fullStats);
        }
        for (String type : types){
            DataStat stat = new DataStat();
            stat.setType(type);
            stat.setDetail(getEmptyItems());
            stats.add(stat);
        }
        return AjaxResult.success(stats);
    }

    private List<DataStatItem> getEmptyItems(){
        List<DataStatItem> items= Lists.newArrayList();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        for (int i = 0; i < 7; i++){
            DataStatItem item = new DataStatItem();
            item.setDate(format.format(calendar.getTime()));
            item.setCount(0);
            items.add(item);
            calendar.set(Calendar.DAY_OF_YEAR, calendar.get(Calendar.DAY_OF_YEAR) - 1);
        }
        return items;
    }
}
