package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.datamanage.domain.TblInventory;
import com.datalink.datamanage.domain.TblInventoryItem;
import com.datalink.datamanage.service.ITblInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@Api("库存接口")
@Validated
@RestController
@RequestMapping("/api/inventory")
public class TblInventoryApi {

    @Autowired
    private ITblInventoryService tblInventoryService;

    //    @ApiOperation("查询库存")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"inventoryId","searchvalue","createby","updateby","updatetimesys","params","createtime","itemid","direction","kafkastatus"}, value={TblInventory.class, TblInventoryItem.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","consignmentInventoryid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblConsignmentInventoryItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询库存", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblInventory> inventoryList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblInventory searchParam = new TblInventory();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            inventoryList = tblInventoryService.selectTblInventoryFullList(searchParam);
        }
        if(inventoryList.isEmpty()){
            Long lastId = tblInventoryService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+inventoryList.get(inventoryList.size()-1).getInventoryId());
            ajax.put("time", inventoryList.get(inventoryList.size()-1).getCreateTime());
        }

        ajax.put("items", inventoryList);
        return ajax;
    }

    //    @ApiOperation("发送")
    @ApiImplicitParam(name = "inventory", value = "库存信息", dataType = "TblInventory")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送库存", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblInventory tblInventory) {
        tblInventory.setDirection(DataConstants.DIRECTION_OUT);
        tblInventory.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblInventory.setCreateBy(SecurityUtils.getUsername());
        tblInventoryService.insertTblInventory(tblInventory);
        return ApiResult.success();
    }

    //    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "inventory", value = "库存信息", dataType = "TblInventory", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送库存", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblInventory> inventoryList) {
        for (TblInventory inventory : inventoryList){
            inventory.setDirection(DataConstants.DIRECTION_OUT);
            inventory.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            inventory.setCreateBy(SecurityUtils.getUsername());
            tblInventoryService.insertTblInventory(inventory);
        }

        return ApiResult.success();
    }
}
