package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.SapApiResult;
import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.*;
import com.datalink.api.domain.dto.*;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.DateUtils;
import com.datalink.datamanage.service.ISapForecastService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Api("SAP相关预测接口")
@Validated
@RestController
@RequestMapping("/api/sapForecast")
public class SapForecastApi {

    @Autowired
    private ISapForecastService sapForecastService;

    //    @ApiOperation("接收周预测")
    @PostMapping("/receiveWeekForecast")
    @Log(title = "接收周预测", businessType = BusinessType.IMPORT)
    public SapApiResult receiveWeekForecast(@Valid @RequestBody SapWeekForecastRequestDTO requestDTO) {
        try {
            for (SapWeekForecastItemDTO itemDTO : requestDTO.getItems()) {
                SapWeekForecast forecast = new SapWeekForecast();
                // 使用 BeanUtils 或其他工具类复制属性
                BeanUtils.copyProperties(itemDTO, forecast);

                List<SapWeekForecastItem> subItems = new ArrayList<>();
                for (SapWeekForecastSubItemDTO subItemDTO : itemDTO.getSubItems()) {
                    SapWeekForecastItem item = new SapWeekForecastItem();
                    BeanUtils.copyProperties(subItemDTO, item);

                    // 处理bstrf字段，去除空格并转换为BigDecimal
                    if (StringUtils.isNotEmpty(subItemDTO.getBstrf())) {
                        item.setBstrf(new BigDecimal(subItemDTO.getBstrf().trim()));
                    }

                    // 处理预测明细...
                    List<SapWeekForecastItemDetail> details = new ArrayList<>();
                    // 处理第1周到第12周
                    processWeekDetails(details, subItemDTO, 1);
                    processWeekDetails(details, subItemDTO, 2);
                    processWeekDetails(details, subItemDTO, 3);
                    processWeekDetails(details, subItemDTO, 4);
                    processWeekDetails(details, subItemDTO, 5);
                    processWeekDetails(details, subItemDTO, 6);
                    processWeekDetails(details, subItemDTO, 7);
                    processWeekDetails(details, subItemDTO, 8);
                    processWeekDetails(details, subItemDTO, 9);
                    processWeekDetails(details, subItemDTO, 10);
                    processWeekDetails(details, subItemDTO, 11);
                    processWeekDetails(details, subItemDTO, 12);

                    item.setDetails(details);
                    subItems.add(item);
                }

                forecast.setSubItems(subItems);
                sapForecastService.saveWeekForecast(forecast);
            }
            return SapApiResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return SapApiResult.error("处理周预测数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("接收3个月预测")
    @PostMapping("/receiveMonthForecast")
    @Log(title = "接收3个月预测", businessType = BusinessType.IMPORT)
    public SapApiResult receiveMonthForecast(@Valid @RequestBody SapMonthForecastRequestDTO requestDTO) {
        try {
            for (SapMonthForecastItemDTO itemDTO : requestDTO.getItems()) {
                SapMonthForecast forecast = new SapMonthForecast();
                BeanUtils.copyProperties(itemDTO, forecast);
                forecast.setCreateTime(DateUtils.getNowDate());

                List<SapMonthForecastItem> subItems = new ArrayList<>();
                for (SapMonthForecastSubItemDTO subItemDTO : itemDTO.getSubItems()) {
                    SapMonthForecastItem item = new SapMonthForecastItem();
                    BeanUtils.copyProperties(subItemDTO, item);
                    item.setCreateTime(DateUtils.getNowDate());

                    // 处理bstrf字段，去除空格并转换为BigDecimal
                    if (StringUtils.isNotEmpty(subItemDTO.getBstrf())) {
                        item.setBstrf(new BigDecimal(subItemDTO.getBstrf().trim()));
                    }

                    subItems.add(item);
                }

                forecast.setSubItems(subItems);
                sapForecastService.saveMonthForecast(forecast);
            }
            return SapApiResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return SapApiResult.error("处理3个月预测数据失败：" + e.getMessage());
        }
    }

    private void processWeekDetails(List<SapWeekForecastItemDetail> details, SapWeekForecastSubItemDTO subItemDTO, int week) {
        switch (week) {
            case 1:
                addDetailIfNotNull(details, subItemDTO.getW1D1Date(), subItemDTO.getW1D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW1D2Date(), subItemDTO.getW1D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW1D3Date(), subItemDTO.getW1D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW1D4Date(), subItemDTO.getW1D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW1D5Date(), subItemDTO.getW1D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW1D6Date(), subItemDTO.getW1D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW1D7Date(), subItemDTO.getW1D7Qty());
                break;
            case 2:
                addDetailIfNotNull(details, subItemDTO.getW2D1Date(), subItemDTO.getW2D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW2D2Date(), subItemDTO.getW2D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW2D3Date(), subItemDTO.getW2D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW2D4Date(), subItemDTO.getW2D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW2D5Date(), subItemDTO.getW2D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW2D6Date(), subItemDTO.getW2D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW2D7Date(), subItemDTO.getW2D7Qty());
                break;
            case 3:
                addDetailIfNotNull(details, subItemDTO.getW3D1Date(), subItemDTO.getW3D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW3D2Date(), subItemDTO.getW3D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW3D3Date(), subItemDTO.getW3D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW3D4Date(), subItemDTO.getW3D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW3D5Date(), subItemDTO.getW3D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW3D6Date(), subItemDTO.getW3D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW3D7Date(), subItemDTO.getW3D7Qty());
                break;
            case 4:
                addDetailIfNotNull(details, subItemDTO.getW4D1Date(), subItemDTO.getW4D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW4D2Date(), subItemDTO.getW4D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW4D3Date(), subItemDTO.getW4D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW4D4Date(), subItemDTO.getW4D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW4D5Date(), subItemDTO.getW4D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW4D6Date(), subItemDTO.getW4D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW4D7Date(), subItemDTO.getW4D7Qty());
                break;
            case 5:
                addDetailIfNotNull(details, subItemDTO.getW5D1Date(), subItemDTO.getW5D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW5D2Date(), subItemDTO.getW5D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW5D3Date(), subItemDTO.getW5D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW5D4Date(), subItemDTO.getW5D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW5D5Date(), subItemDTO.getW5D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW5D6Date(), subItemDTO.getW5D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW5D7Date(), subItemDTO.getW5D7Qty());
                break;
            case 6:
                addDetailIfNotNull(details, subItemDTO.getW6D1Date(), subItemDTO.getW6D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW6D2Date(), subItemDTO.getW6D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW6D3Date(), subItemDTO.getW6D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW6D4Date(), subItemDTO.getW6D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW6D5Date(), subItemDTO.getW6D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW6D6Date(), subItemDTO.getW6D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW6D7Date(), subItemDTO.getW6D7Qty());
                break;
            case 7:
                addDetailIfNotNull(details, subItemDTO.getW7D1Date(), subItemDTO.getW7D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW7D2Date(), subItemDTO.getW7D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW7D3Date(), subItemDTO.getW7D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW7D4Date(), subItemDTO.getW7D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW7D5Date(), subItemDTO.getW7D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW7D6Date(), subItemDTO.getW7D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW7D7Date(), subItemDTO.getW7D7Qty());
                break;
            case 8:
                addDetailIfNotNull(details, subItemDTO.getW8D1Date(), subItemDTO.getW8D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW8D2Date(), subItemDTO.getW8D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW8D3Date(), subItemDTO.getW8D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW8D4Date(), subItemDTO.getW8D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW8D5Date(), subItemDTO.getW8D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW8D6Date(), subItemDTO.getW8D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW8D7Date(), subItemDTO.getW8D7Qty());
                break;
            case 9:
                addDetailIfNotNull(details, subItemDTO.getW9D1Date(), subItemDTO.getW9D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW9D2Date(), subItemDTO.getW9D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW9D3Date(), subItemDTO.getW9D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW9D4Date(), subItemDTO.getW9D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW9D5Date(), subItemDTO.getW9D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW9D6Date(), subItemDTO.getW9D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW9D7Date(), subItemDTO.getW9D7Qty());
                break;
            case 10:
                addDetailIfNotNull(details, subItemDTO.getW10D1Date(), subItemDTO.getW10D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW10D2Date(), subItemDTO.getW10D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW10D3Date(), subItemDTO.getW10D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW10D4Date(), subItemDTO.getW10D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW10D5Date(), subItemDTO.getW10D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW10D6Date(), subItemDTO.getW10D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW10D7Date(), subItemDTO.getW10D7Qty());
                break;
            case 11:
                addDetailIfNotNull(details, subItemDTO.getW11D1Date(), subItemDTO.getW11D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW11D2Date(), subItemDTO.getW11D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW11D3Date(), subItemDTO.getW11D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW11D4Date(), subItemDTO.getW11D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW11D5Date(), subItemDTO.getW11D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW11D6Date(), subItemDTO.getW11D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW11D7Date(), subItemDTO.getW11D7Qty());
                break;
            case 12:
                addDetailIfNotNull(details, subItemDTO.getW12D1Date(), subItemDTO.getW12D1Qty());
                addDetailIfNotNull(details, subItemDTO.getW12D2Date(), subItemDTO.getW12D2Qty());
                addDetailIfNotNull(details, subItemDTO.getW12D3Date(), subItemDTO.getW12D3Qty());
                addDetailIfNotNull(details, subItemDTO.getW12D4Date(), subItemDTO.getW12D4Qty());
                addDetailIfNotNull(details, subItemDTO.getW12D5Date(), subItemDTO.getW12D5Qty());
                addDetailIfNotNull(details, subItemDTO.getW12D6Date(), subItemDTO.getW12D6Qty());
                addDetailIfNotNull(details, subItemDTO.getW12D7Date(), subItemDTO.getW12D7Qty());
                break;
            default:
                throw new RuntimeException("不支持的周数：" + week);
        }
    }

    private void addDetailIfNotNull(List<SapWeekForecastItemDetail> details, Integer date, String qty) {
        if (date != null && StringUtils.isNotEmpty(qty)) {
            SapWeekForecastItemDetail detail = new SapWeekForecastItemDetail();
            detail.setForecastDate(date);
            detail.setForecastQty(SapObjectConverter.convertSapNumber(qty));
            detail.setCreateTime(DateUtils.getNowDate());
            details.add(detail);
        }
    }

    //    @ApiOperation("查询最新周预测数据")
    @GetMapping("/getLatestWeekForecast")
    public AjaxResult getLatestWeekForecast(SapWeekForecast weekForecast) {
        try {
            SapWeekForecast latestForecast = sapForecastService.getLatestWeekForecast(weekForecast);
            if (latestForecast == null) {
                return AjaxResult.success(false);
            }
            return AjaxResult.success("N".equals(latestForecast.getIsRead()));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询周预测数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("下载周预测数据pdf")
    @GetMapping("/downloadWeekForecast")
    public AjaxResult downloadWeekForecast(SapWeekForecast weekForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz) {
        return sapForecastService.downloadWeekForecast(weekForecast, tz);
    }

    //    @ApiOperation("下载周预测数据txt")
    @GetMapping("/downloadWeekForecastTxt")
    public AjaxResult downloadWeekForecastTxt(SapWeekForecast weekForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz) {
        return sapForecastService.downloadWeekForecastTxt(weekForecast, tz);
    }

    //    @ApiOperation("下载3个月预测数据pdf")
    @GetMapping("/downloadMonthForecast")
    public AjaxResult downloadMonthForecast(SapMonthForecast monthForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz) {
        return sapForecastService.downloadMonthForecast(monthForecast, tz);
    }

    //    @ApiOperation("下载3个月预测数据txt")
    @GetMapping("/downloadMonthForecastTxt")
    public AjaxResult downloadMonthForecastTxt(SapMonthForecast monthForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz) {
        return sapForecastService.downloadMonthForecastTxt(monthForecast, tz);
    }

    //    @ApiOperation("获取最新3个月预测数据")
    @GetMapping("/getLatestMonthForecast")
    public AjaxResult getLatestMonthForecast(SapMonthForecast monthForecast) {
        try {
            SapMonthForecast latestForecast = sapForecastService.getLatestMonthForecast(monthForecast);
            if (latestForecast == null) {
                return AjaxResult.success(false);
            }
            return AjaxResult.success("N".equals(latestForecast.getIsRead()));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询3个月预测数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("接收年预测")
    @PostMapping("/receiveYearForecast")
    @Log(title = "接收年预测", businessType = BusinessType.IMPORT)
    public SapApiResult receiveYearForecast(@Valid @RequestBody SapYearForecastRequestDTO requestDTO) {
        try {
            for (SapYearForecastItemDTO itemDTO : requestDTO.getItems()) {
                SapYearForecast forecast = new SapYearForecast();
                BeanUtils.copyProperties(itemDTO, forecast);
                forecast.setCreateTime(DateUtils.getNowDate());

                List<SapYearForecastItem> subItems = new ArrayList<>();
                for (SapYearForecastSubItemDTO subItemDTO : itemDTO.getSubItems()) {
                    SapYearForecastItem item = new SapYearForecastItem();
                    BeanUtils.copyProperties(subItemDTO, item);
                    item.setCreateTime(DateUtils.getNowDate());

                    // 处理bstrf字段，去除空格并转换为BigDecimal
                    if (StringUtils.isNotEmpty(subItemDTO.getBstrf())) {
                        item.setBstrf(new BigDecimal(subItemDTO.getBstrf().trim()));
                    }

                    // 计算上半年合计
                    BigDecimal firstHalfTotal = calculateHalfYearTotal(item, true);
                    item.setFirstHalfTotal(firstHalfTotal);

                    // 计算下半年合计
                    BigDecimal secondHalfTotal = calculateHalfYearTotal(item, false);
                    item.setSecondHalfTotal(secondHalfTotal);

                    // 计算年度合计
                    item.setYearTotal(firstHalfTotal.add(secondHalfTotal));

                    subItems.add(item);
                }

                forecast.setSubItems(subItems);
                sapForecastService.saveYearForecast(forecast);
            }
            return SapApiResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return SapApiResult.error("处理年预测数据失败：" + e.getMessage());
        }
    }

    /**
     * 计算半年合计
     * @param item 预测明细
     * @param isFirstHalf true:上半年 false:下半年
     */
    private BigDecimal calculateHalfYearTotal(SapYearForecastItem item, boolean isFirstHalf) {
        BigDecimal total = BigDecimal.ZERO;
        if (isFirstHalf) {
            // 计算1-6月合计
            total = total.add(item.getGsmng1() != null ? item.getGsmng1() : BigDecimal.ZERO)
                    .add(item.getGsmng2() != null ? item.getGsmng2() : BigDecimal.ZERO)
                    .add(item.getGsmng3() != null ? item.getGsmng3() : BigDecimal.ZERO)
                    .add(item.getGsmng4() != null ? item.getGsmng4() : BigDecimal.ZERO)
                    .add(item.getGsmng5() != null ? item.getGsmng5() : BigDecimal.ZERO)
                    .add(item.getGsmng6() != null ? item.getGsmng6() : BigDecimal.ZERO);
        } else {
            // 计算7-12月合计
            total = total.add(item.getGsmng7() != null ? item.getGsmng7() : BigDecimal.ZERO)
                    .add(item.getGsmng8() != null ? item.getGsmng8() : BigDecimal.ZERO)
                    .add(item.getGsmng9() != null ? item.getGsmng9() : BigDecimal.ZERO)
                    .add(item.getGsmng10() != null ? item.getGsmng10() : BigDecimal.ZERO)
                    .add(item.getGsmng11() != null ? item.getGsmng11() : BigDecimal.ZERO)
                    .add(item.getGsmng12() != null ? item.getGsmng12() : BigDecimal.ZERO);
        }
        return total;
    }

    //    @ApiOperation("获取最新年预测数据")
    @GetMapping("/getLatestYearForecast")
    public AjaxResult getLatestYearForecast(SapYearForecast yearForecast) {
        try {
            SapYearForecast latestForecast = sapForecastService.getLatestYearForecast(yearForecast);
            if (latestForecast == null) {
                return AjaxResult.success(false);
            }
            return AjaxResult.success("N".equals(latestForecast.getIsRead()));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("查询年预测数据失败：" + e.getMessage());
        }
    }

    //    @ApiOperation("下载年预测数据pdf")
    @GetMapping("/downloadYearForecast")
    public AjaxResult downloadYearForecast(SapYearForecast yearForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz) {
        return sapForecastService.downloadYearForecast(yearForecast, tz);
    }

    //    @ApiOperation("下载年预测数据txt")
    @GetMapping("/downloadYearForecastTxt")
    public AjaxResult downloadYearForecastTxt(SapYearForecast yearForecast, @RequestHeader(value="X-User-Timezone", defaultValue = "GMT+9") String tz) {
        return sapForecastService.downloadYearForecastTxt(yearForecast, tz);
    }

}
