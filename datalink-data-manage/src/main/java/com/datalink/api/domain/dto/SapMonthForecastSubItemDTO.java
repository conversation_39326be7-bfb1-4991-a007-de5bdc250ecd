package com.datalink.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SapMonthForecastSubItemDTO {
    @JsonProperty("MATNR") private String matnr;
    @JsonProperty("PLWRK") private String plwrk;
    @JsonProperty("ZMETHD") private String zmethd;
    @JsonProperty("WANST") private String wanst;
    @JsonProperty("DISPO") private String dispo;
    @JsonProperty("BESKZ") private String beskz;
    @JsonProperty("BSTRF") private String bstrf;
    @JsonProperty("MEINS") private String meins;
    @JsonProperty("ZMONTH") private String zmonth;
    @JsonProperty("GSMNG1") private BigDecimal gsmng1;
    @JsonProperty("GSMNG2") private BigDecimal gsmng2;
    @JsonProperty("GSMNG3") private BigDecimal gsmng3;
    @JsonProperty("GSMNG_T") private BigDecimal gsmngT;
    @JsonProperty("SEIKU") private String seiku;
    @JsonProperty("JYOKEN") private String jyoken;
    @JsonProperty("KANSAN") private String kansan;
    @JsonProperty("RYOSAN_DANKIRI") private String ryosanDankiri;
    @JsonProperty("RYOSAN_DANKIRI_M") private String ryosanDankiriM;
    @JsonProperty("RYOSAN_DANKIRI_YM") private String ryosanDankiriYm;
} 