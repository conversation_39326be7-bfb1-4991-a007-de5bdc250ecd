package com.datalink.datamanage.service.impl;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.SapDeliveryPlan;
import com.datalink.api.domain.SapDeliveryPlanDetail;
import com.datalink.api.domain.dto.SapDeliveryPlanDTO;
import com.datalink.api.domain.dto.SapRequestDTO;
import com.datalink.common.annotation.DataScope;
import com.datalink.common.config.RuoYiConfig;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.core.domain.model.LoginUser;
import com.datalink.common.core.page.TableDataInfo;
import com.datalink.common.utils.*;
import com.datalink.datamanage.mapper.SapDeliveryPlanMapper;
import com.datalink.datamanage.service.ISapDeliveryPlanService;
import com.datalink.framework.web.service.PermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.DayOfWeek;
import java.util.*;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;

/**
 * 支给计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-03
 */
@Service
public class SapDeliveryPlanServiceImpl implements ISapDeliveryPlanService
{
    private static final Logger log = LoggerFactory.getLogger(SapDeliveryPlanServiceImpl.class);

    @Autowired
    private SapDeliveryPlanMapper sapDeliveryPlanMapper;

    @Autowired
    private PermissionService permissionService;

    /**
     * 查询支给计划明细
     *
     * @param sapDeliveryPlan 支给计划
     * @return 支给计划
     */
    @Override
    public List<SapDeliveryPlanDetail> selectTblSapDeliveryPlanDetail(SapDeliveryPlan sapDeliveryPlan)
    {
        return sapDeliveryPlanMapper.selectTblSapDeliveryPlanDetail(sapDeliveryPlan);
    }

    /**
     * 查询支给计划列表
     *
     * @param tblSapDeliveryPlan 支给计划
     * @return 支给计划
     */
    @Override
    @DataScope(supplierAlias = "a")
    public List<SapDeliveryPlan> selectTblSapDeliveryPlanList(SapDeliveryPlan tblSapDeliveryPlan)
    {
        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        // 判断是否是承运商角色
        if (permissionService.hasRole("carrier") && !loginUser.getUser().isAdmin()) {
            // 获取当前用户的备注（即配置的可以查看的depot）
            String depot = loginUser.getUser().getRemark();
            // 设置查询条件 - 只能查看与自己岗位编码匹配的数据
            tblSapDeliveryPlan.setDepot(depot);
        }
        return sapDeliveryPlanMapper.selectTblSapDeliveryPlanList(tblSapDeliveryPlan);
    }

    /**
     * 新增支给计划/实际
     *
     * @param request 支给计划/实际
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertTblSapDeliveryPlan(SapRequestDTO<SapDeliveryPlanDTO> request)
    {
        request.getItems().forEach((sapDeliveryPlanDTO) -> {
            try {
                // 参数校验
                if (StringUtils.isAnyBlank(
                        sapDeliveryPlanDTO.getClient(),
//                        sapDeliveryPlanDTO.getDepot(),
                        sapDeliveryPlanDTO.getFactory(),
                        sapDeliveryPlanDTO.getPartNumber())) {
                    throw new IllegalArgumentException("client、factory、partNumber不能为空");
                }

                // 转换并保存主表数据
                SapDeliveryPlan sapDeliveryPlan = new SapDeliveryPlan();
                BeanUtils.copyProperties(sapDeliveryPlanDTO, sapDeliveryPlan);

                if (sapDeliveryPlan.getDepot() == null) {
                    sapDeliveryPlan.setDepot("");
                }

                // 执行主表插入或更新，并确保获取到ID
                sapDeliveryPlanMapper.insertOrUpdateDeliveryPlan(sapDeliveryPlan);

                // 检查是否成功获取到主表ID
                if (sapDeliveryPlan.getDeliveryPlanId() == null) {
                    // 如果没有获取到ID，需要查询获取
                    SapDeliveryPlan queryPlan = new SapDeliveryPlan();
                    queryPlan.setClient(sapDeliveryPlanDTO.getClient());
                    queryPlan.setDepot(sapDeliveryPlanDTO.getDepot());
                    queryPlan.setFactory(sapDeliveryPlanDTO.getFactory());
                    queryPlan.setPartNumber(sapDeliveryPlanDTO.getPartNumber());
                    List<SapDeliveryPlan> existingPlans = sapDeliveryPlanMapper.selectTblSapDeliveryPlanList(queryPlan);
                    if (!existingPlans.isEmpty()) {
                        sapDeliveryPlan.setDeliveryPlanId(existingPlans.get(0).getDeliveryPlanId());
                    } else {
                        throw new RuntimeException("Failed to get delivery plan id");
                    }
                }

                // 处理明细数据
                if (StringUtils.isNotEmpty(sapDeliveryPlanDTO.getSupplyPlanDate())
                        || StringUtils.isNotEmpty(sapDeliveryPlanDTO.getSupplyActualDate())) {
                    // 确保主表ID存在
                    if (sapDeliveryPlan.getDeliveryPlanId() == null) {
                        throw new RuntimeException("Missing delivery plan id");
                    }
                    SapDeliveryPlanDetail detail = new SapDeliveryPlanDetail();
                    detail.setDeliveryPlanId(sapDeliveryPlan.getDeliveryPlanId());
                    detail.setSupplyPlanDate(sapDeliveryPlanDTO.getSupplyPlanDate());
                    detail.setSupplyPlanQuantity(sapDeliveryPlanDTO.getSupplyPlanQuantity());
                    detail.setDeleteFlag(sapDeliveryPlanDTO.getDeleteFlag());
                    detail.setRsnum(sapDeliveryPlanDTO.getRsnum());
                    detail.setRspos(sapDeliveryPlanDTO.getRspos());
                    detail.setFormatType(sapDeliveryPlanDTO.getFormatType());
                    detail.setDeliveryNote(sapDeliveryPlanDTO.getDeliveryNote());
                    detail.setDeliveryNoteItemNumber(sapDeliveryPlanDTO.getDeliveryNoteItemNumber());
                    detail.setSupplyActualQuantity(sapDeliveryPlanDTO.getSupplyActualQuantity());
                    detail.setSupplyActualWeight(sapDeliveryPlanDTO.getSupplyActualWeight());
                    detail.setSupplyActualDate(sapDeliveryPlanDTO.getSupplyActualDate());
                    if (sapDeliveryPlanDTO.getLastMonthRemaining() != null) {
                        if ("40".equals(sapDeliveryPlanDTO.getFormatType())) {
                            // 计划
                            detail.setLastMonthRemainingPlan(SapObjectConverter.convertSapNumber(sapDeliveryPlanDTO.getLastMonthRemaining()));
                        } else if ("42".equals(sapDeliveryPlanDTO.getFormatType())) {
                            // 实际
                            detail.setLastMonthRemainingActual(SapObjectConverter.convertSapNumber(sapDeliveryPlanDTO.getLastMonthRemaining()));
                        }
                    }

                    // 插入或更新明细表（计划删除时更新删除标志）
                    if ("40".equals(sapDeliveryPlanDTO.getFormatType())) {
                        sapDeliveryPlanMapper.insertOrUpdateDeliveryPlanDetail(detail);
                    } else if ("42".equals(sapDeliveryPlanDTO.getFormatType())) {
                        // 执行明细表插入或更新
                        sapDeliveryPlanMapper.insertOrUpdateDeliveryActualDetail(detail);
                    }
                }
            } catch (Exception e) {
                log.error("处理支给计划数据失败", e);
                throw new RuntimeException("处理支给计划数据失败: " + e.getMessage());
            }
        });
    }

    /**
     * 修改支给计划
     *
     * @param tblSapDeliveryPlan 支给计划
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblSapDeliveryPlan(SapDeliveryPlan tblSapDeliveryPlan)
    {
        sapDeliveryPlanMapper.deleteTblSapDeliveryPlanDetailByDeliveryPlanId(tblSapDeliveryPlan.getDeliveryPlanId());
        insertTblSapDeliveryPlanDetail(tblSapDeliveryPlan);
        return sapDeliveryPlanMapper.updateTblSapDeliveryPlan(tblSapDeliveryPlan);
    }

    /**
     * 新增供应计划明细信息
     *
     * @param tblSapDeliveryPlan 支给计划对象
     */
    public void insertTblSapDeliveryPlanDetail(SapDeliveryPlan tblSapDeliveryPlan)
    {
        List<SapDeliveryPlanDetail> tblSapDeliveryPlanDetailList = tblSapDeliveryPlan.getSapDeliveryPlanDetailList();
        Long deliveryPlanId = tblSapDeliveryPlan.getDeliveryPlanId();
        if (StringUtils.isNotNull(tblSapDeliveryPlanDetailList))
        {
            List<SapDeliveryPlanDetail> list = new ArrayList<SapDeliveryPlanDetail>();
            for (SapDeliveryPlanDetail tblSapDeliveryPlanDetail : tblSapDeliveryPlanDetailList)
            {
                tblSapDeliveryPlanDetail.setDeliveryPlanId(deliveryPlanId);
                list.add(tblSapDeliveryPlanDetail);
            }
            if (list.size() > 0)
            {
                sapDeliveryPlanMapper.batchTblSapDeliveryPlanDetail(list);
            }
        }
    }

    /**
     * 将支给计划数据转换为周数据格式
     */
    @Override
    public Map<String, Object> convertToWeekData(SapDeliveryPlan deliveryPlan, String selectedYearMonth) {
        deliveryPlan.setSelectedYearMonth(selectedYearMonth);
        Map<String, Object> result = new HashMap<>();

        // 保存明细数据的引用
        List<SapDeliveryPlanDetail> detailList = this.selectTblSapDeliveryPlanDetail(deliveryPlan);
        // 清空明细列表，避免在basicInfo中返回
        deliveryPlan.setSapDeliveryPlanDetailList(null);
        result.put("basicInfo", deliveryPlan);

        // 初始化空的上月剩余数据
        Map<String, BigDecimal> lastMonthRemainingPlanMap = new HashMap<>();
//        lastMonthRemainingPlanMap.put("lastMonthRemaining", BigDecimal.ZERO);
        Map<String, BigDecimal> lastMonthRemainingActualMap = new HashMap<>();
//        lastMonthRemainingActualMap.put("lastMonthRemaining", BigDecimal.ZERO);
        result.put("planData", lastMonthRemainingPlanMap);
        result.put("actualData", lastMonthRemainingActualMap);

        // 使用入参中的selectedYearMonth
        YearMonth yearMonth = YearMonth.parse(selectedYearMonth);
        
        // 获取月份的第一天和最后一天
        LocalDate firstDayOfMonth = yearMonth.atDay(1);
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();

        // 调整第一天到最近的周一（向前调整）
        LocalDate weekStart = firstDayOfMonth;
        while (weekStart.getDayOfWeek() != DayOfWeek.MONDAY) {
            weekStart = weekStart.minusDays(1);
        }

        // 初始化周数据
        List<Map<String, Object>> weeksData = new ArrayList<>();
        Map<String, Object> currentWeek = null;
        List<Map<String, Object>> currentDays = null;
        int currentWeekNumber = 1;

        // 如果有明细数据，使用明细数据构建日期映射
        Map<String, Map<String, Object>> dateDataMap = new HashMap<>();
        if (detailList != null && !detailList.isEmpty()) {
            // 更新上月剩余数据
            lastMonthRemainingPlanMap.put("lastMonthRemaining", detailList.get(0).getLastMonthRemainingPlan());
            lastMonthRemainingActualMap.put("lastMonthRemaining", detailList.get(0).getLastMonthRemainingActual());
            
            for (SapDeliveryPlanDetail detail : detailList) {
                String date = detail.getSupplyPlanDate();
                if (date != null) {
                    Map<String, Object> dayData = dateDataMap.computeIfAbsent(date, k -> {
                        Map<String, Object> newDay = new HashMap<>();
                        newDay.put("date", date);
                        LocalDate localDate = LocalDate.parse(date);
                        int dayOfWeek = localDate.getDayOfWeek().getValue();
                        dayOfWeek = dayOfWeek == 7 ? 0 : dayOfWeek;
                        newDay.put("dayOfWeek", dayOfWeek);
                        return newDay;
                    });

                    if (detail.getSupplyPlanQuantity() != null && !detail.getSupplyPlanQuantity().isEmpty()) {
                        dayData.put("plan", new BigDecimal(detail.getSupplyPlanQuantity()));
                    }
                    if (detail.getSupplyActualQuantity() != null && !detail.getSupplyActualQuantity().isEmpty()) {
                        dayData.put("actual", new BigDecimal(detail.getSupplyActualQuantity()));
                    }
                }
            }
        }

        // 从调整后的周一开始遍历，直到月末
        for (LocalDate date = weekStart; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
            // 计算星期几 (周日为0，周一到周六为1-6)
            int dayOfWeek = date.getDayOfWeek().getValue();
            dayOfWeek = dayOfWeek == 7 ? 0 : dayOfWeek;

            // 每到周一开始新的一周
            if (date.getDayOfWeek() == DayOfWeek.MONDAY) {
                currentWeek = new HashMap<>();
                currentWeek.put("weekNumber", currentWeekNumber++);
                currentDays = new ArrayList<>();
                currentWeek.put("days", currentDays);
                weeksData.add(currentWeek);
            }

            // 只添加当月的日期
            if (!date.isBefore(firstDayOfMonth)) {
                String dateStr = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
                int finalDayOfWeek = dayOfWeek;
                Map<String, Object> dayData = dateDataMap.getOrDefault(dateStr, new HashMap<String, Object>() {{
                    put("date", dateStr);
                    put("plan", null);
                    put("actual", null);
                    put("dayOfWeek", finalDayOfWeek);
                }});

                currentDays.add(dayData);
            }
        }

        result.put("weeksData", weeksData);
        return result;
    }

    @Override
    public AjaxResult downloadDeliveryPlanTxt(TableDataInfo result) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> planList = (List<Map<String, Object>>) result.getRows();
            if (planList == null || planList.isEmpty()) {
                return AjaxResult.error(MessageUtils.message("supply.plan.no.data"));
            }

            Map<String, Object> firstPlan = planList.get(0);
            SapDeliveryPlan basicInfo = (SapDeliveryPlan) firstPlan.get("basicInfo");
            
            String fileName = String.format("HM05_%s.txt", UUID.randomUUID());
            String filePath = RuoYiConfig.getDownloadPath() + fileName;
            File file = new File(filePath);

            try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file), "Shift-JIS"))) {
                
                for (Map<String, Object> plan : planList) {
                    basicInfo = (SapDeliveryPlan) plan.get("basicInfo");
                    Map<String, Object> planData = (Map<String, Object>) plan.get("planData");
                    List<Map<String, Object>> weeksData = (List<Map<String, Object>>) plan.get("weeksData");
                    
                    // 创建一个294字符长的字符数组，初始化为空格
                    char[] record = new char[294];
                    Arrays.fill(record, ' ');
                    
                    // 按照规范填充数据
                    TextFileUtil.fillField(record, "40", 1, 2, "F/#");                             // F/# (1-2)
                    TextFileUtil.fillField(record, basicInfo.getClient(), 3, 7, "取引先");  // 取引先 (3-7)
                    TextFileUtil.fillField(record, basicInfo.getDepot(), 8, 9, "デポ");    // デポ (8-9)
                    TextFileUtil.fillField(record, basicInfo.getFactory(), 10, 13, "要求工場"); // 要求工場 (10-13)
                    TextFileUtil.fillField(record, basicInfo.getPartNumber(), 14, 38, "部品番号"); // 部品番号 (14-38)
                    TextFileUtil.fillField(record, basicInfo.getSelectedYearMonth().replace("-", ""), 39, 44, "対象年月"); // 対象年月 (39-44)
                    TextFileUtil.fillField(record, basicInfo.getDeliveryMethod(), 45, 46, "納入方式"); // 納入方式 (45-46)
                    TextFileUtil.fillField(record, basicInfo.getDeliveryLocation(), 47, 53, "納入場所"); // 納入場所 (47-53)
                    TextFileUtil.fillField(record, basicInfo.getSupplyType(), 54, 54, "支給区分"); // 支給区分 (54)
                    TextFileUtil.fillField(record, basicInfo.getSupplyMethod(), 55, 55, "支給方式"); // 支給方式 (55)
                    TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(basicInfo.getSnep()), 56, 62, 0); // ＳＮＥＰ (56-62)
                    TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(basicInfo.getAllocationLot() == null ? "0" : basicInfo.getAllocationLot()), 63, 69, 0); // 手配ロットサイズ (63-69)
                    TextFileUtil.fillField(record, basicInfo.getBasicUnit(), 70, 72, "基本数量単位"); // 基本数量単位 (70-72)
                    TextFileUtil.fillNumber(record, SapObjectConverter.convertSapNumber(basicInfo.getClientInventory()), 73, 83, 3); // 取引先在庫 (73-83,3)
                    
                    // 上月剩余计划数
                    TextFileUtil.fillNumber(record, planData.get("lastMonthRemaining") == null ? BigDecimal.ZERO : new BigDecimal(planData.get("lastMonthRemaining").toString()), 84, 91, 0); // 前月残計画数 (84-91)
                    
                    // 当月日别计划数（35个位置，每个5位数字）
                    int startPos = 92;
                    BigDecimal monthTotal = BigDecimal.ZERO;
                    
                    // 遍历weeksData中的每一天
                    for (Map<String, Object> week : weeksData) {
                        List<Map<String, Object>> days = (List<Map<String, Object>>) week.get("days");
                        for (Map<String, Object> day : days) {
                            Object planValue = day.get("plan");
                            BigDecimal planQty = planValue != null ? new BigDecimal(planValue.toString()) : BigDecimal.ZERO;
                            
                            TextFileUtil.fillNumber(record, planQty,
                                startPos,
                                startPos + 4,
                                0);
                            monthTotal = monthTotal.add(planQty);
                            startPos += 5;
                            
                            if (startPos > 266) break; // 超过35天的数据不处理
                        }
                    }

                    // 月合计数
                    TextFileUtil.fillNumber(record, monthTotal, 267, 274, 0); // 月合計数 (267-274)

                    // 日期和时间相关字段
                    TextFileUtil.fillField(record, basicInfo.getConfirmedDate(), 275, 282, "計画確定年月日"); // 計画確定年月日 (275-282)
                    TextFileUtil.fillField(record, basicInfo.getCreationDate().replace("-", ""), 283, 290, "作成年月日"); // 作成年月日 (283-290)
//                    TextFileUtil.fillField(record, DateUtils.parseDateToStr("HHmm", DateUtils.getNowDate()), 291, 294, "作成時間"); // 作成時間 (291-294)
                    TextFileUtil.fillField(record, "", 291, 294, "作成時間"); // 作成時間 (291-294)

                    writer.write(record);
                    writer.write("\r\n");
                }
            }

            return AjaxResult.success(fileName);
        } catch (Exception e) {
            log.error("生成支给计划txt文件失败", e);
            return AjaxResult.error("生成支给计划txt文件失败：" + e.getMessage());
        }
    }
}
