<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datalink.datamanage.mapper.TransportPlanMapper">
    
    <resultMap type="TransportPlan" id="TransportPlanResult">
        <result property="transportPlanId"    column="transport_plan_id"    />
        <result property="no"    column="no"    />
        <result property="status"    column="status"    />
        <result property="depot"    column="depot"    />
        <result property="materialFactor"    column="material_factor"    />
        <result property="way"    column="way"    />
        <result property="clientCode"    column="supp_code"    />
        <result property="deliveryLocation"    column="delivery_location"    />
        <result property="factory"    column="factory"    />
        <result property="pickupDate"    column="pickup_date"    />
        <result property="pickupTime"    column="pickup_time"    />
        <result property="carrierPickupTime"    column="carrier_pickup_time"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="deliveryTime"    column="delivery_time"    />
        <result property="port"    column="port"    />
        <result property="weightTotal"    column="weight_total"    />
        <result property="totalQuantity"    column="total_quantity"    />
        <result property="average"    column="average"    />
        <result property="ticketNo"    column="ticket_no"    />
        <result property="company"    column="company"    />
        <result property="pickupQuantity"    column="pickup_quantity"    />
        <result property="carType"    column="car_type"    />
        <result property="carNo"    column="car_no"    />
        <result property="driver"    column="driver"    />
        <result property="deliveryLocationName"    column="delivery_location_name"    />
        <result property="companyName"    column="company_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy" column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy" column="update_by"    />
    </resultMap>

    <sql id="selectTransportPlanVo">
        select transport_plan_id, no, status, depot, material_factor, way, supp_code, delivery_location, factory, pickup_date, pickup_time, carrier_pickup_time, delivery_date, delivery_time, port, weight_total, total_quantity, average, ticket_no, company, pickup_quantity, car_type, car_no, driver, delivery_location_name, company_name, create_time, create_by, update_time, update_by from tbl_transport_plan a
    </sql>

    <select id="selectTransportPlanList" parameterType="TransportPlan" resultMap="TransportPlanResult">
        <include refid="selectTransportPlanVo"/>
        <where>  
            <if test="no != null "> and no = #{no}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="depot != null  and depot != ''"> and depot = #{depot}</if>
            <if test="materialFactor != null  and materialFactor != ''"> and material_factor = #{materialFactor}</if>
            <if test="way != null  and way != ''"> and way = #{way}</if>
            <if test="clientCode != null  and clientCode != ''"> and supp_code = #{clientCode}</if>
            <if test="deliveryLocation != null  and deliveryLocation != ''"> and delivery_location = #{deliveryLocation}</if>
            <if test="factory != null  and factory != ''"> and factory = #{factory}</if>
            <if test="pickupTime != null "> and pickup_time = #{pickupTime}</if>
            <if test="params.pickupDateBegin != null and params.pickupDateBegin != '' and params.pickupDateEnd != null and params.pickupDateEnd != ''">
                and concat(pickup_date, ' ', pickup_time) between #{params.pickupDateBegin} and #{params.pickupDateEnd}
            </if>
            <if test="carrierPickupTime != null "> and carrier_pickup_time = #{carrierPickupTime}</if>
            <if test="params.deliveryDateBegin != null and params.deliveryDateBegin != '' and params.deliveryDateEnd != null and params.deliveryDateEnd != ''">
                and concat(delivery_date, ' ', delivery_time) between #{params.deliveryDateBegin} and #{params.deliveryDateEnd}
            </if>
            <if test="port != null  and port != ''"> and port = #{port}</if>
            <if test="weightTotal != null "> and weight_total = #{weightTotal}</if>
            <if test="totalQuantity != null "> and total_quantity = #{totalQuantity}</if>
            <if test="average != null "> and average = #{average}</if>
            <if test="ticketNo != null  and ticketNo != ''"> and ticket_no = #{ticketNo}</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="pickupQuantity != null "> and pickup_quantity = #{pickupQuantity}</if>
            <if test="carType != null  and carType != ''"> and car_type = #{carType}</if>
            <if test="carNo != null  and carNo != ''"> and car_no = #{carNo}</if>
            <if test="driver != null  and driver != ''"> and driver = #{driver}</if>
            <if test="deliveryLocationName != null  and deliveryLocationName != ''"> and delivery_location_name like concat('%', #{deliveryLocationName}, '%')</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="createBy != null  and createBy != ''">
                and create_by = #{createBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
            <if test="updateBy != null  and updateBy != ''">
                and update_by = #{updateBy}</if>
            <if test="userId != null and userId != ''">
                AND a.company IN ( SELECT sup.post_id FROM sys_user_post sup WHERE sup.user_id = #{userId} )
            </if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectTransportPlanById" parameterType="Long" resultMap="TransportPlanResult">
        <include refid="selectTransportPlanVo"/>
        where transport_plan_id = #{transportPlanId}
    </select>
        
    <insert id="insertTransportPlan" parameterType="TransportPlan" useGeneratedKeys="true" keyProperty="transportPlanId">
        insert into tbl_transport_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="no != null">no,</if>
            <if test="status != null">status,</if>
            <if test="depot != null">depot,</if>
            <if test="materialFactor != null">material_factor,</if>
            <if test="way != null">way,</if>
            <if test="clientCode != null">supp_code,</if>
            <if test="deliveryLocation != null">delivery_location,</if>
            <if test="factory != null">factory,</if>
            <if test="pickupDate != null">pickup_date,</if>
            <if test="pickupTime != null">pickup_time,</if>
            <if test="carrierPickupTime != null">tbl_transport_plan.carrier_pickup_time,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="deliveryTime != null">delivery_time,</if>
            <if test="port != null">port,</if>
            <if test="weightTotal != null">weight_total,</if>
            <if test="totalQuantity != null">total_quantity,</if>
            <if test="average != null">average,</if>
            <if test="ticketNo != null">ticket_no,</if>
            <if test="company != null">company,</if>
            <if test="pickupQuantity != null">pickup_quantity,</if>
            <if test="carType != null">car_type,</if>
            <if test="carNo != null">car_no,</if>
            <if test="driver != null">driver,</if>
            <if test="deliveryLocationName != null">delivery_location_name,</if>
            <if test="companyName != null">company_name,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="no != null">#{no},</if>
            <if test="status != null">#{status},</if>
            <if test="depot != null">#{depot},</if>
            <if test="materialFactor != null">#{materialFactor},</if>
            <if test="way != null">#{way},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="deliveryLocation != null">#{deliveryLocation},</if>
            <if test="factory != null">#{factory},</if>
            <if test="pickupDate != null">#{pickupDate},</if>
            <if test="pickupTime != null">#{pickupTime},</if>
            <if test="carrierPickupTime != null">#{carrierPickupTime},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="deliveryTime != null">#{deliveryTime},</if>
            <if test="port != null">#{port},</if>
            <if test="weightTotal != null">#{weightTotal},</if>
            <if test="totalQuantity != null">#{totalQuantity},</if>
            <if test="average != null">#{average},</if>
            <if test="ticketNo != null">#{ticketNo},</if>
            <if test="company != null">#{company},</if>
            <if test="pickupQuantity != null">#{pickupQuantity},</if>
            <if test="carType != null">#{carType},</if>
            <if test="carNo != null">#{carNo},</if>
            <if test="driver != null">#{driver},</if>
            <if test="deliveryLocationName != null">#{deliveryLocationName},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateTransportPlan" parameterType="TransportPlan">
        update tbl_transport_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="no != null">no = #{no},</if>
            <if test="status != null">status = #{status},</if>
            <if test="depot != null">depot = #{depot},</if>
            <if test="materialFactor != null">material_factor = #{materialFactor},</if>
            <if test="way != null">way = #{way},</if>
            <if test="clientCode != null">supp_code = #{clientCode},</if>
            <if test="deliveryLocation != null">delivery_location = #{deliveryLocation},</if>
            <if test="factory != null">factory = #{factory},</if>
            <if test="pickupDate != null">pickup_date = #{pickupDate},</if>
            <if test="pickupTime != null">pickup_time = #{pickupTime},</if>
            <if test="carrierPickupTime != null">carrier_pickup_time = #{carrierPickupTime},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="port != null">port = #{port},</if>
            <if test="weightTotal != null">weight_total = #{weightTotal},</if>
            <if test="totalQuantity != null">total_quantity = #{totalQuantity},</if>
            <if test="average != null">average = #{average},</if>
            <if test="ticketNo != null">ticket_no = #{ticketNo},</if>
            <if test="company != null">company = #{company},</if>
            <if test="pickupQuantity != null">pickup_quantity = #{pickupQuantity},</if>
            <if test="carType != null">car_type = #{carType},</if>
            <if test="carNo != null">car_no = #{carNo},</if>
            <if test="driver != null">driver = #{driver},</if>
            <if test="deliveryLocationName != null">delivery_location_name = #{deliveryLocationName},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where transport_plan_id = #{transportPlanId}
    </update>

    <delete id="deleteTransportPlanById" parameterType="Long">
        delete from tbl_transport_plan where transport_plan_id = #{transportPlanId}
    </delete>

    <delete id="deleteTransportPlanByIds" parameterType="String">
        delete from tbl_transport_plan where transport_plan_id in 
        <foreach item="transportPlanId" collection="array" open="(" separator="," close=")">
            #{transportPlanId}
        </foreach>
    </delete>
</mapper>