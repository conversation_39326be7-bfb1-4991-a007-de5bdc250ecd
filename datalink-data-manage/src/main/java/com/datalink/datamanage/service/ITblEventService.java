package com.datalink.datamanage.service;

import com.datalink.datamanage.domain.TblEvent;

import java.util.List;

/**
 * 事件Service接口
 * 
 * <AUTHOR>
 * @date 2022-04-20
 */
public interface ITblEventService 
{
    /**
     * 查询事件
     * 
     * @param eventId 事件ID
     * @return 事件
     */
    public TblEvent selectTblEventById(Long eventId);

    /**
     * 查询事件列表
     * 
     * @param tblEvent 事件
     * @return 事件集合
     */
    public List<TblEvent> selectTblEventList(TblEvent tblEvent);

    /**
     * 新增事件
     * 
     * @param tblEvent 事件
     * @return 结果
     */
    public int insertTblEvent(TblEvent tblEvent);

    /**
     * 修改事件
     * 
     * @param tblEvent 事件
     * @return 结果
     */
    public int updateTblEvent(TblEvent tblEvent);

    /**
     * 批量删除事件
     * 
     * @param eventIds 需要删除的事件ID
     * @return 结果
     */
    public int deleteTblEventByIds(Long[] eventIds);

    /**
     * 删除事件信息
     * 
     * @param eventId 事件ID
     * @return 结果
     */
    public int deleteTblEventById(Long eventId);
}
