package com.datalink.api.controller;

import com.datalink.api.common.SapObjectConverter;
import com.datalink.api.domain.SapMaterialItem;
import com.datalink.common.annotation.Log;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.datamanage.domain.TblMaterial;
import com.datalink.datamanage.service.ITblMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 物料接口
 */
@Api("物料接口")
@Validated
@RestController
@RequestMapping("/api/material")
public class TblMaterialApi {

    @Autowired
    private ITblMaterialService tblMaterialService;

    private static final Logger logger = LoggerFactory.getLogger(TblMaterialApi.class);

    /**
     * 接收SAP物料数据
     */
    @ApiOperation("接收SAP物料数据")
    @PostMapping("/receive")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "接收物料数据", businessType = BusinessType.IMPORT)
    public AjaxResult receiveMaterial(@Valid @RequestBody SapMaterialItem materialItem) {
        try {
            // 转换SAP物料数据为系统物料对象
            TblMaterial material = SapObjectConverter.convertToTblMaterial(materialItem);
            
            // 检查物料是否已存在
            TblMaterial existingMaterial = tblMaterialService.selectTblMaterialByCode(material.getMaterialCode());
            
            if (existingMaterial != null) {
                // 物料已存在，执行更新
                material.setMaterialId(existingMaterial.getMaterialId());
                tblMaterialService.updateTblMaterial(material);
            } else {
                // 物料不存在，执行插入
                tblMaterialService.insertTblMaterial(material);
            }
            
        } catch (Exception e) {
            logger.error("处理SAP物料数据失败：", e);
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            return AjaxResult.error(e.getCause().getMessage());
            throw new RuntimeException("处理SAP物料数据失败：" + e.getCause().getMessage());
        }
        
        return AjaxResult.success();
    }
} 