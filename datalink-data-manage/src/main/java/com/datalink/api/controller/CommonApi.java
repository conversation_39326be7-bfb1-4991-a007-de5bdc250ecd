package com.datalink.api.controller;

import com.datalink.common.annotation.Log;
import com.datalink.common.constant.Constants;
import com.datalink.common.core.domain.AjaxResult;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.StringUtils;
import com.datalink.framework.web.service.SysLoginService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * swagger API基础接口
 *
 * <AUTHOR>
 */
@Api("API基础接口")
@RestController
@RequestMapping("/api")
public class CommonApi {
    @Autowired
    private SysLoginService loginService;

    @ApiOperation("登陆")
    @ApiImplicitParam(name = "loginEntity", value = "登陆信息", dataType = "LoginEntity")
    @PostMapping("/login")
    @Log(title = "登陆", businessType = BusinessType.OTHER)
    public AjaxResult login(@RequestBody LoginEntity user)
    {
        if (StringUtils.isNull(user) || StringUtils.isNull(user.getUsername()) || StringUtils.isNull(user.getPassword()))
        {
            return AjaxResult.error("用户名或密码不能为空");
        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.loginWithNameAndPass(user.getUsername(), user.getPassword());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    @ApiModel("登陆信息")
    static class LoginEntity{
        @ApiModelProperty("用户名称")
        private String username;

        @ApiModelProperty("用户密码")
        private String password;

        public LoginEntity(){}

        public LoginEntity(String username, String password) {
            this.username = username;
            this.password = password;
        }

        public String getUsername()
        {
            return username;
        }

        public void setUsername(String username)
        {
            this.username = username;
        }

        public String getPassword()
        {
            return password;
        }
    }
}
