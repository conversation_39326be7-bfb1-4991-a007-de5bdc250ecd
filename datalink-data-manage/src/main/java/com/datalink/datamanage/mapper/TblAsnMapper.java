package com.datalink.datamanage.mapper;

import java.util.List;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblAsn;
import com.datalink.datamanage.domain.TblAsnArticle;
import com.datalink.datamanage.domain.TblAsnItem;
import com.datalink.datamanage.domain.TblOrderAsnQuantity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * ASNMapper接口
 * 
 * <AUTHOR>
 * @date 2021-08-04
 */
@Mapper
public interface TblAsnMapper 
{
    /**
     * 查询ASN
     *
     * @param asn ASNID
     * @return ASN
     */
    @DataScope(supplierAlias = "a")
    public TblAsn selectTblAsnById(TblAsn asn);

    /**
     * 查询ASN
     *
     * @param asn ASNCode
     * @return ASN
     */
    @DataScope(supplierAlias = "a")
    public TblAsn selectTblAsnByAsnCode(TblAsn asn);

    /**
     * 查询ASN列表
     *
     * @param tblAsn ASN
     * @param is223X
     * @return ASN集合
     */
    public List<TblAsn> selectTblAsnList(@Param("tblAsn") TblAsn tblAsn, @Param("is223X") boolean is223X);

    /**
     * 查询ASN及行项目列表
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    public List<TblAsn> selectTblAsnWithItemList(TblAsn tblAsn);

    /**
     * 新增ASN
     * 
     * @param tblAsn ASN
     * @return 结果
     */
    public int insertTblAsn(TblAsn tblAsn);

    /**
     * 修改ASN
     * 
     * @param tblAsn ASN
     * @return 结果
     */
    public int updateTblAsn(TblAsn tblAsn);

    /**
     * 删除ASN
     * 
     * @param asnId ASNID
     * @return 结果
     */
    public int deleteTblAsnById(Long asnId);

    /**
     * 批量删除ASN
     * 
     * @param asnIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblAsnByIds(Long[] asnIds);

    /**
     * 批量删除ASN行项目
     * 
     * @param asnIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblAsnItemByAsnIds(Long[] asnIds);
    
    /**
     * 批量新增ASN行项目
     * 
     * @param tblAsnItemList ASN行项目列表
     * @return 结果
     */
    public int batchTblAsnItem(List<TblAsnItem> tblAsnItemList);

    /**
     * 批量新增ASN物料
     *
     * @param tblAsnArticleList ASN行项目列表
     * @return 结果
     */
    public int batchTblAsnArticle(List<TblAsnArticle> tblAsnArticleList);
    

    /**
     * 通过ASNID删除ASN行项目信息
     * 
     * @param asnId ASNID
     * @return 结果
     */
    public int deleteTblAsnItemByAsnId(Long asnId);

    /**
     * 查询ASN列表(接口专用)
     *
     * @param tblAsn ASN
     * @return ASN集合
     */
    public List<TblAsn> selectTblAsnFullList(TblAsn tblAsn);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询ASN(不包含行项目)
     *
     * @param asnId ASNID
     * @return ASN
     */
    public TblAsn selectTblAsnOnlyById(Long asnId);

    /**
     * 查询ASN行项目列表
     *
     * @param asnId ASNID
     * @return ASN行项目集合
     */
    public List<TblAsnItem> selectTblAsnItemByAsnId(Long asnId);

    /**
     * 查询ASN行项目列表
     *
     * @param tblAsnItem TblAsnItem
     * @return ASN行项目集合
     */
    public List<TblAsnItem> selectTblAsnItemList(TblAsnItem tblAsnItem);

    /**
     * 查询ASN行项目
     *
     * @param itemId ItemID
     * @return ASN行项目
     */
    public TblAsnItem selectTblAsnItemByItemId(Long itemId);

    /**
     * 查询ASN物料列表
     *
     * @param itemId ItemID
     * @return ASN物料列表
     */
    public List<TblAsnArticle> selectTblAsnArticleByItemId(Long itemId);

    /**
     * 查询ASN物料列表
     *
     * @param tblAsnArticle TblAsnArticle
     * @return ASN物料列表
     */
    public List<TblAsnArticle> selectTblAsnArticleList(TblAsnArticle tblAsnArticle);

    /**
     * 修改订单物料剩余未发数量
     *
     * @param quantity 增加数量
     * @return 结果
     */
    public int updateOrderArticleQuantity(TblOrderAsnQuantity quantity);

    /**
     * 删除asn物料时修正订单物料剩余数量
     *
     * @param asnId asnId
     * @return 结果
     */
    public int reduceOrderArticleQuantityByAsnId(Long asnId);

    /**
     * 新增asn物料时修正订单物料剩余数量
     *
     * @param asnId asnId
     * @return 结果
     */
    public int addOrderArticleQuantityByAsnId(Long asnId);

    /**
     * 删除asn时恢复订单物料剩余数量
     *
     * @param asnId asnId
     * @return 结果
     */
    public int recoverOrderArticleQuantityByAsnId(Long asnId);

    /**
     * 通过ASNID删除ASN物料信息
     *
     * @param asnId ASNID
     * @return 结果
     */
    public int deleteTblAsnArticleByAsnId(Long asnId);

    /**
     * 查询订单物料剩余
     *
     * @param orderAsnQuantity TblOrderAsnQuantity
     * @return 订单物料剩余集合
     */
    public List<TblOrderAsnQuantity> selectTblOrderAsnQuantityList(TblOrderAsnQuantity orderAsnQuantity);

    /**
     * 查询ASN详细信息用于打印（包含供应商名称和物料名称）
     *
     * @param asn ASN
     * @return ASN
     */
    @DataScope(supplierAlias = "a")
    public TblAsn selectTblAsnForPrint(TblAsn asn);
}
