package com.datalink.datamanage.service.impl;

import com.datalink.common.DataConstants;
import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.NoticeReply;
import com.datalink.datamanage.domain.TblAttachment;
import com.datalink.datamanage.domain.TblNotice;
import com.datalink.datamanage.domain.TblNoticeReply;
import com.datalink.datamanage.mapper.TblAttachmentMapper;
import com.datalink.datamanage.mapper.TblNoticeMapper;
import com.datalink.datamanage.service.ITblNoticeService;
import com.datalink.system.domain.SysConfig;
import com.datalink.system.service.ISysConfigService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 公告Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-05-02
 */
@Service
public class TblNoticeServiceImpl implements ITblNoticeService
{
    @Autowired
    private TblNoticeMapper tblNoticeMapper;

    @Resource
    private ISysConfigService configService;

    @Resource
    private TblAttachmentMapper attachmentMapper;

    /**
     * 查询公告
     *
     * @param noticeId 公告ID
     * @return 公告
     */
    @Override
    public TblNotice selectTblNoticeById(Long noticeId)
    {
        return tblNoticeMapper.selectTblNoticeById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param tblNotice 公告
     * @return 公告
     */
    @Override
    public List<TblNotice> selectTblNoticeList(TblNotice tblNotice)
    {
        return tblNoticeMapper.selectTblNoticeList(tblNotice);
    }

    /**
     * 新增公告
     *
     * @param tblNotice 公告
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblNotice(TblNotice tblNotice)
    {
        if(StringUtils.isEmpty(tblNotice.getNoticeCode())){
            genNoticeCode(tblNotice);
        }
        tblNotice.setCreateTime(DateUtils.getNowDate());
        int rows = tblNoticeMapper.insertTblNotice(tblNotice);
        insertTblNoticeReply(tblNotice);
        addAttachments(tblNotice);
        return rows;
    }

    /**
     * 修改公告
     *
     * @param tblNotice 公告
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblNotice(TblNotice tblNotice)
    {
        tblNotice.setUpdateTime(DateUtils.getNowDate());
        tblNoticeMapper.deleteTblNoticeReplyByNoticeId(tblNotice.getNoticeId());
        attachmentMapper.deleteTblAttachmentByParentAndType(tblNotice.getNoticeId(), DataConstants.NOTICE_TYPE);
        insertTblNoticeReply(tblNotice);
        addAttachments(tblNotice);
        return tblNoticeMapper.updateTblNotice(tblNotice);
    }

    /**
     * 批量删除公告
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblNoticeByIds(Long[] noticeIds)
    {
        tblNoticeMapper.deleteTblNoticeReplyByNoticeIds(noticeIds);
        for (Long noticeId: noticeIds){
            attachmentMapper.deleteTblAttachmentByParentAndType(noticeId, DataConstants.NOTICE_TYPE);
        }
        return tblNoticeMapper.deleteTblNoticeByIds(noticeIds);
    }

    /**
     * 删除公告信息
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    public int deleteTblNoticeById(Long noticeId)
    {
        tblNoticeMapper.deleteTblNoticeReplyByNoticeId(noticeId);
        attachmentMapper.deleteTblAttachmentByParentAndType(noticeId, DataConstants.NOTICE_TYPE);
        return tblNoticeMapper.deleteTblNoticeById(noticeId);
    }

    @Override
    public List<NoticeReply> selectToSendNoticeReplyList() {
        return tblNoticeMapper.selectToSendNoticeReplyList();
    }

    @Override
    public int updateTblNoticeReply(TblNoticeReply tblNoticeReply) {
        attachmentMapper.deleteTblAttachmentByParentAndType(tblNoticeReply.getReplyId(), DataConstants.NOTICE_REPLY_TYPE);
        addAttachments(tblNoticeReply);
        return tblNoticeMapper.updateTblNoticeReply(tblNoticeReply);
    }

    @Override
    public List<TblNoticeReply> selectTblNoticeReplyList(TblNoticeReply tblNoticeReply) {
        return tblNoticeMapper.selectTblNoticeReplyList(tblNoticeReply);
    }

    @Override
    public List<NoticeReply> selectNoticeReplyList(NoticeReply noticeReply) {
        return tblNoticeMapper.selectNoticeReplyList(noticeReply);
    }

    @Override
    public int updateTblNoticeReplyByNoticeCodeMap(Map<String, Object> map) {
        if (map.containsKey("replyAttachmentMap")){

            TblNoticeReply reply = tblNoticeMapper.selectTblNoticeReplyByNoticeCodeAndSuppCode(map.get("noticeCode").toString(), map.get("suppCode").toString());
            if (null != reply ){
                Map<String, String> attachmentMap = (Map<String, String>) map.get("replyAttachmentMap");
                List<TblAttachment> attachmentList = Lists.newArrayList();
                for (Map.Entry<String, String> entry : attachmentMap.entrySet()){
                    TblAttachment attachment = new TblAttachment();
                    attachment.setParent(reply.getReplyId());
                    attachment.setType(DataConstants.NOTICE_REPLY_TYPE);
                    attachment.setName(entry.getKey());
                    attachment.setUrl(entry.getValue());
                    attachmentList.add(attachment);
                }
                if (!attachmentList.isEmpty()){
                    attachmentMapper.batchTblAttachments(attachmentList);
                }
            }
        }

        return tblNoticeMapper.updateTblNoticeReplyByNoticeCodeMap(map);
    }

    /**
     * 新增公告回复信息
     *
     * @param tblNotice 公告对象
     */
    public void insertTblNoticeReply(TblNotice tblNotice)
    {
        List<TblNoticeReply> tblNoticeReplyList = tblNotice.getTblNoticeReplyList();
        Long noticeId = tblNotice.getNoticeId();
        if (StringUtils.isNotNull(tblNoticeReplyList))
        {
            List<TblNoticeReply> list = new ArrayList<TblNoticeReply>();
            for (TblNoticeReply tblNoticeReply : tblNoticeReplyList)
            {
                if (null == tblNoticeReply.getCreateTime()){
                    tblNoticeReply.setCreateTime(new Date());
                }
                tblNoticeReply.setNoticeId(noticeId);
                list.add(tblNoticeReply);
            }
            if (list.size() > 0)
            {
                tblNoticeMapper.batchTblNoticeReply(list);
            }
        }
    }

    private void addAttachments(TblNotice tblNotice){
        List<TblAttachment> attachmentList = tblNotice.getAttachmentList();
        Long noticeId = tblNotice.getNoticeId();
        if (StringUtils.isNotNull(attachmentList)){
            List<TblAttachment> list = new ArrayList<>();
            for (TblAttachment attachment:attachmentList){
                attachment.setParent(noticeId);
                attachment.setType(DataConstants.NOTICE_TYPE);
                if (null != attachment.getCreateTime()){
                    attachment.setCreateTime(new Date());
                }
                list.add(attachment);
            }
            if (list.size()>0){
                attachmentMapper.batchTblAttachments(list);
            }
        }

    }

    private void addAttachments(TblNoticeReply tblNoticeReply){
        List<TblAttachment> attachmentList = tblNoticeReply.getReplyAttachmentList();
        Long replyId = tblNoticeReply.getReplyId();
        if (StringUtils.isNotNull(attachmentList)){
            List<TblAttachment> list = new ArrayList<>();
            for (TblAttachment attachment:attachmentList){
                attachment.setParent(replyId);
                attachment.setType(DataConstants.NOTICE_REPLY_TYPE);
                if (null != attachment.getCreateTime()){
                    attachment.setCreateTime(new Date());
                }
                list.add(attachment);
            }
            if (list.size()>0){
                attachmentMapper.batchTblAttachments(list);
            }
        }

    }

    private void genNoticeCode(TblNotice notice){
        String noticeCode = "NOTICE"+notice.getCompCode();
        StringBuilder currentCode = new StringBuilder(configService.selectConfigByKey(DataConstants.NOTICE_CODE_PARAM));
        while(currentCode.length() < 5){
            currentCode.insert(0, "0");
        }
        noticeCode = noticeCode+currentCode;
        SysConfig update = new SysConfig();
        update.setConfigKey(DataConstants.NOTICE_CODE_PARAM);
        update.setConfigValue(String.valueOf(Integer.parseInt(currentCode.toString())+1));
        configService.updateConfigValueByKey(update);
        notice.setNoticeCode(noticeCode);
    }

}
