package com.datalink.datamanage.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.datalink.common.core.domain.BaseEntity;

/**
 * 看板SV对象 tbl_sap_kanban_sv
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
public class TblSapKanbanSv extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonProperty("id")
    private Long id;

    /** ZFILE_NO - Ｆ／＃('02') */
    @JsonProperty("ZFILE_NO")
    private String fileNo;

    /** ZRESERVE1 - 予備 */
    @JsonProperty("ZRESERVE1")
    private String reserve1;

    /** ZDMND_CD - 要元 */
    @JsonProperty("ZDMND_CD")
    private String demandCode;

    /** ZISSU_NO_ID - 発行ＮＯ */
    @JsonProperty("ZISSU_NO_ID")
    private String issueNoId;

    /** ZDELI_INSTR_YY - 納入指示日年月日時分 */
    @JsonProperty("ZDELI_INSTR_YY")
    private String deliveryInstructionYy;

    /** ZMAKER - メーカー */
    @JsonProperty("ZMAKER")
    private String maker;

    /** ZDEPO - デポ */
    @JsonProperty("ZDEPO")
    private String depot;

    /** ZCLASS - 区分 */
    @JsonProperty("ZCLASS")
    private String classification;

    /** ZDELI_LOC - 納入場所 */
    @JsonProperty("ZDELI_LOC")
    private String deliveryLocation;

    /** ZCUST_PARTS_NO - 部品番号 */
    @JsonProperty("ZCUST_PARTS_NO")
    private String customerPartsNo;

    /** ZCUR_PRC_CLS - 当工順 */
    @JsonProperty("ZCUR_PRC_CLS")
    private String currentProcessClass;

    /** ZPARTS_NAM - 部品名称 */
    @JsonProperty("ZPARTS_NAM")
    private String partsName;

    /** ZDEL_INSTR_NO - 納入指示数 */
    @JsonProperty("ZDEL_INSTR_NO")
    private String deliveryInstructionNumber;

    /** ZSUPP_LOC - 供給場所 */
    @JsonProperty("ZSUPP_LOC")
    private String supplyLocation;

    /** ZSNEP - ＳＮＥＰ（荷姿用） */
    @JsonProperty("ZSNEP")
    private String snep;

    /** ZSAFE_FLG - 重保マーク */
    @JsonProperty("ZSAFE_FLG")
    private String safetyFlag;

    /** ZLBL_CNT - 容器数・ラベル枚数 */
    @JsonProperty("ZLBL_CNT")
    private String labelCount;

    /** ZMANAGE_PIC - 管理担当 */
    @JsonProperty("ZMANAGE_PIC")
    private String managementPic;

    /** ZISSU_RSN - 発行理由 */
    @JsonProperty("ZISSU_RSN")
    private String issueReason;

    /** ZVIP_FLG - 特便マーク */
    @JsonProperty("ZVIP_FLG")
    private String vipFlag;

    /** ZPCKG_CD - 荷姿コード */
    @JsonProperty("ZPCKG_CD")
    private String packageCode;

    /** ZIO_CLS - 内外区分 */
    @JsonProperty("ZIO_CLS")
    private String internalExternalClass;

    /** ZDELI_INSTR_CNT - 納入指示端数 */
    @JsonProperty("ZDELI_INSTR_CNT")
    private String deliveryInstructionCount;

    /** ZDELI_METHD - 納入方式 */
    @JsonProperty("ZDELI_METHD")
    private String deliveryMethod;

    /** ZORD_CLS - 発注区分 */
    @JsonProperty("ZORD_CLS")
    private String orderClass;

    /** ZRESERVE2 - 予備 */
    @JsonProperty("ZRESERVE2")
    private String reserve2;

    /** ZDELAY_ANTI_MAK_SYM - 遅防法メーカーシンボル */
    @JsonProperty("ZDELAY_ANTI_MAK_SYM")
    private String delayPreventionMakerSymbol;

    /** ZRESERVE3 - 予備 */
    @JsonProperty("ZRESERVE3")
    private String reserve3;

    /** ZRESERVE4 - 予備 */
    @JsonProperty("ZRESERVE4")
    private String reserve4;

    /** ZRESERVE5 - 予備 */
    @JsonProperty("ZRESERVE5")
    private String reserve5;

    /** ZLBL_SIZE - ラベルサイズ */
    @JsonProperty("ZLBL_SIZE")
    private String labelSize;

    /** ZLBL_SPEC - ラベル仕様 */
    @JsonProperty("ZLBL_SPEC")
    private String labelSpecification;

    /** ZORDER_TYPE - 発注種類 */
    @JsonProperty("ZORDER_TYPE")
    private String orderType;

    /** ZCHK_SPEC - 検査仕様 */
    @JsonProperty("ZCHK_SPEC")
    private String checkSpecification;

    /** ZLOCATION - ロケーション */
    @JsonProperty("ZLOCATION")
    private String location;

    /** ZPACK_CLS - 包装区分 */
    @JsonProperty("ZPACK_CLS")
    private String packClass;

    /** ZTRADE_SPEC - 商品化仕様 */
    @JsonProperty("ZTRADE_SPEC")
    private String tradeSpecification;

    /** ZOUTPACK_MAT_CD - 外装資材コード */
    @JsonProperty("ZOUTPACK_MAT_CD")
    private String outpackMaterialCode;

    /** ZOUTPACK_MAT_PROC_CLS - 外装資材調達区分 */
    @JsonProperty("ZOUTPACK_MAT_PROC_CLS")
    private String outpackMaterialProcClass;

    /** ZBIND_PACK_CD - 結束資材コード */
    @JsonProperty("ZBIND_PACK_CD")
    private String bindPackCode;

    /** ZBIND_UN - 結束単位 */
    @JsonProperty("ZBIND_UN")
    private String bindUnit;

    /** ZPACK_UN - 包装単位 */
    @JsonProperty("ZPACK_UN")
    private String packUnit;

    /** ZCONSIST_PACK_CLS - 一貫バレチ区分 */
    @JsonProperty("ZCONSIST_PACK_CLS")
    private String consistPackClass;

    /** ZELEMENTS_FINSH_CLS - 諸元設定完了区分 */
    @JsonProperty("ZELEMENTS_FINSH_CLS")
    private String elementsFinishClass;

    /** ZIN_PACK_CD1 - 内装資材１＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD1")
    private String inpackCode1;

    /** ZIN_PACK_PROC_CLS1 - 内装資材１＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS1")
    private String inpackProcClass1;

    /** ZIN_PACK_NEC_NB1 - 内装資材１＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB1")
    private String inpackNecessaryNumber1;

    /** ZIN_PACK_CD2 - 内装資材２＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD2")
    private String inpackCode2;

    /** ZIN_PACK_PROC_CLS2 - 内装資材２＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS2")
    private String inpackProcClass2;

    /** ZIN_PACK_NEC_NB2 - 内装資材２＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB2")
    private String inpackNecessaryNumber2;

    /** ZIN_PACK_CD3 - 内装資材3＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD3")
    private String inpackCode3;

    /** ZIN_PACK_PROC_CLS3 - 内装資材3＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS3")
    private String inpackProcClass3;

    /** ZIN_PACK_NEC_NB3 - 内装資材3＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB3")
    private String inpackNecessaryNumber3;

    /** ZIN_PACK_CD4 - 内装資材4＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD4")
    private String inpackCode4;

    /** ZIN_PACK_PROC_CLS4 - 内装資材4＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS4")
    private String inpackProcClass4;

    /** ZIN_PACK_NEC_NB4 - 内装資材4＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB4")
    private String inpackNecessaryNumber4;

    /** ZIN_PACK_CD5 - 内装資材5＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD5")
    private String inpackCode5;

    /** ZIN_PACK_PROC_CLS5 - 内装資材5＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS5")
    private String inpackProcClass5;

    /** ZIN_PACK_NEC_NB5 - 内装資材5＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB5")
    private String inpackNecessaryNumber5;

    /** ZIN_PACK_CD6 - 内装資材6＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD6")
    private String inpackCode6;

    /** ZIN_PACK_PROC_CLS6 - 内装資材6＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS6")
    private String inpackProcClass6;

    /** ZIN_PACK_NEC_NB6 - 内装資材6＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB6")
    private String inpackNecessaryNumber6;

    /** ZIN_PACK_CD7 - 内装資材7＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD7")
    private String inpackCode7;

    /** ZIN_PACK_PROC_CLS7 - 内装資材7＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS7")
    private String inpackProcClass7;

    /** ZIN_PACK_NEC_NB7 - 内装資材7＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB7")
    private String inpackNecessaryNumber7;

    /** ZIN_PACK_CD8 - 内装資材8＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD8")
    private String inpackCode8;

    /** ZIN_PACK_PROC_CLS8 - 内装資材8＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS8")
    private String inpackProcClass8;

    /** ZIN_PACK_NEC_NB8 - 内装資材8＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB8")
    private String inpackNecessaryNumber8;

    /** ZIN_PACK_CD9 - 内装資材9＿内装資材コード */
    @JsonProperty("ZIN_PACK_CD9")
    private String inpackCode9;

    /** ZIN_PACK_PROC_CLS9 - 内装資材9＿調達区分 */
    @JsonProperty("ZIN_PACK_PROC_CLS9")
    private String inpackProcClass9;

    /** ZIN_PACK_NEC_NB9 - 内装資材9＿必要数 */
    @JsonProperty("ZIN_PACK_NEC_NB9")
    private String inpackNecessaryNumber9;

    /** ZMOD_MAT - モジュール資材 */
    @JsonProperty("ZMOD_MAT")
    private String moduleMaterial;

    /** ZMOD_UN - モジュール単位 */
    @JsonProperty("ZMOD_UN")
    private String moduleUnit;

    /** ZMOD_CLS - モジュール区分 */
    @JsonProperty("ZMOD_CLS")
    private String moduleClass;

    /** ZORIG_PART_NO - オリジナル部品番号 */
    @JsonProperty("ZORIG_PART_NO")
    private String originalPartNo;

    /** ZPART_MANAGE_CLS - 部管区分 */
    @JsonProperty("ZPART_MANAGE_CLS")
    private String partManageClass;

    /** ZNEWCAR_PGM_CD - 新車展開コード */
    @JsonProperty("ZNEWCAR_PGM_CD")
    private String newcarProgramCode;

    /** ZORIG_ISSU_NO - オリジナル発行番号 */
    @JsonProperty("ZORIG_ISSU_NO")
    private String originalIssueNo;

    /** ZDESIGN_CHG_NO - 図面変番 */
    @JsonProperty("ZDESIGN_CHG_NO")
    private String designChangeNo;

    /** ZNORM_CNTR_CD - 標準容器コード */
    @JsonProperty("ZNORM_CNTR_CD")
    private String normalContainerCode;

    /** ZBO_FCST_DD - Ｂ／Ｏ予測日 */
    @JsonProperty("ZBO_FCST_DD")
    private String boForecastDate;

    /** ZBO_SYM - Ｂ／Ｏシンボル */
    @JsonProperty("ZBO_SYM")
    private String boSymbol;

    /** ZTRUSTEE_ID - 受託先識別ＮＯ */
    @JsonProperty("ZTRUSTEE_ID")
    private String trusteeId;

    /** ZDELI_DOC_TYPE - 納品書種類 */
    @JsonProperty("ZDELI_DOC_TYPE")
    private String deliveryDocType;

    /** ZPCKG_LBL_TYPE - 荷姿ラベル種別 */
    @JsonProperty("ZPCKG_LBL_TYPE")
    private String packageLabelType;

    /** ZLBL_ISSU_ORG - ラベル発行単位 */
    @JsonProperty("ZLBL_ISSU_ORG")
    private String labelIssueOrg;

    /** ZDELI_CNTR - 納入容器 */
    @JsonProperty("ZDELI_CNTR")
    private String deliveryContainer;

    /** ZUL_CNTR_CNT - Ｕ／Ｌ集約箱数 */
    @JsonProperty("ZUL_CNTR_CNT")
    private String ulContainerCount;

    /** ZITEM_SUBNO1 - アイテム枝番1 */
    @JsonProperty("ZITEM_SUBNO1")
    private String itemSubno1;

    /** ZITEM_SUBNO2 - アイテム枝番2 */
    @JsonProperty("ZITEM_SUBNO2")
    private String itemSubno2;

    /** ZITEM_SUBNO3 - アイテム枝番3 */
    @JsonProperty("ZITEM_SUBNO3")
    private String itemSubno3;

    /** ZITEM_SUBNO4 - アイテム枝番4 */
    @JsonProperty("ZITEM_SUBNO4")
    private String itemSubno4;

    /** ZITEM_SUBNO5 - アイテム枝番5 */
    @JsonProperty("ZITEM_SUBNO5")
    private String itemSubno5;

    /** ZITEM_SUBNO6 - アイテム枝番6 */
    @JsonProperty("ZITEM_SUBNO6")
    private String itemSubno6;

    /** ZITEM_SUBNO7 - アイテム枝番7 */
    @JsonProperty("ZITEM_SUBNO7")
    private String itemSubno7;

    /** ZITEM_SUBNO8 - アイテム枝番8 */
    @JsonProperty("ZITEM_SUBNO8")
    private String itemSubno8;

    /** ZITEM_SUBNO9 - アイテム枝番9 */
    @JsonProperty("ZITEM_SUBNO9")
    private String itemSubno9;

    /** ZITEM_SUBNO10 - アイテム枝番10 */
    @JsonProperty("ZITEM_SUBNO10")
    private String itemSubno10;

    /** ZITEM_SUBNO11 - アイテム枝番11 */
    @JsonProperty("ZITEM_SUBNO11")
    private String itemSubno11;

    /** ZITEM_SUBNO12 - アイテム枝番12 */
    @JsonProperty("ZITEM_SUBNO12")
    private String itemSubno12;

    /** ZITEM_SUBNO13 - アイテム枝番13 */
    @JsonProperty("ZITEM_SUBNO13")
    private String itemSubno13;

    /** ZITEM_SUBNO14 - アイテム枝番14 */
    @JsonProperty("ZITEM_SUBNO14")
    private String itemSubno14;

    /** ZITEM_SUBNO15 - アイテム枝番15 */
    @JsonProperty("ZITEM_SUBNO15")
    private String itemSubno15;

    /** ZITEM_SUBNO16 - アイテム枝番16 */
    @JsonProperty("ZITEM_SUBNO16")
    private String itemSubno16;

    /** ZITEM_SUBNO17 - アイテム枝番17 */
    @JsonProperty("ZITEM_SUBNO17")
    private String itemSubno17;

    /** ZITEM_SUBNO18 - アイテム枝番18 */
    @JsonProperty("ZITEM_SUBNO18")
    private String itemSubno18;

    /** ZITEM_SUBNO19 - アイテム枝番19 */
    @JsonProperty("ZITEM_SUBNO19")
    private String itemSubno19;

    /** ZITEM_SUBNO20 - アイテム枝番20 */
    @JsonProperty("ZITEM_SUBNO20")
    private String itemSubno20;

    /** ZBARCODE_INF - バーコード情報 */
    @JsonProperty("ZBARCODE_INF")
    private String barcodeInfo;

    /** ZFREE_COL1 - フリーカラム1 */
    @JsonProperty("ZFREE_COL1")
    private String freeColumn1;

    /** ZFREE_COL2 - フリーカラム2 */
    @JsonProperty("ZFREE_COL2")
    private String freeColumn2;

    /** ZFREE_COL3 - フリーカラム3 */
    @JsonProperty("ZFREE_COL3")
    private String freeColumn3;

    /** ZFREE_COL4 - フリーカラム4 */
    @JsonProperty("ZFREE_COL4")
    private String freeColumn4;

    /** ZYARD - ヤード */
    @JsonProperty("ZYARD")
    private String yard;

    /** ZENLARG_MARK1 - 拡大表示マーク１ */
    @JsonProperty("ZENLARG_MARK1")
    private String enlargeMark1;

    /** ZENLARG_MARK2 - 拡大表示マーク２ */
    @JsonProperty("ZENLARG_MARK2")
    private String enlargeMark2;

    /** ZNODIFF_DELI_CLS - 異数納入不可区分 */
    @JsonProperty("ZNODIFF_DELI_CLS")
    private String noDiffDeliveryClass;

    /** ZUL_SNP - Ｕ／Ｌ　ＳＮＰ */
    @JsonProperty("ZUL_SNP")
    private String ulSnp;

    /** ZDEL_TICKET_NO - 納品チケットＮＯ */
    @JsonProperty("ZDEL_TICKET_NO")
    private String deliveryTicketNo;

    /** ZWD_CLS - 引取区分 */
    @JsonProperty("ZWD_CLS")
    private String withdrawalClass;

    /** ZPART_PRICE - 部品価格 */
    @JsonProperty("ZPART_PRICE")
    private String partPrice;

    /** ZPROT_SYM - 試作シンボル */
    @JsonProperty("ZPROT_SYM")
    private String prototypeSymbol;

    /** ZRESEND_SYM - 再送シンボル */
    @JsonProperty("ZRESEND_SYM")
    private String resendSymbol;

    /** ZORD_YMD - 発注年月日 */
    @JsonProperty("ZORD_YMD")
    private String orderDate;

    /** ZRESERVE6 - 予備 */
    @JsonProperty("ZRESERVE6")
    private String reserve6;

    /** ZRESERVE7 - 予備 */
    @JsonProperty("ZRESERVE7")
    private String reserve7;

    /** ZLBL_TERM_NO - ラベル端末ＮＯ */
    @JsonProperty("ZLBL_TERM_NO")
    private String labelTerminalNo;

    /** ZLIST_TERM_NO - リスト端末ＮＯ */
    @JsonProperty("ZLIST_TERM_NO")
    private String listTerminalNo;

    /** ZMAKER_PIC - メーカー担当 */
    @JsonProperty("ZMAKER_PIC")
    private String makerPic;

    /** ZMAKER_SNP - メーカーＳＮＰ */
    @JsonProperty("ZMAKER_SNP")
    private String makerSnp;

    /** ZRESEND_DEPO - 再配信デポ */
    @JsonProperty("ZRESEND_DEPO")
    private String resendDepot;

    /** ZPARTS_NO - 部品番号 */
    @JsonProperty("ZPARTS_NO")
    private String partsNo;

    /** ZPRDCT_CLS - 製品区分 */
    @JsonProperty("ZPRDCT_CLS")
    private String productClass;

    /** ZPRD_METHD - 生産方式 */
    @JsonProperty("ZPRD_METHD")
    private String productionMethod;

    /** ZDELI_METHD2 - 納入方式2 */
    @JsonProperty("ZDELI_METHD2")
    private String deliveryMethod2;

    /** ZFROM_PRC - ＦＲＯＭ工順 */
    @JsonProperty("ZFROM_PRC")
    private String fromProcess;

    /** ZFROM_W_AREA - ＦＲＯＭ工程 */
    @JsonProperty("ZFROM_W_AREA")
    private String fromWorkArea;

    /** ZSHIP_PIC - 出荷担当 */
    @JsonProperty("ZSHIP_PIC")
    private String shippingPic;

    /** ZPARTS_NO_ID_CD - 部品番号識別コード */
    @JsonProperty("ZPARTS_NO_ID_CD")
    private String partsNoIdCode;

    /** ZINSTR_YMD - 指示年月日 */
    @JsonProperty("ZINSTR_YMD")
    private String instructionDate;

    /** ZINSTR_HM - 指示時分 */
    @JsonProperty("ZINSTR_HM")
    private String instructionTime;

    /** ZINSTR_NO - 指示ＮＯ */
    @JsonProperty("ZINSTR_NO")
    private String instructionNo;

    /** ZPRD_HANDL_METHD - 生産対応方式 */
    @JsonProperty("ZPRD_HANDL_METHD")
    private String productionHandlingMethod;

    /** ZPLANT_CD - 工場コード */
    @JsonProperty("ZPLANT_CD")
    private String plantCode;

    /** ZSHIP_LOC - 出荷場所 */
    @JsonProperty("ZSHIP_LOC")
    private String shippingLocation;

    /** ZSHIP_PORT - 出荷ポート */
    @JsonProperty("ZSHIP_PORT")
    private String shippingPort;

    /** ZRESERVE8 - 予備 */
    @JsonProperty("ZRESERVE8")
    private String reserve8;

    /** ZLBL_ISSU_ASG - ラベル発行指定 */
    @JsonProperty("ZLBL_ISSU_ASG")
    private String labelIssueAssignment;

    /** ZDIV_DEPO - 振分デポ */
    @JsonProperty("ZDIV_DEPO")
    private String divisionDepot;

    /** ZCUST_CD - 得意先コード */
    @JsonProperty("ZCUST_CD")
    private String customerCode;

    /** ZDMND_CD2 - 要求元コード */
    @JsonProperty("ZDMND_CD2")
    private String demandCode2;

    /** ZSHIP_LOC_CD2 - 出荷場所コード２ */
    @JsonProperty("ZSHIP_LOC_CD2")
    private String shippingLocationCode2;

    /** ZMOV_LT - 移動リードタイム */
    @JsonProperty("ZMOV_LT")
    private String movementLeadTime;

    /** ZRE_HANDL_FLG - 再処理フラグ */
    @JsonProperty("ZRE_HANDL_FLG")
    private String rehandlingFlag;

    /** ZNODATA_FLG - ノーマスターフラグ */
    @JsonProperty("ZNODATA_FLG")
    private String noMasterFlag;

    /** ZRESERVE９ - 予備 */
    @JsonProperty("ZRESERVE９")
    private String reserve9;

    /** ZDIV_DEPO2 - 振分デポ */
    @JsonProperty("ZDIV_DEPO2")
    private String divisionDepot2;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFileNo(String fileNo)
    {
        this.fileNo = fileNo;
    }

    public String getFileNo()
    {
        return fileNo;
    }
    public void setReserve1(String reserve1)
    {
        this.reserve1 = reserve1;
    }

    public String getReserve1()
    {
        return reserve1;
    }
    public void setDemandCode(String demandCode)
    {
        this.demandCode = demandCode;
    }

    public String getDemandCode()
    {
        return demandCode;
    }
    public void setIssueNoId(String issueNoId)
    {
        this.issueNoId = issueNoId;
    }

    public String getIssueNoId()
    {
        return issueNoId;
    }
    public void setDeliveryInstructionYy(String deliveryInstructionYy)
    {
        this.deliveryInstructionYy = deliveryInstructionYy;
    }

    public String getDeliveryInstructionYy()
    {
        return deliveryInstructionYy;
    }
    public void setMaker(String maker)
    {
        this.maker = maker;
    }

    public String getMaker()
    {
        return maker;
    }
    public void setDepot(String depot)
    {
        this.depot = depot;
    }

    public String getDepot()
    {
        return depot;
    }
    public void setClassification(String classification)
    {
        this.classification = classification;
    }

    public String getClassification()
    {
        return classification;
    }
    public void setDeliveryLocation(String deliveryLocation)
    {
        this.deliveryLocation = deliveryLocation;
    }

    public String getDeliveryLocation()
    {
        return deliveryLocation;
    }
    public void setCustomerPartsNo(String customerPartsNo)
    {
        this.customerPartsNo = customerPartsNo;
    }

    public String getCustomerPartsNo()
    {
        return customerPartsNo;
    }
    public void setCurrentProcessClass(String currentProcessClass)
    {
        this.currentProcessClass = currentProcessClass;
    }

    public String getCurrentProcessClass()
    {
        return currentProcessClass;
    }
    public void setPartsName(String partsName)
    {
        this.partsName = partsName;
    }

    public String getPartsName()
    {
        return partsName;
    }
    public void setDeliveryInstructionNumber(String deliveryInstructionNumber)
    {
        this.deliveryInstructionNumber = deliveryInstructionNumber;
    }

    public String getDeliveryInstructionNumber()
    {
        return deliveryInstructionNumber;
    }
    public void setSupplyLocation(String supplyLocation)
    {
        this.supplyLocation = supplyLocation;
    }

    public String getSupplyLocation()
    {
        return supplyLocation;
    }
    public void setSnep(String snep)
    {
        this.snep = snep;
    }

    public String getSnep()
    {
        return snep;
    }
    public void setSafetyFlag(String safetyFlag)
    {
        this.safetyFlag = safetyFlag;
    }

    public String getSafetyFlag()
    {
        return safetyFlag;
    }
    public void setLabelCount(String labelCount)
    {
        this.labelCount = labelCount;
    }

    public String getLabelCount()
    {
        return labelCount;
    }
    public void setManagementPic(String managementPic)
    {
        this.managementPic = managementPic;
    }

    public String getManagementPic()
    {
        return managementPic;
    }
    public void setIssueReason(String issueReason)
    {
        this.issueReason = issueReason;
    }

    public String getIssueReason()
    {
        return issueReason;
    }
    public void setVipFlag(String vipFlag)
    {
        this.vipFlag = vipFlag;
    }

    public String getVipFlag()
    {
        return vipFlag;
    }
    public void setPackageCode(String packageCode)
    {
        this.packageCode = packageCode;
    }

    public String getPackageCode()
    {
        return packageCode;
    }
    public void setInternalExternalClass(String internalExternalClass)
    {
        this.internalExternalClass = internalExternalClass;
    }

    public String getInternalExternalClass()
    {
        return internalExternalClass;
    }
    public void setDeliveryInstructionCount(String deliveryInstructionCount)
    {
        this.deliveryInstructionCount = deliveryInstructionCount;
    }

    public String getDeliveryInstructionCount()
    {
        return deliveryInstructionCount;
    }
    public void setDeliveryMethod(String deliveryMethod)
    {
        this.deliveryMethod = deliveryMethod;
    }

    public String getDeliveryMethod()
    {
        return deliveryMethod;
    }
    public void setOrderClass(String orderClass)
    {
        this.orderClass = orderClass;
    }

    public String getOrderClass()
    {
        return orderClass;
    }
    public void setReserve2(String reserve2)
    {
        this.reserve2 = reserve2;
    }

    public String getReserve2()
    {
        return reserve2;
    }
    public void setDelayPreventionMakerSymbol(String delayPreventionMakerSymbol)
    {
        this.delayPreventionMakerSymbol = delayPreventionMakerSymbol;
    }

    public String getDelayPreventionMakerSymbol()
    {
        return delayPreventionMakerSymbol;
    }
    public void setReserve3(String reserve3)
    {
        this.reserve3 = reserve3;
    }

    public String getReserve3()
    {
        return reserve3;
    }
    public void setReserve4(String reserve4)
    {
        this.reserve4 = reserve4;
    }

    public String getReserve4()
    {
        return reserve4;
    }
    public void setReserve5(String reserve5)
    {
        this.reserve5 = reserve5;
    }

    public String getReserve5()
    {
        return reserve5;
    }
    public void setLabelSize(String labelSize)
    {
        this.labelSize = labelSize;
    }

    public String getLabelSize()
    {
        return labelSize;
    }
    public void setLabelSpecification(String labelSpecification)
    {
        this.labelSpecification = labelSpecification;
    }

    public String getLabelSpecification()
    {
        return labelSpecification;
    }
    public void setOrderType(String orderType)
    {
        this.orderType = orderType;
    }

    public String getOrderType()
    {
        return orderType;
    }
    public void setCheckSpecification(String checkSpecification)
    {
        this.checkSpecification = checkSpecification;
    }

    public String getCheckSpecification()
    {
        return checkSpecification;
    }
    public void setLocation(String location)
    {
        this.location = location;
    }

    public String getLocation()
    {
        return location;
    }
    public void setPackClass(String packClass)
    {
        this.packClass = packClass;
    }

    public String getPackClass()
    {
        return packClass;
    }
    public void setTradeSpecification(String tradeSpecification)
    {
        this.tradeSpecification = tradeSpecification;
    }

    public String getTradeSpecification()
    {
        return tradeSpecification;
    }
    public void setOutpackMaterialCode(String outpackMaterialCode)
    {
        this.outpackMaterialCode = outpackMaterialCode;
    }

    public String getOutpackMaterialCode()
    {
        return outpackMaterialCode;
    }
    public void setOutpackMaterialProcClass(String outpackMaterialProcClass)
    {
        this.outpackMaterialProcClass = outpackMaterialProcClass;
    }

    public String getOutpackMaterialProcClass()
    {
        return outpackMaterialProcClass;
    }
    public void setBindPackCode(String bindPackCode)
    {
        this.bindPackCode = bindPackCode;
    }

    public String getBindPackCode()
    {
        return bindPackCode;
    }
    public void setBindUnit(String bindUnit)
    {
        this.bindUnit = bindUnit;
    }

    public String getBindUnit()
    {
        return bindUnit;
    }
    public void setPackUnit(String packUnit)
    {
        this.packUnit = packUnit;
    }

    public String getPackUnit()
    {
        return packUnit;
    }
    public void setConsistPackClass(String consistPackClass)
    {
        this.consistPackClass = consistPackClass;
    }

    public String getConsistPackClass()
    {
        return consistPackClass;
    }
    public void setElementsFinishClass(String elementsFinishClass)
    {
        this.elementsFinishClass = elementsFinishClass;
    }

    public String getElementsFinishClass()
    {
        return elementsFinishClass;
    }
    public void setInpackCode1(String inpackCode1)
    {
        this.inpackCode1 = inpackCode1;
    }

    public String getInpackCode1()
    {
        return inpackCode1;
    }
    public void setInpackProcClass1(String inpackProcClass1)
    {
        this.inpackProcClass1 = inpackProcClass1;
    }

    public String getInpackProcClass1()
    {
        return inpackProcClass1;
    }
    public void setInpackNecessaryNumber1(String inpackNecessaryNumber1)
    {
        this.inpackNecessaryNumber1 = inpackNecessaryNumber1;
    }

    public String getInpackNecessaryNumber1()
    {
        return inpackNecessaryNumber1;
    }
    public void setInpackCode2(String inpackCode2)
    {
        this.inpackCode2 = inpackCode2;
    }

    public String getInpackCode2()
    {
        return inpackCode2;
    }
    public void setInpackProcClass2(String inpackProcClass2)
    {
        this.inpackProcClass2 = inpackProcClass2;
    }

    public String getInpackProcClass2()
    {
        return inpackProcClass2;
    }
    public void setInpackNecessaryNumber2(String inpackNecessaryNumber2)
    {
        this.inpackNecessaryNumber2 = inpackNecessaryNumber2;
    }

    public String getInpackNecessaryNumber2()
    {
        return inpackNecessaryNumber2;
    }
    public void setInpackCode3(String inpackCode3)
    {
        this.inpackCode3 = inpackCode3;
    }

    public String getInpackCode3()
    {
        return inpackCode3;
    }
    public void setInpackProcClass3(String inpackProcClass3)
    {
        this.inpackProcClass3 = inpackProcClass3;
    }

    public String getInpackProcClass3()
    {
        return inpackProcClass3;
    }
    public void setInpackNecessaryNumber3(String inpackNecessaryNumber3)
    {
        this.inpackNecessaryNumber3 = inpackNecessaryNumber3;
    }

    public String getInpackNecessaryNumber3()
    {
        return inpackNecessaryNumber3;
    }
    public void setInpackCode4(String inpackCode4)
    {
        this.inpackCode4 = inpackCode4;
    }

    public String getInpackCode4()
    {
        return inpackCode4;
    }
    public void setInpackProcClass4(String inpackProcClass4)
    {
        this.inpackProcClass4 = inpackProcClass4;
    }

    public String getInpackProcClass4()
    {
        return inpackProcClass4;
    }
    public void setInpackNecessaryNumber4(String inpackNecessaryNumber4)
    {
        this.inpackNecessaryNumber4 = inpackNecessaryNumber4;
    }

    public String getInpackNecessaryNumber4()
    {
        return inpackNecessaryNumber4;
    }
    public void setInpackCode5(String inpackCode5)
    {
        this.inpackCode5 = inpackCode5;
    }

    public String getInpackCode5()
    {
        return inpackCode5;
    }
    public void setInpackProcClass5(String inpackProcClass5)
    {
        this.inpackProcClass5 = inpackProcClass5;
    }

    public String getInpackProcClass5()
    {
        return inpackProcClass5;
    }
    public void setInpackNecessaryNumber5(String inpackNecessaryNumber5)
    {
        this.inpackNecessaryNumber5 = inpackNecessaryNumber5;
    }

    public String getInpackNecessaryNumber5()
    {
        return inpackNecessaryNumber5;
    }
    public void setInpackCode6(String inpackCode6)
    {
        this.inpackCode6 = inpackCode6;
    }

    public String getInpackCode6()
    {
        return inpackCode6;
    }
    public void setInpackProcClass6(String inpackProcClass6)
    {
        this.inpackProcClass6 = inpackProcClass6;
    }

    public String getInpackProcClass6()
    {
        return inpackProcClass6;
    }
    public void setInpackNecessaryNumber6(String inpackNecessaryNumber6)
    {
        this.inpackNecessaryNumber6 = inpackNecessaryNumber6;
    }

    public String getInpackNecessaryNumber6()
    {
        return inpackNecessaryNumber6;
    }
    public void setInpackCode7(String inpackCode7)
    {
        this.inpackCode7 = inpackCode7;
    }

    public String getInpackCode7()
    {
        return inpackCode7;
    }
    public void setInpackProcClass7(String inpackProcClass7)
    {
        this.inpackProcClass7 = inpackProcClass7;
    }

    public String getInpackProcClass7()
    {
        return inpackProcClass7;
    }
    public void setInpackNecessaryNumber7(String inpackNecessaryNumber7)
    {
        this.inpackNecessaryNumber7 = inpackNecessaryNumber7;
    }

    public String getInpackNecessaryNumber7()
    {
        return inpackNecessaryNumber7;
    }
    public void setInpackCode8(String inpackCode8)
    {
        this.inpackCode8 = inpackCode8;
    }

    public String getInpackCode8()
    {
        return inpackCode8;
    }
    public void setInpackProcClass8(String inpackProcClass8)
    {
        this.inpackProcClass8 = inpackProcClass8;
    }

    public String getInpackProcClass8()
    {
        return inpackProcClass8;
    }
    public void setInpackNecessaryNumber8(String inpackNecessaryNumber8)
    {
        this.inpackNecessaryNumber8 = inpackNecessaryNumber8;
    }

    public String getInpackNecessaryNumber8()
    {
        return inpackNecessaryNumber8;
    }
    public void setInpackCode9(String inpackCode9)
    {
        this.inpackCode9 = inpackCode9;
    }

    public String getInpackCode9()
    {
        return inpackCode9;
    }
    public void setInpackProcClass9(String inpackProcClass9)
    {
        this.inpackProcClass9 = inpackProcClass9;
    }

    public String getInpackProcClass9()
    {
        return inpackProcClass9;
    }
    public void setInpackNecessaryNumber9(String inpackNecessaryNumber9)
    {
        this.inpackNecessaryNumber9 = inpackNecessaryNumber9;
    }

    public String getInpackNecessaryNumber9()
    {
        return inpackNecessaryNumber9;
    }
    public void setModuleMaterial(String moduleMaterial)
    {
        this.moduleMaterial = moduleMaterial;
    }

    public String getModuleMaterial()
    {
        return moduleMaterial;
    }
    public void setModuleUnit(String moduleUnit)
    {
        this.moduleUnit = moduleUnit;
    }

    public String getModuleUnit()
    {
        return moduleUnit;
    }
    public void setModuleClass(String moduleClass)
    {
        this.moduleClass = moduleClass;
    }

    public String getModuleClass()
    {
        return moduleClass;
    }
    public void setOriginalPartNo(String originalPartNo)
    {
        this.originalPartNo = originalPartNo;
    }

    public String getOriginalPartNo()
    {
        return originalPartNo;
    }
    public void setPartManageClass(String partManageClass)
    {
        this.partManageClass = partManageClass;
    }

    public String getPartManageClass()
    {
        return partManageClass;
    }
    public void setNewcarProgramCode(String newcarProgramCode)
    {
        this.newcarProgramCode = newcarProgramCode;
    }

    public String getNewcarProgramCode()
    {
        return newcarProgramCode;
    }
    public void setOriginalIssueNo(String originalIssueNo)
    {
        this.originalIssueNo = originalIssueNo;
    }

    public String getOriginalIssueNo()
    {
        return originalIssueNo;
    }
    public void setDesignChangeNo(String designChangeNo)
    {
        this.designChangeNo = designChangeNo;
    }

    public String getDesignChangeNo()
    {
        return designChangeNo;
    }
    public void setNormalContainerCode(String normalContainerCode)
    {
        this.normalContainerCode = normalContainerCode;
    }

    public String getNormalContainerCode()
    {
        return normalContainerCode;
    }
    public void setBoForecastDate(String boForecastDate)
    {
        this.boForecastDate = boForecastDate;
    }

    public String getBoForecastDate()
    {
        return boForecastDate;
    }
    public void setBoSymbol(String boSymbol)
    {
        this.boSymbol = boSymbol;
    }

    public String getBoSymbol()
    {
        return boSymbol;
    }
    public void setTrusteeId(String trusteeId)
    {
        this.trusteeId = trusteeId;
    }

    public String getTrusteeId()
    {
        return trusteeId;
    }
    public void setDeliveryDocType(String deliveryDocType)
    {
        this.deliveryDocType = deliveryDocType;
    }

    public String getDeliveryDocType()
    {
        return deliveryDocType;
    }
    public void setPackageLabelType(String packageLabelType)
    {
        this.packageLabelType = packageLabelType;
    }

    public String getPackageLabelType()
    {
        return packageLabelType;
    }
    public void setLabelIssueOrg(String labelIssueOrg)
    {
        this.labelIssueOrg = labelIssueOrg;
    }

    public String getLabelIssueOrg()
    {
        return labelIssueOrg;
    }
    public void setDeliveryContainer(String deliveryContainer)
    {
        this.deliveryContainer = deliveryContainer;
    }

    public String getDeliveryContainer()
    {
        return deliveryContainer;
    }
    public void setUlContainerCount(String ulContainerCount)
    {
        this.ulContainerCount = ulContainerCount;
    }

    public String getUlContainerCount()
    {
        return ulContainerCount;
    }
    public void setItemSubno1(String itemSubno1)
    {
        this.itemSubno1 = itemSubno1;
    }

    public String getItemSubno1()
    {
        return itemSubno1;
    }
    public void setItemSubno2(String itemSubno2)
    {
        this.itemSubno2 = itemSubno2;
    }

    public String getItemSubno2()
    {
        return itemSubno2;
    }
    public void setItemSubno3(String itemSubno3)
    {
        this.itemSubno3 = itemSubno3;
    }

    public String getItemSubno3()
    {
        return itemSubno3;
    }
    public void setItemSubno4(String itemSubno4)
    {
        this.itemSubno4 = itemSubno4;
    }

    public String getItemSubno4()
    {
        return itemSubno4;
    }
    public void setItemSubno5(String itemSubno5)
    {
        this.itemSubno5 = itemSubno5;
    }

    public String getItemSubno5()
    {
        return itemSubno5;
    }
    public void setItemSubno6(String itemSubno6)
    {
        this.itemSubno6 = itemSubno6;
    }

    public String getItemSubno6()
    {
        return itemSubno6;
    }
    public void setItemSubno7(String itemSubno7)
    {
        this.itemSubno7 = itemSubno7;
    }

    public String getItemSubno7()
    {
        return itemSubno7;
    }
    public void setItemSubno8(String itemSubno8)
    {
        this.itemSubno8 = itemSubno8;
    }

    public String getItemSubno8()
    {
        return itemSubno8;
    }
    public void setItemSubno9(String itemSubno9)
    {
        this.itemSubno9 = itemSubno9;
    }

    public String getItemSubno9()
    {
        return itemSubno9;
    }
    public void setItemSubno10(String itemSubno10)
    {
        this.itemSubno10 = itemSubno10;
    }

    public String getItemSubno10()
    {
        return itemSubno10;
    }
    public void setItemSubno11(String itemSubno11)
    {
        this.itemSubno11 = itemSubno11;
    }

    public String getItemSubno11()
    {
        return itemSubno11;
    }
    public void setItemSubno12(String itemSubno12)
    {
        this.itemSubno12 = itemSubno12;
    }

    public String getItemSubno12()
    {
        return itemSubno12;
    }
    public void setItemSubno13(String itemSubno13)
    {
        this.itemSubno13 = itemSubno13;
    }

    public String getItemSubno13()
    {
        return itemSubno13;
    }
    public void setItemSubno14(String itemSubno14)
    {
        this.itemSubno14 = itemSubno14;
    }

    public String getItemSubno14()
    {
        return itemSubno14;
    }
    public void setItemSubno15(String itemSubno15)
    {
        this.itemSubno15 = itemSubno15;
    }

    public String getItemSubno15()
    {
        return itemSubno15;
    }
    public void setItemSubno16(String itemSubno16)
    {
        this.itemSubno16 = itemSubno16;
    }

    public String getItemSubno16()
    {
        return itemSubno16;
    }
    public void setItemSubno17(String itemSubno17)
    {
        this.itemSubno17 = itemSubno17;
    }

    public String getItemSubno17()
    {
        return itemSubno17;
    }
    public void setItemSubno18(String itemSubno18)
    {
        this.itemSubno18 = itemSubno18;
    }

    public String getItemSubno18()
    {
        return itemSubno18;
    }
    public void setItemSubno19(String itemSubno19)
    {
        this.itemSubno19 = itemSubno19;
    }

    public String getItemSubno19()
    {
        return itemSubno19;
    }
    public void setItemSubno20(String itemSubno20)
    {
        this.itemSubno20 = itemSubno20;
    }

    public String getItemSubno20()
    {
        return itemSubno20;
    }
    public void setBarcodeInfo(String barcodeInfo)
    {
        this.barcodeInfo = barcodeInfo;
    }

    public String getBarcodeInfo()
    {
        return barcodeInfo;
    }
    public void setFreeColumn1(String freeColumn1)
    {
        this.freeColumn1 = freeColumn1;
    }

    public String getFreeColumn1()
    {
        return freeColumn1;
    }
    public void setFreeColumn2(String freeColumn2)
    {
        this.freeColumn2 = freeColumn2;
    }

    public String getFreeColumn2()
    {
        return freeColumn2;
    }
    public void setFreeColumn3(String freeColumn3)
    {
        this.freeColumn3 = freeColumn3;
    }

    public String getFreeColumn3()
    {
        return freeColumn3;
    }
    public void setFreeColumn4(String freeColumn4)
    {
        this.freeColumn4 = freeColumn4;
    }

    public String getFreeColumn4()
    {
        return freeColumn4;
    }
    public void setYard(String yard)
    {
        this.yard = yard;
    }

    public String getYard()
    {
        return yard;
    }
    public void setEnlargeMark1(String enlargeMark1)
    {
        this.enlargeMark1 = enlargeMark1;
    }

    public String getEnlargeMark1()
    {
        return enlargeMark1;
    }
    public void setEnlargeMark2(String enlargeMark2)
    {
        this.enlargeMark2 = enlargeMark2;
    }

    public String getEnlargeMark2()
    {
        return enlargeMark2;
    }
    public void setNoDiffDeliveryClass(String noDiffDeliveryClass)
    {
        this.noDiffDeliveryClass = noDiffDeliveryClass;
    }

    public String getNoDiffDeliveryClass()
    {
        return noDiffDeliveryClass;
    }
    public void setUlSnp(String ulSnp)
    {
        this.ulSnp = ulSnp;
    }

    public String getUlSnp()
    {
        return ulSnp;
    }
    public void setDeliveryTicketNo(String deliveryTicketNo)
    {
        this.deliveryTicketNo = deliveryTicketNo;
    }

    public String getDeliveryTicketNo()
    {
        return deliveryTicketNo;
    }
    public void setWithdrawalClass(String withdrawalClass)
    {
        this.withdrawalClass = withdrawalClass;
    }

    public String getWithdrawalClass()
    {
        return withdrawalClass;
    }
    public void setPartPrice(String partPrice)
    {
        this.partPrice = partPrice;
    }

    public String getPartPrice()
    {
        return partPrice;
    }
    public void setPrototypeSymbol(String prototypeSymbol)
    {
        this.prototypeSymbol = prototypeSymbol;
    }

    public String getPrototypeSymbol()
    {
        return prototypeSymbol;
    }
    public void setResendSymbol(String resendSymbol)
    {
        this.resendSymbol = resendSymbol;
    }

    public String getResendSymbol()
    {
        return resendSymbol;
    }
    public void setOrderDate(String orderDate)
    {
        this.orderDate = orderDate;
    }

    public String getOrderDate()
    {
        return orderDate;
    }
    public void setReserve6(String reserve6)
    {
        this.reserve6 = reserve6;
    }

    public String getReserve6()
    {
        return reserve6;
    }
    public void setReserve7(String reserve7)
    {
        this.reserve7 = reserve7;
    }

    public String getReserve7()
    {
        return reserve7;
    }
    public void setLabelTerminalNo(String labelTerminalNo)
    {
        this.labelTerminalNo = labelTerminalNo;
    }

    public String getLabelTerminalNo()
    {
        return labelTerminalNo;
    }
    public void setListTerminalNo(String listTerminalNo)
    {
        this.listTerminalNo = listTerminalNo;
    }

    public String getListTerminalNo()
    {
        return listTerminalNo;
    }
    public void setMakerPic(String makerPic)
    {
        this.makerPic = makerPic;
    }

    public String getMakerPic()
    {
        return makerPic;
    }
    public void setMakerSnp(String makerSnp)
    {
        this.makerSnp = makerSnp;
    }

    public String getMakerSnp()
    {
        return makerSnp;
    }
    public void setResendDepot(String resendDepot)
    {
        this.resendDepot = resendDepot;
    }

    public String getResendDepot()
    {
        return resendDepot;
    }
    public void setPartsNo(String partsNo)
    {
        this.partsNo = partsNo;
    }

    public String getPartsNo()
    {
        return partsNo;
    }
    public void setProductClass(String productClass)
    {
        this.productClass = productClass;
    }

    public String getProductClass()
    {
        return productClass;
    }
    public void setProductionMethod(String productionMethod)
    {
        this.productionMethod = productionMethod;
    }

    public String getProductionMethod()
    {
        return productionMethod;
    }
    public void setDeliveryMethod2(String deliveryMethod2)
    {
        this.deliveryMethod2 = deliveryMethod2;
    }

    public String getDeliveryMethod2()
    {
        return deliveryMethod2;
    }
    public void setFromProcess(String fromProcess)
    {
        this.fromProcess = fromProcess;
    }

    public String getFromProcess()
    {
        return fromProcess;
    }
    public void setFromWorkArea(String fromWorkArea)
    {
        this.fromWorkArea = fromWorkArea;
    }

    public String getFromWorkArea()
    {
        return fromWorkArea;
    }
    public void setShippingPic(String shippingPic)
    {
        this.shippingPic = shippingPic;
    }

    public String getShippingPic()
    {
        return shippingPic;
    }
    public void setPartsNoIdCode(String partsNoIdCode)
    {
        this.partsNoIdCode = partsNoIdCode;
    }

    public String getPartsNoIdCode()
    {
        return partsNoIdCode;
    }
    public void setInstructionDate(String instructionDate)
    {
        this.instructionDate = instructionDate;
    }

    public String getInstructionDate()
    {
        return instructionDate;
    }
    public void setInstructionTime(String instructionTime)
    {
        this.instructionTime = instructionTime;
    }

    public String getInstructionTime()
    {
        return instructionTime;
    }
    public void setInstructionNo(String instructionNo)
    {
        this.instructionNo = instructionNo;
    }

    public String getInstructionNo()
    {
        return instructionNo;
    }
    public void setProductionHandlingMethod(String productionHandlingMethod)
    {
        this.productionHandlingMethod = productionHandlingMethod;
    }

    public String getProductionHandlingMethod()
    {
        return productionHandlingMethod;
    }
    public void setPlantCode(String plantCode)
    {
        this.plantCode = plantCode;
    }

    public String getPlantCode()
    {
        return plantCode;
    }
    public void setShippingLocation(String shippingLocation)
    {
        this.shippingLocation = shippingLocation;
    }

    public String getShippingLocation()
    {
        return shippingLocation;
    }
    public void setShippingPort(String shippingPort)
    {
        this.shippingPort = shippingPort;
    }

    public String getShippingPort()
    {
        return shippingPort;
    }
    public void setReserve8(String reserve8)
    {
        this.reserve8 = reserve8;
    }

    public String getReserve8()
    {
        return reserve8;
    }
    public void setLabelIssueAssignment(String labelIssueAssignment)
    {
        this.labelIssueAssignment = labelIssueAssignment;
    }

    public String getLabelIssueAssignment()
    {
        return labelIssueAssignment;
    }
    public void setDivisionDepot(String divisionDepot)
    {
        this.divisionDepot = divisionDepot;
    }

    public String getDivisionDepot()
    {
        return divisionDepot;
    }
    public void setCustomerCode(String customerCode)
    {
        this.customerCode = customerCode;
    }

    public String getCustomerCode()
    {
        return customerCode;
    }
    public void setDemandCode2(String demandCode2)
    {
        this.demandCode2 = demandCode2;
    }

    public String getDemandCode2()
    {
        return demandCode2;
    }
    public void setShippingLocationCode2(String shippingLocationCode2)
    {
        this.shippingLocationCode2 = shippingLocationCode2;
    }

    public String getShippingLocationCode2()
    {
        return shippingLocationCode2;
    }
    public void setMovementLeadTime(String movementLeadTime)
    {
        this.movementLeadTime = movementLeadTime;
    }

    public String getMovementLeadTime()
    {
        return movementLeadTime;
    }
    public void setRehandlingFlag(String rehandlingFlag)
    {
        this.rehandlingFlag = rehandlingFlag;
    }

    public String getRehandlingFlag()
    {
        return rehandlingFlag;
    }
    public void setNoMasterFlag(String noMasterFlag)
    {
        this.noMasterFlag = noMasterFlag;
    }

    public String getNoMasterFlag()
    {
        return noMasterFlag;
    }
    public void setReserve9(String reserve9)
    {
        this.reserve9 = reserve9;
    }

    public String getReserve9()
    {
        return reserve9;
    }
    public void setDivisionDepot2(String divisionDepot2)
    {
        this.divisionDepot2 = divisionDepot2;
    }

    public String getDivisionDepot2()
    {
        return divisionDepot2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fileNo", getFileNo())
            .append("reserve1", getReserve1())
            .append("demandCode", getDemandCode())
            .append("issueNoId", getIssueNoId())
            .append("deliveryInstructionYy", getDeliveryInstructionYy())
            .append("maker", getMaker())
            .append("depot", getDepot())
            .append("classification", getClassification())
            .append("deliveryLocation", getDeliveryLocation())
            .append("customerPartsNo", getCustomerPartsNo())
            .append("currentProcessClass", getCurrentProcessClass())
            .append("partsName", getPartsName())
            .append("deliveryInstructionNumber", getDeliveryInstructionNumber())
            .append("supplyLocation", getSupplyLocation())
            .append("snep", getSnep())
            .append("safetyFlag", getSafetyFlag())
            .append("labelCount", getLabelCount())
            .append("managementPic", getManagementPic())
            .append("issueReason", getIssueReason())
            .append("vipFlag", getVipFlag())
            .append("packageCode", getPackageCode())
            .append("internalExternalClass", getInternalExternalClass())
            .append("deliveryInstructionCount", getDeliveryInstructionCount())
            .append("deliveryMethod", getDeliveryMethod())
            .append("orderClass", getOrderClass())
            .append("reserve2", getReserve2())
            .append("delayPreventionMakerSymbol", getDelayPreventionMakerSymbol())
            .append("reserve3", getReserve3())
            .append("reserve4", getReserve4())
            .append("reserve5", getReserve5())
            .append("labelSize", getLabelSize())
            .append("labelSpecification", getLabelSpecification())
            .append("orderType", getOrderType())
            .append("checkSpecification", getCheckSpecification())
            .append("location", getLocation())
            .append("packClass", getPackClass())
            .append("tradeSpecification", getTradeSpecification())
            .append("outpackMaterialCode", getOutpackMaterialCode())
            .append("outpackMaterialProcClass", getOutpackMaterialProcClass())
            .append("bindPackCode", getBindPackCode())
            .append("bindUnit", getBindUnit())
            .append("packUnit", getPackUnit())
            .append("consistPackClass", getConsistPackClass())
            .append("elementsFinishClass", getElementsFinishClass())
            .append("inpackCode1", getInpackCode1())
            .append("inpackProcClass1", getInpackProcClass1())
            .append("inpackNecessaryNumber1", getInpackNecessaryNumber1())
            .append("inpackCode2", getInpackCode2())
            .append("inpackProcClass2", getInpackProcClass2())
            .append("inpackNecessaryNumber2", getInpackNecessaryNumber2())
            .append("inpackCode3", getInpackCode3())
            .append("inpackProcClass3", getInpackProcClass3())
            .append("inpackNecessaryNumber3", getInpackNecessaryNumber3())
            .append("inpackCode4", getInpackCode4())
            .append("inpackProcClass4", getInpackProcClass4())
            .append("inpackNecessaryNumber4", getInpackNecessaryNumber4())
            .append("inpackCode5", getInpackCode5())
            .append("inpackProcClass5", getInpackProcClass5())
            .append("inpackNecessaryNumber5", getInpackNecessaryNumber5())
            .append("inpackCode6", getInpackCode6())
            .append("inpackProcClass6", getInpackProcClass6())
            .append("inpackNecessaryNumber6", getInpackNecessaryNumber6())
            .append("inpackCode7", getInpackCode7())
            .append("inpackProcClass7", getInpackProcClass7())
            .append("inpackNecessaryNumber7", getInpackNecessaryNumber7())
            .append("inpackCode8", getInpackCode8())
            .append("inpackProcClass8", getInpackProcClass8())
            .append("inpackNecessaryNumber8", getInpackNecessaryNumber8())
            .append("inpackCode9", getInpackCode9())
            .append("inpackProcClass9", getInpackProcClass9())
            .append("inpackNecessaryNumber9", getInpackNecessaryNumber9())
            .append("moduleMaterial", getModuleMaterial())
            .append("moduleUnit", getModuleUnit())
            .append("moduleClass", getModuleClass())
            .append("originalPartNo", getOriginalPartNo())
            .append("partManageClass", getPartManageClass())
            .append("newcarProgramCode", getNewcarProgramCode())
            .append("originalIssueNo", getOriginalIssueNo())
            .append("designChangeNo", getDesignChangeNo())
            .append("normalContainerCode", getNormalContainerCode())
            .append("boForecastDate", getBoForecastDate())
            .append("boSymbol", getBoSymbol())
            .append("trusteeId", getTrusteeId())
            .append("deliveryDocType", getDeliveryDocType())
            .append("packageLabelType", getPackageLabelType())
            .append("labelIssueOrg", getLabelIssueOrg())
            .append("deliveryContainer", getDeliveryContainer())
            .append("ulContainerCount", getUlContainerCount())
            .append("itemSubno1", getItemSubno1())
            .append("itemSubno2", getItemSubno2())
            .append("itemSubno3", getItemSubno3())
            .append("itemSubno4", getItemSubno4())
            .append("itemSubno5", getItemSubno5())
            .append("itemSubno6", getItemSubno6())
            .append("itemSubno7", getItemSubno7())
            .append("itemSubno8", getItemSubno8())
            .append("itemSubno9", getItemSubno9())
            .append("itemSubno10", getItemSubno10())
            .append("itemSubno11", getItemSubno11())
            .append("itemSubno12", getItemSubno12())
            .append("itemSubno13", getItemSubno13())
            .append("itemSubno14", getItemSubno14())
            .append("itemSubno15", getItemSubno15())
            .append("itemSubno16", getItemSubno16())
            .append("itemSubno17", getItemSubno17())
            .append("itemSubno18", getItemSubno18())
            .append("itemSubno19", getItemSubno19())
            .append("itemSubno20", getItemSubno20())
            .append("barcodeInfo", getBarcodeInfo())
            .append("freeColumn1", getFreeColumn1())
            .append("freeColumn2", getFreeColumn2())
            .append("freeColumn3", getFreeColumn3())
            .append("freeColumn4", getFreeColumn4())
            .append("yard", getYard())
            .append("enlargeMark1", getEnlargeMark1())
            .append("enlargeMark2", getEnlargeMark2())
            .append("noDiffDeliveryClass", getNoDiffDeliveryClass())
            .append("ulSnp", getUlSnp())
            .append("deliveryTicketNo", getDeliveryTicketNo())
            .append("withdrawalClass", getWithdrawalClass())
            .append("partPrice", getPartPrice())
            .append("prototypeSymbol", getPrototypeSymbol())
            .append("resendSymbol", getResendSymbol())
            .append("orderDate", getOrderDate())
            .append("reserve6", getReserve6())
            .append("reserve7", getReserve7())
            .append("labelTerminalNo", getLabelTerminalNo())
            .append("listTerminalNo", getListTerminalNo())
            .append("makerPic", getMakerPic())
            .append("makerSnp", getMakerSnp())
            .append("resendDepot", getResendDepot())
            .append("partsNo", getPartsNo())
            .append("productClass", getProductClass())
            .append("productionMethod", getProductionMethod())
            .append("deliveryMethod2", getDeliveryMethod2())
            .append("fromProcess", getFromProcess())
            .append("fromWorkArea", getFromWorkArea())
            .append("shippingPic", getShippingPic())
            .append("partsNoIdCode", getPartsNoIdCode())
            .append("instructionDate", getInstructionDate())
            .append("instructionTime", getInstructionTime())
            .append("instructionNo", getInstructionNo())
            .append("productionHandlingMethod", getProductionHandlingMethod())
            .append("plantCode", getPlantCode())
            .append("shippingLocation", getShippingLocation())
            .append("shippingPort", getShippingPort())
            .append("reserve8", getReserve8())
            .append("labelIssueAssignment", getLabelIssueAssignment())
            .append("divisionDepot", getDivisionDepot())
            .append("customerCode", getCustomerCode())
            .append("demandCode2", getDemandCode2())
            .append("shippingLocationCode2", getShippingLocationCode2())
            .append("movementLeadTime", getMovementLeadTime())
            .append("rehandlingFlag", getRehandlingFlag())
            .append("noMasterFlag", getNoMasterFlag())
            .append("reserve9", getReserve9())
            .append("divisionDepot2", getDivisionDepot2())
            .toString();
    }
}
