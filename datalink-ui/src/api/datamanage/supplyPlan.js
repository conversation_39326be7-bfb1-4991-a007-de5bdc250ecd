// src/api/supplyPlan.js
import request from '@/utils/request'

// 获取表单下拉框数据
export function fetchFormOptions () {
  return new Promise(resolve => {
    const mockData = {
      code: 200,
      message: 'success',
      data: {
        factoryOptions: [
          { label: '工厂1', value: 'factory1' },
          { label: '工厂2', value: 'factory2' },
          { label: '工厂3', value: 'factory3' }
        ],
        depotOptions: [
          { label: '仓库A', value: 'depotA' },
          { label: '仓库B', value: 'depotB' }
        ],
        clientOptions: [
          { label: '客户A', value: 'clientA' },
          { label: '客户B', value: 'clientB' }
        ]
      }
    }
    resolve(mockData)
  })
}

export function printTxt(data) {
  return request({
    url: '/api/sapDeliveryPlan/downloadDeliveryPlanTxt',
    method: 'get',
    params: data
  })
}
// 查询表单数据
export function querySupplyPlan (data) {
  return request({
    url: '/api/sapDeliveryPlan/list',
    method: 'get',
    params: data
  })
  // return new Promise((resolve) => {
  //   const mockData = {
  //     code: 200,
  //     message: "success",
  //     data: {
  //       basicInfo: {
  //         client: "客户A",
  //         depot: "仓库A",
  //         factory: "工厂1",
  //         partNumber: "12345",
  //         monthlyRequirement: {
  //           NMonth: 500,
  //           N1Month: 600,
  //           N2Month: 700,
  //         },
  //         monthlyPlan: "月度计划数据",
  //         dailyPlan: "日计划数据",
  //         deliveryMethod: "方式1",
  //         supplyType: "区分1",
  //         supplyMethod: "方式2",
  //         deliveryLocation: "配送地1",
  //         basicUnit: "单元",
  //         orderUnit: "订单单元",
  //         allocationLot: "批次",
  //         currentYearMonth: "2023/10",
  //         clientInventory: 1000,
  //         confirmedDate: "2023-10-01",
  //         updatedDateTime: "2023-10-01 10:30",
  //       },
  //       planData: {
  //         lastMonthRemaining: 100,
  //         monthlyTotal: 500,
  //         week1: 120,
  //         week2: 100,
  //         week3: 80,
  //         week4: 150,
  //         week5: 50,
  //       },
  //       weeksData: [
  //         {
  //           days: [
  //             { date: "10/01", plan: "30", actual: "25" },
  //             { date: "10/02", plan: "40", actual: "35" },
  //             { date: "10/03", plan: "50", actual: "45" },
  //             { date: "10/04", plan: "60", actual: "55" },
  //             { date: "10/05", plan: "70", actual: "65" },
  //             { date: "10/06", plan: "80", actual: "75" },
  //             { date: "10/07", plan: "90", actual: "85" },
  //           ],
  //         },
  //         {
  //           days: [
  //             { date: "10/08", plan: "100", actual: "95" },
  //             { date: "10/09", plan: "110", actual: "105" },
  //             { date: "10/10", plan: "120", actual: "115" },
  //             { date: "10/11", plan: "130", actual: "125" },
  //             { date: "10/12", plan: "140", actual: "135" },
  //             { date: "10/13", plan: "150", actual: "145" },
  //             { date: "10/14", plan: "160", actual: "155" },
  //           ],
  //         },
  //       ],
  //     }
  //   };
  //   resolve(mockData);
  // });
}
